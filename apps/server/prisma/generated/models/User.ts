
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `User` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model User
 * 
 */
export type UserModel = runtime.Types.Result.DefaultSelection<Prisma.$UserPayload>

export type AggregateUser = {
  _count: UserCountAggregateOutputType | null
  _min: UserMinAggregateOutputType | null
  _max: UserMaxAggregateOutputType | null
}

export type UserMinAggregateOutputType = {
  id: string | null
  name: string | null
  email: string | null
  emailVerified: boolean | null
  image: string | null
  role: $Enums.UserRole | null
  isActive: boolean | null
  invitedBy: string | null
  invitedAt: Date | null
  lastLoginAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type UserMaxAggregateOutputType = {
  id: string | null
  name: string | null
  email: string | null
  emailVerified: boolean | null
  image: string | null
  role: $Enums.UserRole | null
  isActive: boolean | null
  invitedBy: string | null
  invitedAt: Date | null
  lastLoginAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type UserCountAggregateOutputType = {
  id: number
  name: number
  email: number
  emailVerified: number
  image: number
  role: number
  isActive: number
  invitedBy: number
  invitedAt: number
  lastLoginAt: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type UserMinAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  role?: true
  isActive?: true
  invitedBy?: true
  invitedAt?: true
  lastLoginAt?: true
  createdAt?: true
  updatedAt?: true
}

export type UserMaxAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  role?: true
  isActive?: true
  invitedBy?: true
  invitedAt?: true
  lastLoginAt?: true
  createdAt?: true
  updatedAt?: true
}

export type UserCountAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  role?: true
  isActive?: true
  invitedBy?: true
  invitedAt?: true
  lastLoginAt?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type UserAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which User to aggregate.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Users
  **/
  _count?: true | UserCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: UserMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: UserMaxAggregateInputType
}

export type GetUserAggregateType<T extends UserAggregateArgs> = {
      [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser[P]>
    : Prisma.GetScalarType<T[P], AggregateUser[P]>
}




export type UserGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UserWhereInput
  orderBy?: Prisma.UserOrderByWithAggregationInput | Prisma.UserOrderByWithAggregationInput[]
  by: Prisma.UserScalarFieldEnum[] | Prisma.UserScalarFieldEnum
  having?: Prisma.UserScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: UserCountAggregateInputType | true
  _min?: UserMinAggregateInputType
  _max?: UserMaxAggregateInputType
}

export type UserGroupByOutputType = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image: string | null
  role: $Enums.UserRole
  isActive: boolean
  invitedBy: string | null
  invitedAt: Date | null
  lastLoginAt: Date | null
  createdAt: Date
  updatedAt: Date
  _count: UserCountAggregateOutputType | null
  _min: UserMinAggregateOutputType | null
  _max: UserMaxAggregateOutputType | null
}

type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<UserGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], UserGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], UserGroupByOutputType[P]>
      }
    >
  >



export type UserWhereInput = {
  AND?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  OR?: Prisma.UserWhereInput[]
  NOT?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  id?: Prisma.StringFilter<"User"> | string
  name?: Prisma.StringFilter<"User"> | string
  email?: Prisma.StringFilter<"User"> | string
  emailVerified?: Prisma.BoolFilter<"User"> | boolean
  image?: Prisma.StringNullableFilter<"User"> | string | null
  role?: Prisma.EnumUserRoleFilter<"User"> | $Enums.UserRole
  isActive?: Prisma.BoolFilter<"User"> | boolean
  invitedBy?: Prisma.StringNullableFilter<"User"> | string | null
  invitedAt?: Prisma.DateTimeNullableFilter<"User"> | Date | string | null
  lastLoginAt?: Prisma.DateTimeNullableFilter<"User"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"User"> | Date | string
  sessions?: Prisma.SessionListRelationFilter
  accounts?: Prisma.AccountListRelationFilter
  createdLicenses?: Prisma.LicenseListRelationFilter
  sentInvitations?: Prisma.UserInvitationListRelationFilter
  receivedInvitations?: Prisma.UserInvitationListRelationFilter
  auditLogsAsActor?: Prisma.AuditLogListRelationFilter
  auditLogsAsTarget?: Prisma.AuditLogListRelationFilter
  processedRefunds?: Prisma.RefundRequestListRelationFilter
  assignedTickets?: Prisma.SupportTicketListRelationFilter
  supportMessages?: Prisma.SupportMessageListRelationFilter
}

export type UserOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  role?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  invitedBy?: Prisma.SortOrderInput | Prisma.SortOrder
  invitedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  lastLoginAt?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  sessions?: Prisma.SessionOrderByRelationAggregateInput
  accounts?: Prisma.AccountOrderByRelationAggregateInput
  createdLicenses?: Prisma.LicenseOrderByRelationAggregateInput
  sentInvitations?: Prisma.UserInvitationOrderByRelationAggregateInput
  receivedInvitations?: Prisma.UserInvitationOrderByRelationAggregateInput
  auditLogsAsActor?: Prisma.AuditLogOrderByRelationAggregateInput
  auditLogsAsTarget?: Prisma.AuditLogOrderByRelationAggregateInput
  processedRefunds?: Prisma.RefundRequestOrderByRelationAggregateInput
  assignedTickets?: Prisma.SupportTicketOrderByRelationAggregateInput
  supportMessages?: Prisma.SupportMessageOrderByRelationAggregateInput
}

export type UserWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  email?: string
  AND?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  OR?: Prisma.UserWhereInput[]
  NOT?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  name?: Prisma.StringFilter<"User"> | string
  emailVerified?: Prisma.BoolFilter<"User"> | boolean
  image?: Prisma.StringNullableFilter<"User"> | string | null
  role?: Prisma.EnumUserRoleFilter<"User"> | $Enums.UserRole
  isActive?: Prisma.BoolFilter<"User"> | boolean
  invitedBy?: Prisma.StringNullableFilter<"User"> | string | null
  invitedAt?: Prisma.DateTimeNullableFilter<"User"> | Date | string | null
  lastLoginAt?: Prisma.DateTimeNullableFilter<"User"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"User"> | Date | string
  sessions?: Prisma.SessionListRelationFilter
  accounts?: Prisma.AccountListRelationFilter
  createdLicenses?: Prisma.LicenseListRelationFilter
  sentInvitations?: Prisma.UserInvitationListRelationFilter
  receivedInvitations?: Prisma.UserInvitationListRelationFilter
  auditLogsAsActor?: Prisma.AuditLogListRelationFilter
  auditLogsAsTarget?: Prisma.AuditLogListRelationFilter
  processedRefunds?: Prisma.RefundRequestListRelationFilter
  assignedTickets?: Prisma.SupportTicketListRelationFilter
  supportMessages?: Prisma.SupportMessageListRelationFilter
}, "id" | "email">

export type UserOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  role?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  invitedBy?: Prisma.SortOrderInput | Prisma.SortOrder
  invitedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  lastLoginAt?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.UserCountOrderByAggregateInput
  _max?: Prisma.UserMaxOrderByAggregateInput
  _min?: Prisma.UserMinOrderByAggregateInput
}

export type UserScalarWhereWithAggregatesInput = {
  AND?: Prisma.UserScalarWhereWithAggregatesInput | Prisma.UserScalarWhereWithAggregatesInput[]
  OR?: Prisma.UserScalarWhereWithAggregatesInput[]
  NOT?: Prisma.UserScalarWhereWithAggregatesInput | Prisma.UserScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"User"> | string
  name?: Prisma.StringWithAggregatesFilter<"User"> | string
  email?: Prisma.StringWithAggregatesFilter<"User"> | string
  emailVerified?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  image?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  role?: Prisma.EnumUserRoleWithAggregatesFilter<"User"> | $Enums.UserRole
  isActive?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  invitedBy?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  invitedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"User"> | Date | string | null
  lastLoginAt?: Prisma.DateTimeNullableWithAggregatesFilter<"User"> | Date | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"User"> | Date | string
}

export type UserCreateInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput
}

export type UserUncheckedCreateInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput
}

export type UserUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUpdateManyWithoutAuthorUserNestedInput
}

export type UserUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput
}

export type UserCreateManyInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type UserUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UserUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UserCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  role?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  invitedBy?: Prisma.SortOrder
  invitedAt?: Prisma.SortOrder
  lastLoginAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  role?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  invitedBy?: Prisma.SortOrder
  invitedAt?: Prisma.SortOrder
  lastLoginAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  role?: Prisma.SortOrder
  isActive?: Prisma.SortOrder
  invitedBy?: Prisma.SortOrder
  invitedAt?: Prisma.SortOrder
  lastLoginAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserScalarRelationFilter = {
  is?: Prisma.UserWhereInput
  isNot?: Prisma.UserWhereInput
}

export type UserNullableScalarRelationFilter = {
  is?: Prisma.UserWhereInput | null
  isNot?: Prisma.UserWhereInput | null
}

export type StringFieldUpdateOperationsInput = {
  set?: string
}

export type BoolFieldUpdateOperationsInput = {
  set?: boolean
}

export type NullableStringFieldUpdateOperationsInput = {
  set?: string | null
}

export type EnumUserRoleFieldUpdateOperationsInput = {
  set?: $Enums.UserRole
}

export type NullableDateTimeFieldUpdateOperationsInput = {
  set?: Date | string | null
}

export type DateTimeFieldUpdateOperationsInput = {
  set?: Date | string
}

export type UserCreateNestedOneWithoutSessionsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSessionsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutSessionsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSessionsInput
  upsert?: Prisma.UserUpsertWithoutSessionsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutSessionsInput, Prisma.UserUpdateWithoutSessionsInput>, Prisma.UserUncheckedUpdateWithoutSessionsInput>
}

export type UserCreateNestedOneWithoutAccountsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAccountsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutAccountsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAccountsInput
  upsert?: Prisma.UserUpsertWithoutAccountsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutAccountsInput, Prisma.UserUpdateWithoutAccountsInput>, Prisma.UserUncheckedUpdateWithoutAccountsInput>
}

export type UserCreateNestedOneWithoutSentInvitationsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSentInvitationsInput, Prisma.UserUncheckedCreateWithoutSentInvitationsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSentInvitationsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserCreateNestedOneWithoutReceivedInvitationsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutReceivedInvitationsInput, Prisma.UserUncheckedCreateWithoutReceivedInvitationsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutReceivedInvitationsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutSentInvitationsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSentInvitationsInput, Prisma.UserUncheckedCreateWithoutSentInvitationsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSentInvitationsInput
  upsert?: Prisma.UserUpsertWithoutSentInvitationsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutSentInvitationsInput, Prisma.UserUpdateWithoutSentInvitationsInput>, Prisma.UserUncheckedUpdateWithoutSentInvitationsInput>
}

export type UserUpdateOneWithoutReceivedInvitationsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutReceivedInvitationsInput, Prisma.UserUncheckedCreateWithoutReceivedInvitationsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutReceivedInvitationsInput
  upsert?: Prisma.UserUpsertWithoutReceivedInvitationsInput
  disconnect?: Prisma.UserWhereInput | boolean
  delete?: Prisma.UserWhereInput | boolean
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutReceivedInvitationsInput, Prisma.UserUpdateWithoutReceivedInvitationsInput>, Prisma.UserUncheckedUpdateWithoutReceivedInvitationsInput>
}

export type UserCreateNestedOneWithoutCreatedLicensesInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutCreatedLicensesInput, Prisma.UserUncheckedCreateWithoutCreatedLicensesInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutCreatedLicensesInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneWithoutCreatedLicensesNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutCreatedLicensesInput, Prisma.UserUncheckedCreateWithoutCreatedLicensesInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutCreatedLicensesInput
  upsert?: Prisma.UserUpsertWithoutCreatedLicensesInput
  disconnect?: Prisma.UserWhereInput | boolean
  delete?: Prisma.UserWhereInput | boolean
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutCreatedLicensesInput, Prisma.UserUpdateWithoutCreatedLicensesInput>, Prisma.UserUncheckedUpdateWithoutCreatedLicensesInput>
}

export type UserCreateNestedOneWithoutProcessedRefundsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutProcessedRefundsInput, Prisma.UserUncheckedCreateWithoutProcessedRefundsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutProcessedRefundsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneWithoutProcessedRefundsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutProcessedRefundsInput, Prisma.UserUncheckedCreateWithoutProcessedRefundsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutProcessedRefundsInput
  upsert?: Prisma.UserUpsertWithoutProcessedRefundsInput
  disconnect?: Prisma.UserWhereInput | boolean
  delete?: Prisma.UserWhereInput | boolean
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutProcessedRefundsInput, Prisma.UserUpdateWithoutProcessedRefundsInput>, Prisma.UserUncheckedUpdateWithoutProcessedRefundsInput>
}

export type UserCreateNestedOneWithoutAuditLogsAsActorInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAuditLogsAsActorInput, Prisma.UserUncheckedCreateWithoutAuditLogsAsActorInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAuditLogsAsActorInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserCreateNestedOneWithoutAuditLogsAsTargetInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAuditLogsAsTargetInput, Prisma.UserUncheckedCreateWithoutAuditLogsAsTargetInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAuditLogsAsTargetInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneWithoutAuditLogsAsActorNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAuditLogsAsActorInput, Prisma.UserUncheckedCreateWithoutAuditLogsAsActorInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAuditLogsAsActorInput
  upsert?: Prisma.UserUpsertWithoutAuditLogsAsActorInput
  disconnect?: Prisma.UserWhereInput | boolean
  delete?: Prisma.UserWhereInput | boolean
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutAuditLogsAsActorInput, Prisma.UserUpdateWithoutAuditLogsAsActorInput>, Prisma.UserUncheckedUpdateWithoutAuditLogsAsActorInput>
}

export type UserUpdateOneWithoutAuditLogsAsTargetNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAuditLogsAsTargetInput, Prisma.UserUncheckedCreateWithoutAuditLogsAsTargetInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAuditLogsAsTargetInput
  upsert?: Prisma.UserUpsertWithoutAuditLogsAsTargetInput
  disconnect?: Prisma.UserWhereInput | boolean
  delete?: Prisma.UserWhereInput | boolean
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutAuditLogsAsTargetInput, Prisma.UserUpdateWithoutAuditLogsAsTargetInput>, Prisma.UserUncheckedUpdateWithoutAuditLogsAsTargetInput>
}

export type UserCreateNestedOneWithoutAssignedTicketsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAssignedTicketsInput, Prisma.UserUncheckedCreateWithoutAssignedTicketsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAssignedTicketsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneWithoutAssignedTicketsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAssignedTicketsInput, Prisma.UserUncheckedCreateWithoutAssignedTicketsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAssignedTicketsInput
  upsert?: Prisma.UserUpsertWithoutAssignedTicketsInput
  disconnect?: Prisma.UserWhereInput | boolean
  delete?: Prisma.UserWhereInput | boolean
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutAssignedTicketsInput, Prisma.UserUpdateWithoutAssignedTicketsInput>, Prisma.UserUncheckedUpdateWithoutAssignedTicketsInput>
}

export type UserCreateNestedOneWithoutSupportMessagesInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSupportMessagesInput, Prisma.UserUncheckedCreateWithoutSupportMessagesInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSupportMessagesInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneWithoutSupportMessagesNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSupportMessagesInput, Prisma.UserUncheckedCreateWithoutSupportMessagesInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSupportMessagesInput
  upsert?: Prisma.UserUpsertWithoutSupportMessagesInput
  disconnect?: Prisma.UserWhereInput | boolean
  delete?: Prisma.UserWhereInput | boolean
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutSupportMessagesInput, Prisma.UserUpdateWithoutSupportMessagesInput>, Prisma.UserUncheckedUpdateWithoutSupportMessagesInput>
}

export type UserCreateWithoutSessionsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput
}

export type UserUncheckedCreateWithoutSessionsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput
}

export type UserCreateOrConnectWithoutSessionsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
}

export type UserUpsertWithoutSessionsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutSessionsInput, Prisma.UserUncheckedUpdateWithoutSessionsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutSessionsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutSessionsInput, Prisma.UserUncheckedUpdateWithoutSessionsInput>
}

export type UserUpdateWithoutSessionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUpdateManyWithoutAuthorUserNestedInput
}

export type UserUncheckedUpdateWithoutSessionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput
}

export type UserCreateWithoutAccountsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput
}

export type UserUncheckedCreateWithoutAccountsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput
}

export type UserCreateOrConnectWithoutAccountsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
}

export type UserUpsertWithoutAccountsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutAccountsInput, Prisma.UserUncheckedUpdateWithoutAccountsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutAccountsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutAccountsInput, Prisma.UserUncheckedUpdateWithoutAccountsInput>
}

export type UserUpdateWithoutAccountsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUpdateManyWithoutAuthorUserNestedInput
}

export type UserUncheckedUpdateWithoutAccountsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput
}

export type UserCreateWithoutSentInvitationsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseCreateNestedManyWithoutCreatedByUserInput
  receivedInvitations?: Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput
}

export type UserUncheckedCreateWithoutSentInvitationsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput
  receivedInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput
}

export type UserCreateOrConnectWithoutSentInvitationsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutSentInvitationsInput, Prisma.UserUncheckedCreateWithoutSentInvitationsInput>
}

export type UserCreateWithoutReceivedInvitationsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput
  auditLogsAsActor?: Prisma.AuditLogCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput
}

export type UserUncheckedCreateWithoutReceivedInvitationsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput
}

export type UserCreateOrConnectWithoutReceivedInvitationsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutReceivedInvitationsInput, Prisma.UserUncheckedCreateWithoutReceivedInvitationsInput>
}

export type UserUpsertWithoutSentInvitationsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutSentInvitationsInput, Prisma.UserUncheckedUpdateWithoutSentInvitationsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutSentInvitationsInput, Prisma.UserUncheckedCreateWithoutSentInvitationsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutSentInvitationsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutSentInvitationsInput, Prisma.UserUncheckedUpdateWithoutSentInvitationsInput>
}

export type UserUpdateWithoutSentInvitationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUpdateManyWithoutCreatedByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUpdateManyWithoutAuthorUserNestedInput
}

export type UserUncheckedUpdateWithoutSentInvitationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput
}

export type UserUpsertWithoutReceivedInvitationsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutReceivedInvitationsInput, Prisma.UserUncheckedUpdateWithoutReceivedInvitationsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutReceivedInvitationsInput, Prisma.UserUncheckedCreateWithoutReceivedInvitationsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutReceivedInvitationsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutReceivedInvitationsInput, Prisma.UserUncheckedUpdateWithoutReceivedInvitationsInput>
}

export type UserUpdateWithoutReceivedInvitationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUpdateManyWithoutSentByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUpdateManyWithoutAuthorUserNestedInput
}

export type UserUncheckedUpdateWithoutReceivedInvitationsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput
}

export type UserCreateWithoutCreatedLicensesInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  sentInvitations?: Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput
}

export type UserUncheckedCreateWithoutCreatedLicensesInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  sentInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput
}

export type UserCreateOrConnectWithoutCreatedLicensesInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutCreatedLicensesInput, Prisma.UserUncheckedCreateWithoutCreatedLicensesInput>
}

export type UserUpsertWithoutCreatedLicensesInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutCreatedLicensesInput, Prisma.UserUncheckedUpdateWithoutCreatedLicensesInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutCreatedLicensesInput, Prisma.UserUncheckedCreateWithoutCreatedLicensesInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutCreatedLicensesInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutCreatedLicensesInput, Prisma.UserUncheckedUpdateWithoutCreatedLicensesInput>
}

export type UserUpdateWithoutCreatedLicensesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  sentInvitations?: Prisma.UserInvitationUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUpdateManyWithoutAuthorUserNestedInput
}

export type UserUncheckedUpdateWithoutCreatedLicensesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  sentInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput
}

export type UserCreateWithoutProcessedRefundsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogCreateNestedManyWithoutTargetInput
  assignedTickets?: Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput
}

export type UserUncheckedCreateWithoutProcessedRefundsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedCreateNestedManyWithoutTargetInput
  assignedTickets?: Prisma.SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput
}

export type UserCreateOrConnectWithoutProcessedRefundsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutProcessedRefundsInput, Prisma.UserUncheckedCreateWithoutProcessedRefundsInput>
}

export type UserUpsertWithoutProcessedRefundsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutProcessedRefundsInput, Prisma.UserUncheckedUpdateWithoutProcessedRefundsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutProcessedRefundsInput, Prisma.UserUncheckedCreateWithoutProcessedRefundsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutProcessedRefundsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutProcessedRefundsInput, Prisma.UserUncheckedUpdateWithoutProcessedRefundsInput>
}

export type UserUpdateWithoutProcessedRefundsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUpdateManyWithoutTargetNestedInput
  assignedTickets?: Prisma.SupportTicketUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUpdateManyWithoutAuthorUserNestedInput
}

export type UserUncheckedUpdateWithoutProcessedRefundsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedUpdateManyWithoutTargetNestedInput
  assignedTickets?: Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput
}

export type UserCreateWithoutAuditLogsAsActorInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsTarget?: Prisma.AuditLogCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput
}

export type UserUncheckedCreateWithoutAuditLogsAsActorInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput
}

export type UserCreateOrConnectWithoutAuditLogsAsActorInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutAuditLogsAsActorInput, Prisma.UserUncheckedCreateWithoutAuditLogsAsActorInput>
}

export type UserCreateWithoutAuditLogsAsTargetInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogCreateNestedManyWithoutActorInput
  processedRefunds?: Prisma.RefundRequestCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput
}

export type UserUncheckedCreateWithoutAuditLogsAsTargetInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedCreateNestedManyWithoutActorInput
  processedRefunds?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput
  supportMessages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput
}

export type UserCreateOrConnectWithoutAuditLogsAsTargetInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutAuditLogsAsTargetInput, Prisma.UserUncheckedCreateWithoutAuditLogsAsTargetInput>
}

export type UserUpsertWithoutAuditLogsAsActorInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutAuditLogsAsActorInput, Prisma.UserUncheckedUpdateWithoutAuditLogsAsActorInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutAuditLogsAsActorInput, Prisma.UserUncheckedCreateWithoutAuditLogsAsActorInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutAuditLogsAsActorInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutAuditLogsAsActorInput, Prisma.UserUncheckedUpdateWithoutAuditLogsAsActorInput>
}

export type UserUpdateWithoutAuditLogsAsActorInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUpdateManyWithoutAuthorUserNestedInput
}

export type UserUncheckedUpdateWithoutAuditLogsAsActorInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput
}

export type UserUpsertWithoutAuditLogsAsTargetInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutAuditLogsAsTargetInput, Prisma.UserUncheckedUpdateWithoutAuditLogsAsTargetInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutAuditLogsAsTargetInput, Prisma.UserUncheckedCreateWithoutAuditLogsAsTargetInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutAuditLogsAsTargetInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutAuditLogsAsTargetInput, Prisma.UserUncheckedUpdateWithoutAuditLogsAsTargetInput>
}

export type UserUpdateWithoutAuditLogsAsTargetInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUpdateManyWithoutActorNestedInput
  processedRefunds?: Prisma.RefundRequestUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUpdateManyWithoutAuthorUserNestedInput
}

export type UserUncheckedUpdateWithoutAuditLogsAsTargetInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedUpdateManyWithoutActorNestedInput
  processedRefunds?: Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput
  supportMessages?: Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput
}

export type UserCreateWithoutAssignedTicketsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestCreateNestedManyWithoutProcessedByUserInput
  supportMessages?: Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput
}

export type UserUncheckedCreateWithoutAssignedTicketsInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput
  supportMessages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput
}

export type UserCreateOrConnectWithoutAssignedTicketsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutAssignedTicketsInput, Prisma.UserUncheckedCreateWithoutAssignedTicketsInput>
}

export type UserUpsertWithoutAssignedTicketsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutAssignedTicketsInput, Prisma.UserUncheckedUpdateWithoutAssignedTicketsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutAssignedTicketsInput, Prisma.UserUncheckedCreateWithoutAssignedTicketsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutAssignedTicketsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutAssignedTicketsInput, Prisma.UserUncheckedUpdateWithoutAssignedTicketsInput>
}

export type UserUpdateWithoutAssignedTicketsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUpdateManyWithoutProcessedByUserNestedInput
  supportMessages?: Prisma.SupportMessageUpdateManyWithoutAuthorUserNestedInput
}

export type UserUncheckedUpdateWithoutAssignedTicketsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput
  supportMessages?: Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput
}

export type UserCreateWithoutSupportMessagesInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput
}

export type UserUncheckedCreateWithoutSupportMessagesInput = {
  id?: string
  name: string
  email: string
  emailVerified?: boolean
  image?: string | null
  role?: $Enums.UserRole
  isActive?: boolean
  invitedBy?: string | null
  invitedAt?: Date | string | null
  lastLoginAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  createdLicenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput
  sentInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput
  receivedInvitations?: Prisma.UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedCreateNestedManyWithoutActorInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedCreateNestedManyWithoutTargetInput
  processedRefunds?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput
  assignedTickets?: Prisma.SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput
}

export type UserCreateOrConnectWithoutSupportMessagesInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutSupportMessagesInput, Prisma.UserUncheckedCreateWithoutSupportMessagesInput>
}

export type UserUpsertWithoutSupportMessagesInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutSupportMessagesInput, Prisma.UserUncheckedUpdateWithoutSupportMessagesInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutSupportMessagesInput, Prisma.UserUncheckedCreateWithoutSupportMessagesInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutSupportMessagesInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutSupportMessagesInput, Prisma.UserUncheckedUpdateWithoutSupportMessagesInput>
}

export type UserUpdateWithoutSupportMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUpdateManyWithoutAssignedToUserNestedInput
}

export type UserUncheckedUpdateWithoutSupportMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  isActive?: Prisma.BoolFieldUpdateOperationsInput | boolean
  invitedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  invitedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  lastLoginAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  createdLicenses?: Prisma.LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput
  sentInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput
  receivedInvitations?: Prisma.UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput
  auditLogsAsActor?: Prisma.AuditLogUncheckedUpdateManyWithoutActorNestedInput
  auditLogsAsTarget?: Prisma.AuditLogUncheckedUpdateManyWithoutTargetNestedInput
  processedRefunds?: Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput
  assignedTickets?: Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput
}


/**
 * Count Type UserCountOutputType
 */

export type UserCountOutputType = {
  sessions: number
  accounts: number
  createdLicenses: number
  sentInvitations: number
  receivedInvitations: number
  auditLogsAsActor: number
  auditLogsAsTarget: number
  processedRefunds: number
  assignedTickets: number
  supportMessages: number
}

export type UserCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  sessions?: boolean | UserCountOutputTypeCountSessionsArgs
  accounts?: boolean | UserCountOutputTypeCountAccountsArgs
  createdLicenses?: boolean | UserCountOutputTypeCountCreatedLicensesArgs
  sentInvitations?: boolean | UserCountOutputTypeCountSentInvitationsArgs
  receivedInvitations?: boolean | UserCountOutputTypeCountReceivedInvitationsArgs
  auditLogsAsActor?: boolean | UserCountOutputTypeCountAuditLogsAsActorArgs
  auditLogsAsTarget?: boolean | UserCountOutputTypeCountAuditLogsAsTargetArgs
  processedRefunds?: boolean | UserCountOutputTypeCountProcessedRefundsArgs
  assignedTickets?: boolean | UserCountOutputTypeCountAssignedTicketsArgs
  supportMessages?: boolean | UserCountOutputTypeCountSupportMessagesArgs
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserCountOutputType
   */
  select?: Prisma.UserCountOutputTypeSelect<ExtArgs> | null
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountSessionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.SessionWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountAccountsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AccountWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountCreatedLicensesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.LicenseWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountSentInvitationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UserInvitationWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountReceivedInvitationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UserInvitationWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountAuditLogsAsActorArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AuditLogWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountAuditLogsAsTargetArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AuditLogWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountProcessedRefundsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RefundRequestWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountAssignedTicketsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.SupportTicketWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountSupportMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.SupportMessageWhereInput
}


export type UserSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  role?: boolean
  isActive?: boolean
  invitedBy?: boolean
  invitedAt?: boolean
  lastLoginAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  sessions?: boolean | Prisma.User$sessionsArgs<ExtArgs>
  accounts?: boolean | Prisma.User$accountsArgs<ExtArgs>
  createdLicenses?: boolean | Prisma.User$createdLicensesArgs<ExtArgs>
  sentInvitations?: boolean | Prisma.User$sentInvitationsArgs<ExtArgs>
  receivedInvitations?: boolean | Prisma.User$receivedInvitationsArgs<ExtArgs>
  auditLogsAsActor?: boolean | Prisma.User$auditLogsAsActorArgs<ExtArgs>
  auditLogsAsTarget?: boolean | Prisma.User$auditLogsAsTargetArgs<ExtArgs>
  processedRefunds?: boolean | Prisma.User$processedRefundsArgs<ExtArgs>
  assignedTickets?: boolean | Prisma.User$assignedTicketsArgs<ExtArgs>
  supportMessages?: boolean | Prisma.User$supportMessagesArgs<ExtArgs>
  _count?: boolean | Prisma.UserCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["user"]>

export type UserSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  role?: boolean
  isActive?: boolean
  invitedBy?: boolean
  invitedAt?: boolean
  lastLoginAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["user"]>

export type UserSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  role?: boolean
  isActive?: boolean
  invitedBy?: boolean
  invitedAt?: boolean
  lastLoginAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["user"]>

export type UserSelectScalar = {
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  role?: boolean
  isActive?: boolean
  invitedBy?: boolean
  invitedAt?: boolean
  lastLoginAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type UserOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "email" | "emailVerified" | "image" | "role" | "isActive" | "invitedBy" | "invitedAt" | "lastLoginAt" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
export type UserInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  sessions?: boolean | Prisma.User$sessionsArgs<ExtArgs>
  accounts?: boolean | Prisma.User$accountsArgs<ExtArgs>
  createdLicenses?: boolean | Prisma.User$createdLicensesArgs<ExtArgs>
  sentInvitations?: boolean | Prisma.User$sentInvitationsArgs<ExtArgs>
  receivedInvitations?: boolean | Prisma.User$receivedInvitationsArgs<ExtArgs>
  auditLogsAsActor?: boolean | Prisma.User$auditLogsAsActorArgs<ExtArgs>
  auditLogsAsTarget?: boolean | Prisma.User$auditLogsAsTargetArgs<ExtArgs>
  processedRefunds?: boolean | Prisma.User$processedRefundsArgs<ExtArgs>
  assignedTickets?: boolean | Prisma.User$assignedTicketsArgs<ExtArgs>
  supportMessages?: boolean | Prisma.User$supportMessagesArgs<ExtArgs>
  _count?: boolean | Prisma.UserCountOutputTypeDefaultArgs<ExtArgs>
}
export type UserIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}
export type UserIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}

export type $UserPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "User"
  objects: {
    sessions: Prisma.$SessionPayload<ExtArgs>[]
    accounts: Prisma.$AccountPayload<ExtArgs>[]
    createdLicenses: Prisma.$LicensePayload<ExtArgs>[]
    sentInvitations: Prisma.$UserInvitationPayload<ExtArgs>[]
    receivedInvitations: Prisma.$UserInvitationPayload<ExtArgs>[]
    auditLogsAsActor: Prisma.$AuditLogPayload<ExtArgs>[]
    auditLogsAsTarget: Prisma.$AuditLogPayload<ExtArgs>[]
    processedRefunds: Prisma.$RefundRequestPayload<ExtArgs>[]
    assignedTickets: Prisma.$SupportTicketPayload<ExtArgs>[]
    supportMessages: Prisma.$SupportMessagePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string
    email: string
    emailVerified: boolean
    image: string | null
    role: $Enums.UserRole
    isActive: boolean
    invitedBy: string | null
    invitedAt: Date | null
    lastLoginAt: Date | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["user"]>
  composites: {}
}

export type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$UserPayload, S>

export type UserCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: UserCountAggregateInputType | true
  }

export interface UserDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
  /**
   * Find zero or one User that matches the filter.
   * @param {UserFindUniqueArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends UserFindUniqueArgs>(args: Prisma.SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindFirstArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends UserFindFirstArgs>(args?: Prisma.SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Users that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Users
   * const users = await prisma.user.findMany()
   * 
   * // Get first 10 Users
   * const users = await prisma.user.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends UserFindManyArgs>(args?: Prisma.SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User.
   * @param {UserCreateArgs} args - Arguments to create a User.
   * @example
   * // Create one User
   * const User = await prisma.user.create({
   *   data: {
   *     // ... data to create a User
   *   }
   * })
   * 
   */
  create<T extends UserCreateArgs>(args: Prisma.SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Users.
   * @param {UserCreateManyArgs} args - Arguments to create many Users.
   * @example
   * // Create many Users
   * const user = await prisma.user.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends UserCreateManyArgs>(args?: Prisma.SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Users and returns the data saved in the database.
   * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
   * @example
   * // Create many Users
   * const user = await prisma.user.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Users and only return the `id`
   * const userWithIdOnly = await prisma.user.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a User.
   * @param {UserDeleteArgs} args - Arguments to delete one User.
   * @example
   * // Delete one User
   * const User = await prisma.user.delete({
   *   where: {
   *     // ... filter to delete one User
   *   }
   * })
   * 
   */
  delete<T extends UserDeleteArgs>(args: Prisma.SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User.
   * @param {UserUpdateArgs} args - Arguments to update one User.
   * @example
   * // Update one User
   * const user = await prisma.user.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends UserUpdateArgs>(args: Prisma.SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Users.
   * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
   * @example
   * // Delete a few Users
   * const { count } = await prisma.user.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends UserDeleteManyArgs>(args?: Prisma.SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Users.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Users
   * const user = await prisma.user.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends UserUpdateManyArgs>(args: Prisma.SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Users and returns the data updated in the database.
   * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
   * @example
   * // Update many Users
   * const user = await prisma.user.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Users and only return the `id`
   * const userWithIdOnly = await prisma.user.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one User.
   * @param {UserUpsertArgs} args - Arguments to update or create a User.
   * @example
   * // Update or create a User
   * const user = await prisma.user.upsert({
   *   create: {
   *     // ... data to create a User
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User we want to update
   *   }
   * })
   */
  upsert<T extends UserUpsertArgs>(args: Prisma.SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Users.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserCountArgs} args - Arguments to filter Users to count.
   * @example
   * // Count the number of Users
   * const count = await prisma.user.count({
   *   where: {
   *     // ... the filter for the Users we want to count
   *   }
   * })
  **/
  count<T extends UserCountArgs>(
    args?: Prisma.Subset<T, UserCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], UserCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends UserAggregateArgs>(args: Prisma.Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

  /**
   * Group by User.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends UserGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: UserGroupByArgs['orderBy'] }
      : { orderBy?: UserGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the User model
 */
readonly fields: UserFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for User.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__UserClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  sessions<T extends Prisma.User$sessionsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$sessionsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  accounts<T extends Prisma.User$accountsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$accountsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AccountPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  createdLicenses<T extends Prisma.User$createdLicensesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$createdLicensesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  sentInvitations<T extends Prisma.User$sentInvitationsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$sentInvitationsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  receivedInvitations<T extends Prisma.User$receivedInvitationsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$receivedInvitationsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  auditLogsAsActor<T extends Prisma.User$auditLogsAsActorArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$auditLogsAsActorArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  auditLogsAsTarget<T extends Prisma.User$auditLogsAsTargetArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$auditLogsAsTargetArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  processedRefunds<T extends Prisma.User$processedRefundsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$processedRefundsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  assignedTickets<T extends Prisma.User$assignedTicketsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$assignedTicketsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  supportMessages<T extends Prisma.User$supportMessagesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$supportMessagesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the User model
 */
export interface UserFieldRefs {
  readonly id: Prisma.FieldRef<"User", 'String'>
  readonly name: Prisma.FieldRef<"User", 'String'>
  readonly email: Prisma.FieldRef<"User", 'String'>
  readonly emailVerified: Prisma.FieldRef<"User", 'Boolean'>
  readonly image: Prisma.FieldRef<"User", 'String'>
  readonly role: Prisma.FieldRef<"User", 'UserRole'>
  readonly isActive: Prisma.FieldRef<"User", 'Boolean'>
  readonly invitedBy: Prisma.FieldRef<"User", 'String'>
  readonly invitedAt: Prisma.FieldRef<"User", 'DateTime'>
  readonly lastLoginAt: Prisma.FieldRef<"User", 'DateTime'>
  readonly createdAt: Prisma.FieldRef<"User", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"User", 'DateTime'>
}
    

// Custom InputTypes
/**
 * User findUnique
 */
export type UserFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User findUniqueOrThrow
 */
export type UserFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User findFirst
 */
export type UserFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Users.
   */
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User findFirstOrThrow
 */
export type UserFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Users.
   */
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User findMany
 */
export type UserFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which Users to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User create
 */
export type UserCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The data needed to create a User.
   */
  data: Prisma.XOR<Prisma.UserCreateInput, Prisma.UserUncheckedCreateInput>
}

/**
 * User createMany
 */
export type UserCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Users.
   */
  data: Prisma.UserCreateManyInput | Prisma.UserCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * User createManyAndReturn
 */
export type UserCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * The data used to create many Users.
   */
  data: Prisma.UserCreateManyInput | Prisma.UserCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * User update
 */
export type UserUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The data needed to update a User.
   */
  data: Prisma.XOR<Prisma.UserUpdateInput, Prisma.UserUncheckedUpdateInput>
  /**
   * Choose, which User to update.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User updateMany
 */
export type UserUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Users.
   */
  data: Prisma.XOR<Prisma.UserUpdateManyMutationInput, Prisma.UserUncheckedUpdateManyInput>
  /**
   * Filter which Users to update
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to update.
   */
  limit?: number
}

/**
 * User updateManyAndReturn
 */
export type UserUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * The data used to update Users.
   */
  data: Prisma.XOR<Prisma.UserUpdateManyMutationInput, Prisma.UserUncheckedUpdateManyInput>
  /**
   * Filter which Users to update
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to update.
   */
  limit?: number
}

/**
 * User upsert
 */
export type UserUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The filter to search for the User to update in case it exists.
   */
  where: Prisma.UserWhereUniqueInput
  /**
   * In case the User found by the `where` argument doesn't exist, create a new User with this data.
   */
  create: Prisma.XOR<Prisma.UserCreateInput, Prisma.UserUncheckedCreateInput>
  /**
   * In case the User was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.UserUpdateInput, Prisma.UserUncheckedUpdateInput>
}

/**
 * User delete
 */
export type UserDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter which User to delete.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User deleteMany
 */
export type UserDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Users to delete
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to delete.
   */
  limit?: number
}

/**
 * User.sessions
 */
export type User$sessionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  where?: Prisma.SessionWhereInput
  orderBy?: Prisma.SessionOrderByWithRelationInput | Prisma.SessionOrderByWithRelationInput[]
  cursor?: Prisma.SessionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SessionScalarFieldEnum | Prisma.SessionScalarFieldEnum[]
}

/**
 * User.accounts
 */
export type User$accountsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Account
   */
  select?: Prisma.AccountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Account
   */
  omit?: Prisma.AccountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AccountInclude<ExtArgs> | null
  where?: Prisma.AccountWhereInput
  orderBy?: Prisma.AccountOrderByWithRelationInput | Prisma.AccountOrderByWithRelationInput[]
  cursor?: Prisma.AccountWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AccountScalarFieldEnum | Prisma.AccountScalarFieldEnum[]
}

/**
 * User.createdLicenses
 */
export type User$createdLicensesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
  where?: Prisma.LicenseWhereInput
  orderBy?: Prisma.LicenseOrderByWithRelationInput | Prisma.LicenseOrderByWithRelationInput[]
  cursor?: Prisma.LicenseWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.LicenseScalarFieldEnum | Prisma.LicenseScalarFieldEnum[]
}

/**
 * User.sentInvitations
 */
export type User$sentInvitationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
  where?: Prisma.UserInvitationWhereInput
  orderBy?: Prisma.UserInvitationOrderByWithRelationInput | Prisma.UserInvitationOrderByWithRelationInput[]
  cursor?: Prisma.UserInvitationWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.UserInvitationScalarFieldEnum | Prisma.UserInvitationScalarFieldEnum[]
}

/**
 * User.receivedInvitations
 */
export type User$receivedInvitationsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
  where?: Prisma.UserInvitationWhereInput
  orderBy?: Prisma.UserInvitationOrderByWithRelationInput | Prisma.UserInvitationOrderByWithRelationInput[]
  cursor?: Prisma.UserInvitationWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.UserInvitationScalarFieldEnum | Prisma.UserInvitationScalarFieldEnum[]
}

/**
 * User.auditLogsAsActor
 */
export type User$auditLogsAsActorArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AuditLogInclude<ExtArgs> | null
  where?: Prisma.AuditLogWhereInput
  orderBy?: Prisma.AuditLogOrderByWithRelationInput | Prisma.AuditLogOrderByWithRelationInput[]
  cursor?: Prisma.AuditLogWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AuditLogScalarFieldEnum | Prisma.AuditLogScalarFieldEnum[]
}

/**
 * User.auditLogsAsTarget
 */
export type User$auditLogsAsTargetArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the AuditLog
   */
  select?: Prisma.AuditLogSelect<ExtArgs> | null
  /**
   * Omit specific fields from the AuditLog
   */
  omit?: Prisma.AuditLogOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AuditLogInclude<ExtArgs> | null
  where?: Prisma.AuditLogWhereInput
  orderBy?: Prisma.AuditLogOrderByWithRelationInput | Prisma.AuditLogOrderByWithRelationInput[]
  cursor?: Prisma.AuditLogWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AuditLogScalarFieldEnum | Prisma.AuditLogScalarFieldEnum[]
}

/**
 * User.processedRefunds
 */
export type User$processedRefundsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
  where?: Prisma.RefundRequestWhereInput
  orderBy?: Prisma.RefundRequestOrderByWithRelationInput | Prisma.RefundRequestOrderByWithRelationInput[]
  cursor?: Prisma.RefundRequestWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.RefundRequestScalarFieldEnum | Prisma.RefundRequestScalarFieldEnum[]
}

/**
 * User.assignedTickets
 */
export type User$assignedTicketsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketInclude<ExtArgs> | null
  where?: Prisma.SupportTicketWhereInput
  orderBy?: Prisma.SupportTicketOrderByWithRelationInput | Prisma.SupportTicketOrderByWithRelationInput[]
  cursor?: Prisma.SupportTicketWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SupportTicketScalarFieldEnum | Prisma.SupportTicketScalarFieldEnum[]
}

/**
 * User.supportMessages
 */
export type User$supportMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
  where?: Prisma.SupportMessageWhereInput
  orderBy?: Prisma.SupportMessageOrderByWithRelationInput | Prisma.SupportMessageOrderByWithRelationInput[]
  cursor?: Prisma.SupportMessageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SupportMessageScalarFieldEnum | Prisma.SupportMessageScalarFieldEnum[]
}

/**
 * User without action
 */
export type UserDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
}
