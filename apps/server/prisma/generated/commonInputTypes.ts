
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports various common sort, input & filter types that are not directly linked to a particular model.
 *
 * 🟢 You can import this file directly.
 */

import * as runtime from "@prisma/client/runtime/library"
import * as $Enums from "./enums"
import type * as Prisma from "./internal/prismaNamespace"


export type StringFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  mode?: Prisma.QueryMode
  not?: Prisma.NestedStringFilter<$PrismaModel> | string
}

export type BoolFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolFilter<$PrismaModel> | boolean
}

export type StringNullableFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  mode?: Prisma.QueryMode
  not?: Prisma.NestedStringNullableFilter<$PrismaModel> | string | null
}

export type EnumUserRoleFilter<$PrismaModel = never> = {
  equals?: $Enums.UserRole | Prisma.EnumUserRoleFieldRefInput<$PrismaModel>
  in?: $Enums.UserRole[] | Prisma.ListEnumUserRoleFieldRefInput<$PrismaModel>
  notIn?: $Enums.UserRole[] | Prisma.ListEnumUserRoleFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
}

export type DateTimeNullableFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
}

export type DateTimeFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeFilter<$PrismaModel> | Date | string
}

export type SortOrderInput = {
  sort: Prisma.SortOrder
  nulls?: Prisma.NullsOrder
}

export type StringWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  mode?: Prisma.QueryMode
  not?: Prisma.NestedStringWithAggregatesFilter<$PrismaModel> | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedStringFilter<$PrismaModel>
  _max?: Prisma.NestedStringFilter<$PrismaModel>
}

export type BoolWithAggregatesFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedBoolFilter<$PrismaModel>
  _max?: Prisma.NestedBoolFilter<$PrismaModel>
}

export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  mode?: Prisma.QueryMode
  not?: Prisma.NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedStringNullableFilter<$PrismaModel>
  _max?: Prisma.NestedStringNullableFilter<$PrismaModel>
}

export type EnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.UserRole | Prisma.EnumUserRoleFieldRefInput<$PrismaModel>
  in?: $Enums.UserRole[] | Prisma.ListEnumUserRoleFieldRefInput<$PrismaModel>
  notIn?: $Enums.UserRole[] | Prisma.ListEnumUserRoleFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumUserRoleFilter<$PrismaModel>
  _max?: Prisma.NestedEnumUserRoleFilter<$PrismaModel>
}

export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
}

export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeFilter<$PrismaModel>
}

export type EnumInvitationStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.InvitationStatus | Prisma.EnumInvitationStatusFieldRefInput<$PrismaModel>
  in?: $Enums.InvitationStatus[] | Prisma.ListEnumInvitationStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.InvitationStatus[] | Prisma.ListEnumInvitationStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumInvitationStatusFilter<$PrismaModel> | $Enums.InvitationStatus
}

export type EnumInvitationStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.InvitationStatus | Prisma.EnumInvitationStatusFieldRefInput<$PrismaModel>
  in?: $Enums.InvitationStatus[] | Prisma.ListEnumInvitationStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.InvitationStatus[] | Prisma.ListEnumInvitationStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumInvitationStatusWithAggregatesFilter<$PrismaModel> | $Enums.InvitationStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumInvitationStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumInvitationStatusFilter<$PrismaModel>
}

export type IntFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntFilter<$PrismaModel> | number
}

export type EnumPaymentStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.PaymentStatus | Prisma.EnumPaymentStatusFieldRefInput<$PrismaModel>
  in?: $Enums.PaymentStatus[] | Prisma.ListEnumPaymentStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.PaymentStatus[] | Prisma.ListEnumPaymentStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumPaymentStatusFilter<$PrismaModel> | $Enums.PaymentStatus
}

export type EnumPaymentTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.PaymentType | Prisma.EnumPaymentTypeFieldRefInput<$PrismaModel>
  in?: $Enums.PaymentType[] | Prisma.ListEnumPaymentTypeFieldRefInput<$PrismaModel>
  notIn?: $Enums.PaymentType[] | Prisma.ListEnumPaymentTypeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumPaymentTypeFilter<$PrismaModel> | $Enums.PaymentType
}

export type IntWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntWithAggregatesFilter<$PrismaModel> | number
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatFilter<$PrismaModel>
  _sum?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedIntFilter<$PrismaModel>
  _max?: Prisma.NestedIntFilter<$PrismaModel>
}

export type EnumPaymentStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.PaymentStatus | Prisma.EnumPaymentStatusFieldRefInput<$PrismaModel>
  in?: $Enums.PaymentStatus[] | Prisma.ListEnumPaymentStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.PaymentStatus[] | Prisma.ListEnumPaymentStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumPaymentStatusWithAggregatesFilter<$PrismaModel> | $Enums.PaymentStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumPaymentStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumPaymentStatusFilter<$PrismaModel>
}

export type EnumPaymentTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.PaymentType | Prisma.EnumPaymentTypeFieldRefInput<$PrismaModel>
  in?: $Enums.PaymentType[] | Prisma.ListEnumPaymentTypeFieldRefInput<$PrismaModel>
  notIn?: $Enums.PaymentType[] | Prisma.ListEnumPaymentTypeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumPaymentTypeWithAggregatesFilter<$PrismaModel> | $Enums.PaymentType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumPaymentTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumPaymentTypeFilter<$PrismaModel>
}

export type EnumLicenseTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.LicenseType | Prisma.EnumLicenseTypeFieldRefInput<$PrismaModel>
  in?: $Enums.LicenseType[] | Prisma.ListEnumLicenseTypeFieldRefInput<$PrismaModel>
  notIn?: $Enums.LicenseType[] | Prisma.ListEnumLicenseTypeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumLicenseTypeFilter<$PrismaModel> | $Enums.LicenseType
}

export type EnumLicenseStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.LicenseStatus | Prisma.EnumLicenseStatusFieldRefInput<$PrismaModel>
  in?: $Enums.LicenseStatus[] | Prisma.ListEnumLicenseStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.LicenseStatus[] | Prisma.ListEnumLicenseStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumLicenseStatusFilter<$PrismaModel> | $Enums.LicenseStatus
}

export type IntNullableFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableFilter<$PrismaModel> | number | null
}

export type EnumLicenseTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.LicenseType | Prisma.EnumLicenseTypeFieldRefInput<$PrismaModel>
  in?: $Enums.LicenseType[] | Prisma.ListEnumLicenseTypeFieldRefInput<$PrismaModel>
  notIn?: $Enums.LicenseType[] | Prisma.ListEnumLicenseTypeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumLicenseTypeWithAggregatesFilter<$PrismaModel> | $Enums.LicenseType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumLicenseTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumLicenseTypeFilter<$PrismaModel>
}

export type EnumLicenseStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.LicenseStatus | Prisma.EnumLicenseStatusFieldRefInput<$PrismaModel>
  in?: $Enums.LicenseStatus[] | Prisma.ListEnumLicenseStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.LicenseStatus[] | Prisma.ListEnumLicenseStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumLicenseStatusWithAggregatesFilter<$PrismaModel> | $Enums.LicenseStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumLicenseStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumLicenseStatusFilter<$PrismaModel>
}

export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _sum?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _max?: Prisma.NestedIntNullableFilter<$PrismaModel>
}

export type EnumDeviceStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.DeviceStatus | Prisma.EnumDeviceStatusFieldRefInput<$PrismaModel>
  in?: $Enums.DeviceStatus[] | Prisma.ListEnumDeviceStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.DeviceStatus[] | Prisma.ListEnumDeviceStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumDeviceStatusFilter<$PrismaModel> | $Enums.DeviceStatus
}

export type EnumDeviceStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.DeviceStatus | Prisma.EnumDeviceStatusFieldRefInput<$PrismaModel>
  in?: $Enums.DeviceStatus[] | Prisma.ListEnumDeviceStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.DeviceStatus[] | Prisma.ListEnumDeviceStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumDeviceStatusWithAggregatesFilter<$PrismaModel> | $Enums.DeviceStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumDeviceStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumDeviceStatusFilter<$PrismaModel>
}

export type EnumDeviceExpansionStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.DeviceExpansionStatus | Prisma.EnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  in?: $Enums.DeviceExpansionStatus[] | Prisma.ListEnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.DeviceExpansionStatus[] | Prisma.ListEnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumDeviceExpansionStatusFilter<$PrismaModel> | $Enums.DeviceExpansionStatus
}

export type EnumDeviceExpansionStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.DeviceExpansionStatus | Prisma.EnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  in?: $Enums.DeviceExpansionStatus[] | Prisma.ListEnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.DeviceExpansionStatus[] | Prisma.ListEnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumDeviceExpansionStatusWithAggregatesFilter<$PrismaModel> | $Enums.DeviceExpansionStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumDeviceExpansionStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumDeviceExpansionStatusFilter<$PrismaModel>
}

export type EnumRefundStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.RefundStatus | Prisma.EnumRefundStatusFieldRefInput<$PrismaModel>
  in?: $Enums.RefundStatus[] | Prisma.ListEnumRefundStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.RefundStatus[] | Prisma.ListEnumRefundStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumRefundStatusFilter<$PrismaModel> | $Enums.RefundStatus
}

export type EnumRefundStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.RefundStatus | Prisma.EnumRefundStatusFieldRefInput<$PrismaModel>
  in?: $Enums.RefundStatus[] | Prisma.ListEnumRefundStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.RefundStatus[] | Prisma.ListEnumRefundStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumRefundStatusWithAggregatesFilter<$PrismaModel> | $Enums.RefundStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumRefundStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumRefundStatusFilter<$PrismaModel>
}

export type EnumAuditActionFilter<$PrismaModel = never> = {
  equals?: $Enums.AuditAction | Prisma.EnumAuditActionFieldRefInput<$PrismaModel>
  in?: $Enums.AuditAction[] | Prisma.ListEnumAuditActionFieldRefInput<$PrismaModel>
  notIn?: $Enums.AuditAction[] | Prisma.ListEnumAuditActionFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumAuditActionFilter<$PrismaModel> | $Enums.AuditAction
}

export type JsonNullableFilter<$PrismaModel = never> =
| Prisma.PatchUndefined<
    Prisma.Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
    Required<JsonNullableFilterBase<$PrismaModel>>
  >
| Prisma.OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

export type JsonNullableFilterBase<$PrismaModel = never> = {
  equals?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  path?: string[]
  mode?: Prisma.QueryMode | Prisma.EnumQueryModeFieldRefInput<$PrismaModel>
  string_contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_starts_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_ends_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  array_starts_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_ends_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_contains?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  lt?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  lte?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  gt?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  gte?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  not?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
}

export type EnumAuditActionWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.AuditAction | Prisma.EnumAuditActionFieldRefInput<$PrismaModel>
  in?: $Enums.AuditAction[] | Prisma.ListEnumAuditActionFieldRefInput<$PrismaModel>
  notIn?: $Enums.AuditAction[] | Prisma.ListEnumAuditActionFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumAuditActionWithAggregatesFilter<$PrismaModel> | $Enums.AuditAction
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumAuditActionFilter<$PrismaModel>
  _max?: Prisma.NestedEnumAuditActionFilter<$PrismaModel>
}

export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
| Prisma.PatchUndefined<
    Prisma.Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
    Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
  >
| Prisma.OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
  equals?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  path?: string[]
  mode?: Prisma.QueryMode | Prisma.EnumQueryModeFieldRefInput<$PrismaModel>
  string_contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_starts_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_ends_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  array_starts_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_ends_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_contains?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  lt?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  lte?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  gt?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  gte?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  not?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedJsonNullableFilter<$PrismaModel>
  _max?: Prisma.NestedJsonNullableFilter<$PrismaModel>
}

export type EnumTicketStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketStatus | Prisma.EnumTicketStatusFieldRefInput<$PrismaModel>
  in?: $Enums.TicketStatus[] | Prisma.ListEnumTicketStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.TicketStatus[] | Prisma.ListEnumTicketStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTicketStatusFilter<$PrismaModel> | $Enums.TicketStatus
}

export type EnumTicketPriorityFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketPriority | Prisma.EnumTicketPriorityFieldRefInput<$PrismaModel>
  in?: $Enums.TicketPriority[] | Prisma.ListEnumTicketPriorityFieldRefInput<$PrismaModel>
  notIn?: $Enums.TicketPriority[] | Prisma.ListEnumTicketPriorityFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTicketPriorityFilter<$PrismaModel> | $Enums.TicketPriority
}

export type EnumTicketCategoryNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketCategory | Prisma.EnumTicketCategoryFieldRefInput<$PrismaModel> | null
  in?: $Enums.TicketCategory[] | Prisma.ListEnumTicketCategoryFieldRefInput<$PrismaModel> | null
  notIn?: $Enums.TicketCategory[] | Prisma.ListEnumTicketCategoryFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedEnumTicketCategoryNullableFilter<$PrismaModel> | $Enums.TicketCategory | null
}

export type EnumTicketStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketStatus | Prisma.EnumTicketStatusFieldRefInput<$PrismaModel>
  in?: $Enums.TicketStatus[] | Prisma.ListEnumTicketStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.TicketStatus[] | Prisma.ListEnumTicketStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTicketStatusWithAggregatesFilter<$PrismaModel> | $Enums.TicketStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTicketStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTicketStatusFilter<$PrismaModel>
}

export type EnumTicketPriorityWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketPriority | Prisma.EnumTicketPriorityFieldRefInput<$PrismaModel>
  in?: $Enums.TicketPriority[] | Prisma.ListEnumTicketPriorityFieldRefInput<$PrismaModel>
  notIn?: $Enums.TicketPriority[] | Prisma.ListEnumTicketPriorityFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTicketPriorityWithAggregatesFilter<$PrismaModel> | $Enums.TicketPriority
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTicketPriorityFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTicketPriorityFilter<$PrismaModel>
}

export type EnumTicketCategoryNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketCategory | Prisma.EnumTicketCategoryFieldRefInput<$PrismaModel> | null
  in?: $Enums.TicketCategory[] | Prisma.ListEnumTicketCategoryFieldRefInput<$PrismaModel> | null
  notIn?: $Enums.TicketCategory[] | Prisma.ListEnumTicketCategoryFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedEnumTicketCategoryNullableWithAggregatesFilter<$PrismaModel> | $Enums.TicketCategory | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTicketCategoryNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTicketCategoryNullableFilter<$PrismaModel>
}

export type NestedStringFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  not?: Prisma.NestedStringFilter<$PrismaModel> | string
}

export type NestedBoolFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolFilter<$PrismaModel> | boolean
}

export type NestedStringNullableFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  not?: Prisma.NestedStringNullableFilter<$PrismaModel> | string | null
}

export type NestedEnumUserRoleFilter<$PrismaModel = never> = {
  equals?: $Enums.UserRole | Prisma.EnumUserRoleFieldRefInput<$PrismaModel>
  in?: $Enums.UserRole[] | Prisma.ListEnumUserRoleFieldRefInput<$PrismaModel>
  notIn?: $Enums.UserRole[] | Prisma.ListEnumUserRoleFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
}

export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
}

export type NestedDateTimeFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeFilter<$PrismaModel> | Date | string
}

export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  not?: Prisma.NestedStringWithAggregatesFilter<$PrismaModel> | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedStringFilter<$PrismaModel>
  _max?: Prisma.NestedStringFilter<$PrismaModel>
}

export type NestedIntFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntFilter<$PrismaModel> | number
}

export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedBoolFilter<$PrismaModel>
  _max?: Prisma.NestedBoolFilter<$PrismaModel>
}

export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  not?: Prisma.NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedStringNullableFilter<$PrismaModel>
  _max?: Prisma.NestedStringNullableFilter<$PrismaModel>
}

export type NestedIntNullableFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableFilter<$PrismaModel> | number | null
}

export type NestedEnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.UserRole | Prisma.EnumUserRoleFieldRefInput<$PrismaModel>
  in?: $Enums.UserRole[] | Prisma.ListEnumUserRoleFieldRefInput<$PrismaModel>
  notIn?: $Enums.UserRole[] | Prisma.ListEnumUserRoleFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumUserRoleFilter<$PrismaModel>
  _max?: Prisma.NestedEnumUserRoleFilter<$PrismaModel>
}

export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
}

export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeFilter<$PrismaModel>
}

export type NestedEnumInvitationStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.InvitationStatus | Prisma.EnumInvitationStatusFieldRefInput<$PrismaModel>
  in?: $Enums.InvitationStatus[] | Prisma.ListEnumInvitationStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.InvitationStatus[] | Prisma.ListEnumInvitationStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumInvitationStatusFilter<$PrismaModel> | $Enums.InvitationStatus
}

export type NestedEnumInvitationStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.InvitationStatus | Prisma.EnumInvitationStatusFieldRefInput<$PrismaModel>
  in?: $Enums.InvitationStatus[] | Prisma.ListEnumInvitationStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.InvitationStatus[] | Prisma.ListEnumInvitationStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumInvitationStatusWithAggregatesFilter<$PrismaModel> | $Enums.InvitationStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumInvitationStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumInvitationStatusFilter<$PrismaModel>
}

export type NestedEnumPaymentStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.PaymentStatus | Prisma.EnumPaymentStatusFieldRefInput<$PrismaModel>
  in?: $Enums.PaymentStatus[] | Prisma.ListEnumPaymentStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.PaymentStatus[] | Prisma.ListEnumPaymentStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumPaymentStatusFilter<$PrismaModel> | $Enums.PaymentStatus
}

export type NestedEnumPaymentTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.PaymentType | Prisma.EnumPaymentTypeFieldRefInput<$PrismaModel>
  in?: $Enums.PaymentType[] | Prisma.ListEnumPaymentTypeFieldRefInput<$PrismaModel>
  notIn?: $Enums.PaymentType[] | Prisma.ListEnumPaymentTypeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumPaymentTypeFilter<$PrismaModel> | $Enums.PaymentType
}

export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntWithAggregatesFilter<$PrismaModel> | number
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatFilter<$PrismaModel>
  _sum?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedIntFilter<$PrismaModel>
  _max?: Prisma.NestedIntFilter<$PrismaModel>
}

export type NestedFloatFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  in?: number[] | Prisma.ListFloatFieldRefInput<$PrismaModel>
  notIn?: number[] | Prisma.ListFloatFieldRefInput<$PrismaModel>
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatFilter<$PrismaModel> | number
}

export type NestedEnumPaymentStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.PaymentStatus | Prisma.EnumPaymentStatusFieldRefInput<$PrismaModel>
  in?: $Enums.PaymentStatus[] | Prisma.ListEnumPaymentStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.PaymentStatus[] | Prisma.ListEnumPaymentStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumPaymentStatusWithAggregatesFilter<$PrismaModel> | $Enums.PaymentStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumPaymentStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumPaymentStatusFilter<$PrismaModel>
}

export type NestedEnumPaymentTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.PaymentType | Prisma.EnumPaymentTypeFieldRefInput<$PrismaModel>
  in?: $Enums.PaymentType[] | Prisma.ListEnumPaymentTypeFieldRefInput<$PrismaModel>
  notIn?: $Enums.PaymentType[] | Prisma.ListEnumPaymentTypeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumPaymentTypeWithAggregatesFilter<$PrismaModel> | $Enums.PaymentType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumPaymentTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumPaymentTypeFilter<$PrismaModel>
}

export type NestedEnumLicenseTypeFilter<$PrismaModel = never> = {
  equals?: $Enums.LicenseType | Prisma.EnumLicenseTypeFieldRefInput<$PrismaModel>
  in?: $Enums.LicenseType[] | Prisma.ListEnumLicenseTypeFieldRefInput<$PrismaModel>
  notIn?: $Enums.LicenseType[] | Prisma.ListEnumLicenseTypeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumLicenseTypeFilter<$PrismaModel> | $Enums.LicenseType
}

export type NestedEnumLicenseStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.LicenseStatus | Prisma.EnumLicenseStatusFieldRefInput<$PrismaModel>
  in?: $Enums.LicenseStatus[] | Prisma.ListEnumLicenseStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.LicenseStatus[] | Prisma.ListEnumLicenseStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumLicenseStatusFilter<$PrismaModel> | $Enums.LicenseStatus
}

export type NestedEnumLicenseTypeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.LicenseType | Prisma.EnumLicenseTypeFieldRefInput<$PrismaModel>
  in?: $Enums.LicenseType[] | Prisma.ListEnumLicenseTypeFieldRefInput<$PrismaModel>
  notIn?: $Enums.LicenseType[] | Prisma.ListEnumLicenseTypeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumLicenseTypeWithAggregatesFilter<$PrismaModel> | $Enums.LicenseType
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumLicenseTypeFilter<$PrismaModel>
  _max?: Prisma.NestedEnumLicenseTypeFilter<$PrismaModel>
}

export type NestedEnumLicenseStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.LicenseStatus | Prisma.EnumLicenseStatusFieldRefInput<$PrismaModel>
  in?: $Enums.LicenseStatus[] | Prisma.ListEnumLicenseStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.LicenseStatus[] | Prisma.ListEnumLicenseStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumLicenseStatusWithAggregatesFilter<$PrismaModel> | $Enums.LicenseStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumLicenseStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumLicenseStatusFilter<$PrismaModel>
}

export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _sum?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _max?: Prisma.NestedIntNullableFilter<$PrismaModel>
}

export type NestedFloatNullableFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel> | null
  in?: number[] | Prisma.ListFloatFieldRefInput<$PrismaModel> | null
  notIn?: number[] | Prisma.ListFloatFieldRefInput<$PrismaModel> | null
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatNullableFilter<$PrismaModel> | number | null
}

export type NestedEnumDeviceStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.DeviceStatus | Prisma.EnumDeviceStatusFieldRefInput<$PrismaModel>
  in?: $Enums.DeviceStatus[] | Prisma.ListEnumDeviceStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.DeviceStatus[] | Prisma.ListEnumDeviceStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumDeviceStatusFilter<$PrismaModel> | $Enums.DeviceStatus
}

export type NestedEnumDeviceStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.DeviceStatus | Prisma.EnumDeviceStatusFieldRefInput<$PrismaModel>
  in?: $Enums.DeviceStatus[] | Prisma.ListEnumDeviceStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.DeviceStatus[] | Prisma.ListEnumDeviceStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumDeviceStatusWithAggregatesFilter<$PrismaModel> | $Enums.DeviceStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumDeviceStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumDeviceStatusFilter<$PrismaModel>
}

export type NestedEnumDeviceExpansionStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.DeviceExpansionStatus | Prisma.EnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  in?: $Enums.DeviceExpansionStatus[] | Prisma.ListEnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.DeviceExpansionStatus[] | Prisma.ListEnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumDeviceExpansionStatusFilter<$PrismaModel> | $Enums.DeviceExpansionStatus
}

export type NestedEnumDeviceExpansionStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.DeviceExpansionStatus | Prisma.EnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  in?: $Enums.DeviceExpansionStatus[] | Prisma.ListEnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.DeviceExpansionStatus[] | Prisma.ListEnumDeviceExpansionStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumDeviceExpansionStatusWithAggregatesFilter<$PrismaModel> | $Enums.DeviceExpansionStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumDeviceExpansionStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumDeviceExpansionStatusFilter<$PrismaModel>
}

export type NestedEnumRefundStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.RefundStatus | Prisma.EnumRefundStatusFieldRefInput<$PrismaModel>
  in?: $Enums.RefundStatus[] | Prisma.ListEnumRefundStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.RefundStatus[] | Prisma.ListEnumRefundStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumRefundStatusFilter<$PrismaModel> | $Enums.RefundStatus
}

export type NestedEnumRefundStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.RefundStatus | Prisma.EnumRefundStatusFieldRefInput<$PrismaModel>
  in?: $Enums.RefundStatus[] | Prisma.ListEnumRefundStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.RefundStatus[] | Prisma.ListEnumRefundStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumRefundStatusWithAggregatesFilter<$PrismaModel> | $Enums.RefundStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumRefundStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumRefundStatusFilter<$PrismaModel>
}

export type NestedEnumAuditActionFilter<$PrismaModel = never> = {
  equals?: $Enums.AuditAction | Prisma.EnumAuditActionFieldRefInput<$PrismaModel>
  in?: $Enums.AuditAction[] | Prisma.ListEnumAuditActionFieldRefInput<$PrismaModel>
  notIn?: $Enums.AuditAction[] | Prisma.ListEnumAuditActionFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumAuditActionFilter<$PrismaModel> | $Enums.AuditAction
}

export type NestedEnumAuditActionWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.AuditAction | Prisma.EnumAuditActionFieldRefInput<$PrismaModel>
  in?: $Enums.AuditAction[] | Prisma.ListEnumAuditActionFieldRefInput<$PrismaModel>
  notIn?: $Enums.AuditAction[] | Prisma.ListEnumAuditActionFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumAuditActionWithAggregatesFilter<$PrismaModel> | $Enums.AuditAction
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumAuditActionFilter<$PrismaModel>
  _max?: Prisma.NestedEnumAuditActionFilter<$PrismaModel>
}

export type NestedJsonNullableFilter<$PrismaModel = never> =
| Prisma.PatchUndefined<
    Prisma.Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
    Required<NestedJsonNullableFilterBase<$PrismaModel>>
  >
| Prisma.OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
  equals?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
  path?: string[]
  mode?: Prisma.QueryMode | Prisma.EnumQueryModeFieldRefInput<$PrismaModel>
  string_contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_starts_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  string_ends_with?: string | Prisma.StringFieldRefInput<$PrismaModel>
  array_starts_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_ends_with?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  array_contains?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  lt?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  lte?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  gt?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  gte?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel>
  not?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | Prisma.JsonNullValueFilter
}

export type NestedEnumTicketStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketStatus | Prisma.EnumTicketStatusFieldRefInput<$PrismaModel>
  in?: $Enums.TicketStatus[] | Prisma.ListEnumTicketStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.TicketStatus[] | Prisma.ListEnumTicketStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTicketStatusFilter<$PrismaModel> | $Enums.TicketStatus
}

export type NestedEnumTicketPriorityFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketPriority | Prisma.EnumTicketPriorityFieldRefInput<$PrismaModel>
  in?: $Enums.TicketPriority[] | Prisma.ListEnumTicketPriorityFieldRefInput<$PrismaModel>
  notIn?: $Enums.TicketPriority[] | Prisma.ListEnumTicketPriorityFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTicketPriorityFilter<$PrismaModel> | $Enums.TicketPriority
}

export type NestedEnumTicketCategoryNullableFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketCategory | Prisma.EnumTicketCategoryFieldRefInput<$PrismaModel> | null
  in?: $Enums.TicketCategory[] | Prisma.ListEnumTicketCategoryFieldRefInput<$PrismaModel> | null
  notIn?: $Enums.TicketCategory[] | Prisma.ListEnumTicketCategoryFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedEnumTicketCategoryNullableFilter<$PrismaModel> | $Enums.TicketCategory | null
}

export type NestedEnumTicketStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketStatus | Prisma.EnumTicketStatusFieldRefInput<$PrismaModel>
  in?: $Enums.TicketStatus[] | Prisma.ListEnumTicketStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.TicketStatus[] | Prisma.ListEnumTicketStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTicketStatusWithAggregatesFilter<$PrismaModel> | $Enums.TicketStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTicketStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTicketStatusFilter<$PrismaModel>
}

export type NestedEnumTicketPriorityWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketPriority | Prisma.EnumTicketPriorityFieldRefInput<$PrismaModel>
  in?: $Enums.TicketPriority[] | Prisma.ListEnumTicketPriorityFieldRefInput<$PrismaModel>
  notIn?: $Enums.TicketPriority[] | Prisma.ListEnumTicketPriorityFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTicketPriorityWithAggregatesFilter<$PrismaModel> | $Enums.TicketPriority
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTicketPriorityFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTicketPriorityFilter<$PrismaModel>
}

export type NestedEnumTicketCategoryNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TicketCategory | Prisma.EnumTicketCategoryFieldRefInput<$PrismaModel> | null
  in?: $Enums.TicketCategory[] | Prisma.ListEnumTicketCategoryFieldRefInput<$PrismaModel> | null
  notIn?: $Enums.TicketCategory[] | Prisma.ListEnumTicketCategoryFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedEnumTicketCategoryNullableWithAggregatesFilter<$PrismaModel> | $Enums.TicketCategory | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTicketCategoryNullableFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTicketCategoryNullableFilter<$PrismaModel>
}


