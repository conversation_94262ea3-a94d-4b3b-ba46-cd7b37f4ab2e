import { transporter } from "@/lib/mailer";
import {
	sendDeviceExpansionEmail,
	sendLicenseEmail,
	sendRefundConfirmationEmail,
} from "@/templates";
import { EndpointPrefix, Logger } from "@/utils/logger";
import {
	prepareDatabaseQuery,
	type StandardPaginationParams,
} from "@/utils/pagination";
import prisma from "../../../../packages/shared/src/db/prisma";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface EmailTemplate {
	id: string;
	name: string;
	subject: string;
	htmlContent: string;
	textContent?: string;
	variables: string[];
	createdAt: Date;
	updatedAt: Date;
}

export interface BulkEmailOptions {
	templateId?: string;
	subject: string;
	htmlContent: string;
	textContent?: string;
	recipients: string[];
	sendAt?: Date;
	variables?: Record<string, string>;
}

export interface EmailDeliveryStats {
	totalSent: number;
	totalDelivered: number;
	totalFailed: number;
	totalBounced: number;
	deliveryRate: number;
	bounceRate: number;
	recentActivity: Array<{
		date: string;
		sent: number;
		delivered: number;
		failed: number;
	}>;
}

// ============================================================================
// EMAIL TEMPLATE FUNCTIONS
// ============================================================================

/**
 * Get all email templates
 */
export async function getEmailTemplates(
	params: StandardPaginationParams = { page: 1, limit: 20 },
): Promise<{
	templates: EmailTemplate[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}> {
	try {
		// Return templates that reference the actual template functions in /templates/
		// These templates represent the email templates available for admin management and bulk operations
		// The actual email sending uses the functions from @/templates (sendLicenseEmail, sendRefundConfirmationEmail, etc.)
		const templates: EmailTemplate[] = [
			{
				id: "license-welcome",
				name: "License Welcome Email",
				subject: "Your SnapBack License Key",
				htmlContent: `
					<h2>Welcome to SnapBack!</h2>
					<p>Thank you for your purchase! Here's your license information:</p>
					<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
						<h3>License Key:</h3>
						<code style="font-size: 18px; font-weight: bold; color: #2563eb;">{{licenseKey}}</code>
					</div>
					<p><strong>License Type:</strong> {{licenseType}}</p>
					<p><strong>Max Devices:</strong> {{maxDevices}}</p>
				`,
				textContent: "Welcome to SnapBack! Your license key: {{licenseKey}}",
				variables: ["licenseKey", "licenseType", "maxDevices"],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: "trial-welcome",
				name: "Trial License Email",
				subject: "Your SnapBack Free Trial License Key",
				htmlContent: `
					<h2>Your SnapBack Free Trial</h2>
					<p>Thank you for trying SnapBack! Here's your free trial license:</p>
					<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
						<h3>License Key:</h3>
						<code style="font-size: 18px; font-weight: bold; color: #2563eb;">{{licenseKey}}</code>
					</div>
					<p><strong>Trial Expires:</strong> {{expiresAt}}</p>
				`,
				textContent:
					"Your SnapBack trial license: {{licenseKey}} - Expires: {{expiresAt}}",
				variables: ["licenseKey", "expiresAt"],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: "refund-confirmation",
				name: "Refund Confirmation Email",
				subject: "Refund Confirmation - Your License Has Been Refunded",
				htmlContent: `
					<h2>Refund Confirmation</h2>
					<p>Your refund request has been processed successfully.</p>
					<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
						<h3>Refund Details:</h3>
						<p><strong>License Key:</strong> {{licenseKey}}</p>
						<p><strong>Refund Amount:</strong> {{refundAmount}} USD</p>
						<p><strong>Refund ID:</strong> {{refundId}}</p>
					</div>
				`,
				textContent:
					"Refund processed for license {{licenseKey}} - Amount: ${{refundAmount}}",
				variables: ["licenseKey", "refundAmount", "refundId"],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: "device-expansion",
				name: "Device Expansion Email",
				subject: "SnapBack License Expanded - More Devices Added!",
				htmlContent: `
					<h2>Your SnapBack License Has Been Expanded!</h2>
					<p>Great news! Your license capacity has been successfully increased.</p>
					<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
						<h3>License Information:</h3>
						<p><strong>License Key:</strong> {{licenseKey}}</p>
						<p><strong>Devices Added:</strong> +{{additionalDevices}} devices</p>
						<p><strong>Total Device Capacity:</strong> {{newMaxDevices}} devices</p>
					</div>
				`,
				textContent:
					"License {{licenseKey}} expanded by {{additionalDevices}} devices",
				variables: ["licenseKey", "additionalDevices", "newMaxDevices"],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		// Apply pagination
		const startIndex = (params.page - 1) * params.limit;
		const endIndex = startIndex + params.limit;
		const paginatedTemplates = templates.slice(startIndex, endIndex);

		Logger.info(
			EndpointPrefix.EMAIL_TEMPLATES,
			`Email templates retrieved: ${paginatedTemplates.length} of ${templates.length}`,
			{
				body: {
					page: params.page,
					limit: params.limit,
					total: templates.length,
				},
			},
		);

		return {
			templates: paginatedTemplates,
			pagination: {
				page: params.page,
				limit: params.limit,
				total: templates.length,
				totalPages: Math.ceil(templates.length / params.limit),
			},
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.EMAIL_TEMPLATES,
			`Failed to get email templates: ${error}`,
			{ body: { params } },
		);
		throw error;
	}
}

/**
 * Send bulk emails to customers
 */
export async function sendBulkEmails(
	options: BulkEmailOptions,
	actorId: string,
): Promise<{
	success: boolean;
	sent: number;
	failed: number;
	errors: string[];
}> {
	try {
		Logger.info(
			EndpointPrefix.EMAIL_BULK,
			`Starting bulk email send to ${options.recipients.length} recipients`,
			{
				body: {
					subject: options.subject,
					recipientCount: options.recipients.length,
					templateId: options.templateId,
					actorId,
				},
			},
		);

		let sent = 0;
		let failed = 0;
		const errors: string[] = [];

		// Process emails in batches to avoid overwhelming the SMTP server
		const batchSize = 10;
		const batches = [];

		for (let i = 0; i < options.recipients.length; i += batchSize) {
			batches.push(options.recipients.slice(i, i + batchSize));
		}

		for (const batch of batches) {
			const promises = batch.map(async (recipient) => {
				try {
					// Replace variables in content if provided
					let htmlContent = options.htmlContent;
					let textContent = options.textContent;

					if (options.variables) {
						for (const [key, value] of Object.entries(options.variables)) {
							const placeholder = `{{${key}}}`;
							htmlContent = htmlContent.replace(
								new RegExp(placeholder, "g"),
								value,
							);
							if (textContent) {
								textContent = textContent.replace(
									new RegExp(placeholder, "g"),
									value,
								);
							}
						}
					}

					const mailOptions = {
						from: process.env.FROM_EMAIL,
						to: recipient,
						subject: options.subject,
						html: htmlContent,
						text: textContent,
					};

					await transporter.sendMail(mailOptions);
					sent++;

					Logger.info(
						EndpointPrefix.EMAIL_BULK,
						`Bulk email sent successfully to: ${recipient}`,
						{ body: { recipient, subject: options.subject } },
					);
				} catch (error) {
					failed++;
					const errorMessage = `Failed to send to ${recipient}: ${error}`;
					errors.push(errorMessage);

					Logger.error(EndpointPrefix.EMAIL_BULK, errorMessage, {
						body: { recipient, error: String(error) },
					});
				}
			});

			// Wait for current batch to complete before processing next batch
			await Promise.all(promises);

			// Small delay between batches to be respectful to SMTP server
			if (batches.indexOf(batch) < batches.length - 1) {
				await new Promise((resolve) => setTimeout(resolve, 1000));
			}
		}

		// Create audit log for bulk email operation
		await prisma.auditLog.create({
			data: {
				action: "BULK_EMAIL_SENT",
				actorId,
				details: {
					subject: options.subject,
					recipientCount: options.recipients.length,
					sent,
					failed,
					templateId: options.templateId,
				},
			},
		});

		Logger.info(
			EndpointPrefix.EMAIL_BULK,
			`Bulk email operation completed: ${sent} sent, ${failed} failed`,
			{
				body: {
					sent,
					failed,
					totalRecipients: options.recipients.length,
					errorCount: errors.length,
				},
			},
		);

		return {
			success: true,
			sent,
			failed,
			errors,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.EMAIL_BULK,
			`Failed to send bulk emails: ${error}`,
			{ body: { options, actorId } },
		);
		throw error;
	}
}

/**
 * Get email delivery statistics
 */
export async function getEmailDeliveryStats(
	startDate?: Date,
	endDate?: Date,
	templateId?: string,
): Promise<EmailDeliveryStats> {
	try {
		const now = new Date();
		const defaultStartDate =
			startDate || new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
		const defaultEndDate = endDate || now;

		Logger.info(EndpointPrefix.EMAIL_STATS, "Retrieving email delivery stats", {
			body: {
				startDate: defaultStartDate.toISOString(),
				endDate: defaultEndDate.toISOString(),
				templateId,
			},
		});

		// In a real implementation, you would query from email delivery logs
		// For now, we'll generate statistics based on license emails sent
		const licenseEmailsQuery = {
			where: {
				emailSentAt: {
					gte: defaultStartDate,
					lte: defaultEndDate,
				},
				emailDeliveryStatus: {
					not: null,
				},
			},
		};

		const [totalLicenseEmails, sentEmails, failedEmails] = await Promise.all([
			prisma.license.count(licenseEmailsQuery),
			prisma.license.count({
				...licenseEmailsQuery,
				where: {
					...licenseEmailsQuery.where,
					emailDeliveryStatus: "sent",
				},
			}),
			prisma.license.count({
				...licenseEmailsQuery,
				where: {
					...licenseEmailsQuery.where,
					emailDeliveryStatus: "failed",
				},
			}),
		]);

		// Get audit logs for bulk email operations
		const bulkEmailLogs = await prisma.auditLog.findMany({
			where: {
				action: "BULK_EMAIL_SENT",
				createdAt: {
					gte: defaultStartDate,
					lte: defaultEndDate,
				},
			},
		});

		// Calculate totals including bulk emails
		const bulkEmailStats = bulkEmailLogs.reduce(
			(acc, log) => {
				const details = log.details as Record<string, any>;
				acc.sent += details?.sent || 0;
				acc.failed += details?.failed || 0;
				return acc;
			},
			{ sent: 0, failed: 0 },
		);

		const totalSent = sentEmails + bulkEmailStats.sent;
		const totalFailed = failedEmails + bulkEmailStats.failed;
		const totalDelivered = totalSent; // Assuming sent = delivered for simplicity
		const totalBounced = 0; // Would need bounce tracking in real implementation

		const deliveryRate = totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0;
		const bounceRate = totalSent > 0 ? (totalBounced / totalSent) * 100 : 0;

		// Generate recent activity (last 7 days)
		const recentActivity = [];
		for (let i = 6; i >= 0; i--) {
			const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
			const dateStart = new Date(
				date.getFullYear(),
				date.getMonth(),
				date.getDate(),
			);
			const dateEnd = new Date(dateStart.getTime() + 24 * 60 * 60 * 1000);

			const [daySent, dayFailed] = await Promise.all([
				prisma.license.count({
					where: {
						emailSentAt: {
							gte: dateStart,
							lt: dateEnd,
						},
						emailDeliveryStatus: "sent",
					},
				}),
				prisma.license.count({
					where: {
						emailSentAt: {
							gte: dateStart,
							lt: dateEnd,
						},
						emailDeliveryStatus: "failed",
					},
				}),
			]);

			recentActivity.push({
				date: dateStart.toISOString().split("T")[0],
				sent: daySent,
				delivered: daySent, // Assuming sent = delivered
				failed: dayFailed,
			});
		}

		const stats: EmailDeliveryStats = {
			totalSent,
			totalDelivered,
			totalFailed,
			totalBounced,
			deliveryRate: Math.round(deliveryRate * 100) / 100,
			bounceRate: Math.round(bounceRate * 100) / 100,
			recentActivity,
		};

		Logger.info(EndpointPrefix.EMAIL_STATS, "Email delivery stats retrieved", {
			body: {
				totalSent,
				totalDelivered,
				totalFailed,
				deliveryRate: stats.deliveryRate,
			},
		});

		return stats;
	} catch (error) {
		Logger.error(
			EndpointPrefix.EMAIL_STATS,
			`Failed to get email delivery stats: ${error}`,
			{ body: { startDate, endDate, templateId } },
		);
		throw error;
	}
}
