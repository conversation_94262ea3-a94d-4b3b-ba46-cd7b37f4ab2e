import { EndpointPrefix, Logger } from "@/utils/logger";
import prisma from "../../../../packages/shared/src/db/prisma";
import type {
	PaymentIntent,
	WebhookEvent,
} from "../../../../packages/shared/src/db/prisma/generated/client";
import type { PaymentType } from "../../../../packages/shared/src/db/prisma/generated/enums";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface CreatePaymentIntentOptions {
	stripePaymentIntentId: string;
	stripeCheckoutSessionId?: string;
	amount: number;
	currency?: string;
	paymentType: PaymentType;
	customerEmail: string;
	customerName?: string | null;
}

export interface ProcessWebhookOptions {
	stripeEventId: string;
	eventType: string;
	paymentIntentId?: string;
	checkoutSessionData?: any;
}

export interface ProcessWebhookResult {
	success: boolean;
	processed: boolean;
	error?: string;
}

// ============================================================================
// PAYMENT INTENT MANAGEMENT
// ============================================================================

/**
 * Create or update PaymentIntent record
 */
export async function createPaymentIntent(
	options: CreatePaymentIntentOptions,
): Promise<PaymentIntent> {
	try {
		Logger.info(
			EndpointPrefix.PAYMENT_CREATE_INTENT,
			`Creating PaymentIntent record: ${options.stripePaymentIntentId}`,
			{
				body: {
					stripePaymentIntentId: options.stripePaymentIntentId,
					amount: options.amount,
					paymentType: options.paymentType,
					customerEmail: options.customerEmail,
				},
			},
		);

		// Check if PaymentIntent already exists
		const existingPaymentIntent = await prisma.paymentIntent.findUnique({
			where: { stripePaymentIntentId: options.stripePaymentIntentId },
		});

		if (existingPaymentIntent) {
			Logger.info(
				EndpointPrefix.PAYMENT_CREATE_INTENT,
				`PaymentIntent already exists: ${options.stripePaymentIntentId}`,
				{ body: { paymentIntentId: existingPaymentIntent.id } },
			);
			return existingPaymentIntent;
		}

		// Create new PaymentIntent record
		const paymentIntent = await prisma.paymentIntent.create({
			data: {
				stripePaymentIntentId: options.stripePaymentIntentId,
				stripeCheckoutSessionId: options.stripeCheckoutSessionId,
				amount: options.amount,
				currency: options.currency || "usd",
				status: "PENDING",
				paymentType: options.paymentType,
				customerEmail: options.customerEmail.toLowerCase(),
				customerName: options.customerName,
			},
		});

		Logger.info(
			EndpointPrefix.PAYMENT_CREATE_INTENT,
			`PaymentIntent created: ${paymentIntent.id}`,
			{
				body: {
					paymentIntentId: paymentIntent.id,
					stripePaymentIntentId: options.stripePaymentIntentId,
				},
			},
		);

		return paymentIntent;
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_CREATE_INTENT,
			`Failed to create PaymentIntent: ${error}`,
			{
				body: {
					stripePaymentIntentId: options.stripePaymentIntentId,
					customerEmail: options.customerEmail,
				},
			},
		);
		throw error;
	}
}

/**
 * Update PaymentIntent status
 */
export async function updatePaymentIntentStatus(
	stripePaymentIntentId: string,
	status:
		| "PENDING"
		| "PROCESSING"
		| "SUCCEEDED"
		| "FAILED"
		| "CANCELLED"
		| "REFUNDED",
): Promise<PaymentIntent | null> {
	try {
		Logger.info(
			EndpointPrefix.PAYMENT_STATUS,
			`Updating PaymentIntent status: ${stripePaymentIntentId}`,
			{ body: { stripePaymentIntentId, status } },
		);

		const paymentIntent = await prisma.paymentIntent.update({
			where: { stripePaymentIntentId },
			data: {
				status,
				processedAt: status === "SUCCEEDED" ? new Date() : undefined,
			},
		});

		Logger.info(
			EndpointPrefix.PAYMENT_STATUS,
			`PaymentIntent status updated: ${paymentIntent.id}`,
			{ body: { paymentIntentId: paymentIntent.id, status } },
		);

		return paymentIntent;
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_STATUS,
			`Failed to update PaymentIntent status: ${error}`,
			{ body: { stripePaymentIntentId, status } },
		);
		return null;
	}
}

// ============================================================================
// WEBHOOK EVENT PROCESSING
// ============================================================================

/**
 * Process webhook event with idempotency
 */
export async function processWebhookEvent(
	options: ProcessWebhookOptions,
): Promise<ProcessWebhookResult> {
	try {
		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Processing webhook event: ${options.stripeEventId}`,
			{
				body: {
					stripeEventId: options.stripeEventId,
					eventType: options.eventType,
				},
			},
		);

		// Check if webhook event has already been processed (idempotency)
		const existingEvent = await prisma.webhookEvent.findUnique({
			where: { stripeEventId: options.stripeEventId },
		});

		if (existingEvent) {
			Logger.info(
				EndpointPrefix.PAYMENT_WEBHOOK,
				`Webhook event already processed: ${options.stripeEventId}`,
				{
					body: {
						webhookEventId: existingEvent.id,
						processed: existingEvent.processed,
					},
				},
			);

			return {
				success: true,
				processed: existingEvent.processed,
			};
		}

		// Create webhook event record
		const webhookEvent = await prisma.webhookEvent.create({
			data: {
				stripeEventId: options.stripeEventId,
				eventType: options.eventType,
				processed: false,
				paymentIntentId: options.paymentIntentId,
			},
		});

		// Mark webhook event as processed (business logic handled separately)
		await prisma.webhookEvent.update({
			where: { id: webhookEvent.id },
			data: {
				processed: true,
				processedAt: new Date(),
			},
		});

		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Webhook event processed successfully: ${options.stripeEventId}`,
			{
				body: {
					webhookEventId: webhookEvent.id,
					eventType: options.eventType,
				},
			},
		);

		return {
			success: true,
			processed: true,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Failed to process webhook event: ${error}`,
			{
				body: {
					stripeEventId: options.stripeEventId,
					eventType: options.eventType,
				},
			},
		);

		// Mark webhook event as failed
		try {
			await prisma.webhookEvent.updateMany({
				where: { stripeEventId: options.stripeEventId },
				data: {
					errorMessage: String(error),
					retryCount: { increment: 1 },
				},
			});
		} catch (updateError) {
			Logger.error(
				EndpointPrefix.PAYMENT_WEBHOOK,
				`Failed to update webhook event error: ${updateError}`,
			);
		}

		return {
			success: false,
			processed: false,
			error: String(error),
		};
	}
}

// ============================================================================
// LOOKUP FUNCTIONS
// ============================================================================

/**
 * Get PaymentIntent by Stripe ID
 */
export async function getPaymentIntentByStripeId(
	stripePaymentIntentId: string,
): Promise<PaymentIntent | null> {
	try {
		const paymentIntent = await prisma.paymentIntent.findUnique({
			where: { stripePaymentIntentId },
			include: {
				licenses: true,
				deviceExpansions: true,
				webhookEvents: true,
			},
		});

		return paymentIntent;
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_STATUS,
			`Failed to get PaymentIntent: ${error}`,
			{ body: { stripePaymentIntentId } },
		);
		return null;
	}
}

/**
 * Get WebhookEvent by Stripe event ID
 */
export async function getWebhookEventByStripeId(
	stripeEventId: string,
): Promise<WebhookEvent | null> {
	try {
		const webhookEvent = await prisma.webhookEvent.findUnique({
			where: { stripeEventId },
			include: {
				paymentIntent: true,
			},
		});

		return webhookEvent;
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Failed to get WebhookEvent: ${error}`,
			{ body: { stripeEventId } },
		);
		return null;
	}
}
