import { EndpointPrefix, Logger } from "@/utils/logger";
import {
	prepareDatabaseQuery,
	type StandardPaginationParams,
} from "@/utils/pagination";
import prisma from "../../../../packages/shared/src/db/prisma";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface WebhookEventDetails {
	id: string;
	stripeEventId: string;
	eventType: string;
	processed: boolean;
	processedAt?: Date;
	errorMessage?: string;
	retryCount: number;
	createdAt: Date;
	paymentIntentId?: string;
	paymentIntent?: {
		customerEmail: string;
		amount: number;
		status: string;
	};
}

export interface WebhookStats {
	totalEvents: number;
	processedEvents: number;
	failedEvents: number;
	pendingEvents: number;
	successRate: number;
	averageProcessingTime: number;
	eventsByType: Record<string, number>;
	recentErrors: Array<{
		eventId: string;
		eventType: string;
		errorMessage: string;
		createdAt: Date;
	}>;
}

// ============================================================================
// WEBHOOK MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Get webhook events with pagination and filtering
 */
export async function getWebhookEvents(
	params: StandardPaginationParams & {
		eventType?: string;
		processed?: boolean;
		startDate?: Date;
		endDate?: Date;
	} = { page: 1, limit: 20 },
): Promise<{
	webhookEvents: WebhookEventDetails[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}> {
	try {
		Logger.info(
			EndpointPrefix.WEBHOOK_MANAGEMENT,
			"Retrieving webhook events",
			{
				body: {
					page: params.page,
					limit: params.limit,
					eventType: params.eventType,
					processed: params.processed,
					startDate: params.startDate,
					endDate: params.endDate,
				},
			},
		);

		// Build where clause
		const whereClause: any = {};

		if (params.eventType) {
			whereClause.eventType = params.eventType;
		}

		if (params.processed !== undefined) {
			whereClause.processed = params.processed;
		}

		if (params.startDate || params.endDate) {
			whereClause.createdAt = {};
			if (params.startDate) {
				whereClause.createdAt.gte = params.startDate;
			}
			if (params.endDate) {
				whereClause.createdAt.lte = params.endDate;
			}
		}

		// Get total count
		const total = await prisma.webhookEvent.count({
			where: whereClause,
		});

		// Get paginated results
		const { skip, take } = prepareDatabaseQuery(
			params,
			[], // No search fields for webhook events
			whereClause,
		);

		const webhookEvents = await prisma.webhookEvent.findMany({
			where: whereClause,
			include: {
				paymentIntent: {
					select: {
						customerEmail: true,
						amount: true,
						status: true,
					},
				},
			},
			orderBy: {
				createdAt: "desc",
			},
			skip,
			take,
		});

		const formattedEvents: WebhookEventDetails[] = webhookEvents.map(
			(event) => ({
				id: event.id,
				stripeEventId: event.stripeEventId,
				eventType: event.eventType,
				processed: event.processed,
				processedAt: event.processedAt || undefined,
				errorMessage: event.errorMessage || undefined,
				retryCount: event.retryCount,
				createdAt: event.createdAt,
				paymentIntentId: event.paymentIntentId || undefined,
				paymentIntent: event.paymentIntent || undefined,
			}),
		);

		Logger.info(
			EndpointPrefix.WEBHOOK_MANAGEMENT,
			`Webhook events retrieved: ${formattedEvents.length} of ${total}`,
			{
				body: {
					page: params.page,
					limit: params.limit,
					total,
					filtered: formattedEvents.length,
				},
			},
		);

		return {
			webhookEvents: formattedEvents,
			pagination: {
				page: params.page,
				limit: params.limit,
				total,
				totalPages: Math.ceil(total / params.limit),
			},
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.WEBHOOK_MANAGEMENT,
			`Failed to get webhook events: ${error}`,
			{ body: { params } },
		);
		throw error;
	}
}

/**
 * Retry failed webhook processing
 */
export async function retryWebhookProcessing(
	webhookEventId: string,
	reason?: string,
	actorId?: string,
): Promise<{ success: boolean; error?: string }> {
	try {
		Logger.info(
			EndpointPrefix.WEBHOOK_MANAGEMENT,
			`Retrying webhook processing: ${webhookEventId}`,
			{
				body: {
					webhookEventId,
					reason,
					actorId,
				},
			},
		);

		// Get webhook event
		const webhookEvent = await prisma.webhookEvent.findUnique({
			where: { id: webhookEventId },
			include: {
				paymentIntent: true,
			},
		});

		if (!webhookEvent) {
			return { success: false, error: "Webhook event not found" };
		}

		if (webhookEvent.processed) {
			return {
				success: false,
				error: "Webhook event already processed successfully",
			};
		}

		// Reset webhook event for retry
		await prisma.webhookEvent.update({
			where: { id: webhookEventId },
			data: {
				errorMessage: null,
				retryCount: { increment: 1 },
			},
		});

		// Create audit log for retry action
		if (actorId) {
			await prisma.auditLog.create({
				data: {
					action: "WEBHOOK_PROCESSED",
					actorId,
					details: {
						webhookEventId,
						stripeEventId: webhookEvent.stripeEventId,
						eventType: webhookEvent.eventType,
						action: "RETRY",
						reason,
						retryCount: webhookEvent.retryCount + 1,
					},
				},
			});
		}

		Logger.info(
			EndpointPrefix.WEBHOOK_MANAGEMENT,
			`Webhook retry initiated: ${webhookEventId}`,
			{
				body: {
					webhookEventId,
					stripeEventId: webhookEvent.stripeEventId,
					retryCount: webhookEvent.retryCount + 1,
					actorId,
				},
			},
		);

		// Note: In a real implementation, you would trigger the actual webhook reprocessing
		// This could involve calling the webhook processing function again or queuing it

		return { success: true };
	} catch (error) {
		Logger.error(
			EndpointPrefix.WEBHOOK_MANAGEMENT,
			`Failed to retry webhook processing: ${error}`,
			{ body: { webhookEventId, reason, actorId } },
		);
		return { success: false, error: "Failed to retry webhook processing" };
	}
}

/**
 * Get webhook processing statistics
 */
export async function getWebhookStats(
	period: "24h" | "7d" | "30d" | "90d" = "7d",
	eventType?: string,
): Promise<WebhookStats> {
	try {
		Logger.info(
			EndpointPrefix.WEBHOOK_MANAGEMENT,
			`Retrieving webhook statistics for period: ${period}`,
			{
				body: {
					period,
					eventType,
				},
			},
		);

		// Calculate date range
		const now = new Date();
		const startDate = new Date();

		switch (period) {
			case "24h":
				startDate.setHours(now.getHours() - 24);
				break;
			case "7d":
				startDate.setDate(now.getDate() - 7);
				break;
			case "30d":
				startDate.setDate(now.getDate() - 30);
				break;
			case "90d":
				startDate.setDate(now.getDate() - 90);
				break;
		}

		// Build where clause
		const whereClause: any = {
			createdAt: {
				gte: startDate,
				lte: now,
			},
		};

		if (eventType) {
			whereClause.eventType = eventType;
		}

		// Get basic counts
		const [totalEvents, processedEvents, failedEvents] = await Promise.all([
			prisma.webhookEvent.count({ where: whereClause }),
			prisma.webhookEvent.count({ where: { ...whereClause, processed: true } }),
			prisma.webhookEvent.count({
				where: {
					...whereClause,
					processed: false,
					errorMessage: { not: null },
				},
			}),
		]);

		const pendingEvents = totalEvents - processedEvents - failedEvents;
		const successRate =
			totalEvents > 0 ? (processedEvents / totalEvents) * 100 : 0;

		// Get events by type
		const eventsByTypeRaw = await prisma.webhookEvent.groupBy({
			by: ["eventType"],
			where: whereClause,
			_count: {
				eventType: true,
			},
		});

		const eventsByType: Record<string, number> = {};
		for (const item of eventsByTypeRaw) {
			eventsByType[item.eventType] = item._count.eventType;
		}

		// Get recent errors
		const recentErrors = await prisma.webhookEvent.findMany({
			where: {
				...whereClause,
				processed: false,
				errorMessage: { not: null },
			},
			select: {
				id: true,
				eventType: true,
				errorMessage: true,
				createdAt: true,
			},
			orderBy: {
				createdAt: "desc",
			},
			take: 10,
		});

		// Calculate average processing time (mock calculation)
		const averageProcessingTime = 2.5; // seconds (would be calculated from actual data)

		const stats: WebhookStats = {
			totalEvents,
			processedEvents,
			failedEvents,
			pendingEvents,
			successRate: Math.round(successRate * 100) / 100,
			averageProcessingTime,
			eventsByType,
			recentErrors: recentErrors.map((error) => ({
				eventId: error.id,
				eventType: error.eventType,
				errorMessage: error.errorMessage || "Unknown error",
				createdAt: error.createdAt,
			})),
		};

		Logger.info(
			EndpointPrefix.WEBHOOK_MANAGEMENT,
			`Webhook statistics retrieved for period: ${period}`,
			{
				body: {
					period,
					totalEvents,
					successRate: stats.successRate,
					failedEvents,
				},
			},
		);

		return stats;
	} catch (error) {
		Logger.error(
			EndpointPrefix.WEBHOOK_MANAGEMENT,
			`Failed to get webhook statistics: ${error}`,
			{ body: { period, eventType } },
		);
		throw error;
	}
}
