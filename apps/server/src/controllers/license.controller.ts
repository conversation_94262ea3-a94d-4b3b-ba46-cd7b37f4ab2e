import type { DeviceMetadata } from "@/controllers/device.controller";
import { sendLicenseEmail } from "@/templates";
import { generateLicenseKey, hashDeviceId } from "@/utils";
import { EndpointPrefix, Logger } from "@/utils/logger";
import {
	prepareDatabaseQuery,
	type StandardPaginationParams,
} from "@/utils/pagination";
import prisma from "../../../../packages/shared/src/db/prisma";
import type { License } from "../../../../packages/shared/src/db/prisma/generated/client";
import type { LicenseType } from "../../../../packages/shared/src/db/prisma/generated/enums";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Get all licenses with pagination and filtering (using enhanced utilities)
 */
export async function getLicenses(
	params: StandardPaginationParams,
): Promise<{ licenses: any[]; total: number }> {
	try {
		// Use enhanced pagination utilities - eliminates manual calculations
		const { skip, take, where, orderBy } = prepareDatabaseQuery(
			params,
			["licenseKey", "customerEmail", "customerName"], // searchFields for license search
			{
				// Additional filters from params.filters
				...(params.filters?.status && { status: params.filters.status }),
				...(params.filters?.licenseType && {
					licenseType: params.filters.licenseType,
				}),
			},
		);

		const [licenses, total] = await Promise.all([
			prisma.license.findMany({
				where,
				skip,
				take,
				orderBy,
				include: {
					devices: {
						where: { status: "ACTIVE" },
						select: {
							id: true,
							deviceName: true,
							deviceType: true,
							firstSeen: true,
							lastSeen: true,
							status: true,
						},
					},
					deviceExpansions: {
						select: {
							id: true,
							additionalDevices: true,
							createdAt: true,
							paymentIntentId: true,
						},
					},
					paymentIntent: {
						select: {
							id: true,
							stripePaymentIntentId: true,
							amount: true,
							currency: true,
							status: true,
						},
					},
				},
			}),
			prisma.license.count({ where }),
		]);

		return { licenses, total };
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_LIST,
			`Failed to get licenses: ${error}`,
			{
				body: {
					...params,
				},
			},
		);
		return { licenses: [], total: 0 };
	}
}

// ============================================================================
// ENHANCED LICENSE MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Delete/deactivate a license
 */
export async function deleteLicense(
	licenseId: string,
	actorId: string,
): Promise<{ success: boolean; error?: string }> {
	try {
		const license = await prisma.license.findUnique({
			where: { id: licenseId },
		});

		if (!license) {
			return { success: false, error: "License not found" };
		}

		// Soft delete by updating status
		await prisma.license.update({
			where: { id: licenseId },
			data: {
				status: "CANCELLED",
				updatedAt: new Date(),
			},
		});

		// Create audit log
		await prisma.auditLog.create({
			data: {
				action: "LICENSE_CANCELLED", // Type assertion needed due to enum mismatch
				actorId,
				targetId: licenseId,
				licenseKey: license.licenseKey,
				details: {
					previousStatus: license.status,
					reason: "Admin cancellation",
				},
			},
		});

		Logger.info(
			EndpointPrefix.LICENSE_DELETE,
			`License cancelled: ${license.licenseKey}`,
			{ body: { licenseId, actorId } },
		);

		return { success: true };
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_DELETE,
			`Failed to delete license: ${error}`,
			{ body: { licenseId, actorId } },
		);
		return { success: false, error: "Failed to delete license" };
	}
}

/**
 * Extend license expiry
 */
export async function extendLicense(
	licenseId: string,
	extensionDays: number,
	reason: string | undefined,
	actorId: string,
): Promise<{ success: boolean; error?: string; newExpiryDate?: Date }> {
	try {
		const license = await prisma.license.findUnique({
			where: { id: licenseId },
		});

		if (!license) {
			return { success: false, error: "License not found" };
		}

		// Calculate new expiry date
		const currentExpiry = license.expiresAt || new Date();
		const newExpiryDate = new Date(
			currentExpiry.getTime() + extensionDays * 24 * 60 * 60 * 1000,
		);

		// Update license
		await prisma.license.update({
			where: { id: licenseId },
			data: {
				expiresAt: newExpiryDate,
				updatedAt: new Date(),
			},
		});

		// Create audit log
		await prisma.auditLog.create({
			data: {
				action: "LICENSE_EXTENDED", // Type assertion needed due to enum mismatch
				actorId,
				targetId: licenseId,
				licenseKey: license.licenseKey,
				details: {
					extensionDays,
					previousExpiry: license.expiresAt,
					newExpiry: newExpiryDate,
					reason: reason || "Admin extension",
				},
			},
		});

		Logger.info(
			EndpointPrefix.LICENSE_UPDATE,
			`License extended: ${license.licenseKey} by ${extensionDays} days`,
			{ body: { licenseId, extensionDays, newExpiryDate } },
		);

		return { success: true, newExpiryDate };
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_UPDATE,
			`Failed to extend license: ${error}`,
			{ body: { licenseId, extensionDays, actorId } },
		);
		return { success: false, error: "Failed to extend license" };
	}
}

/**
 * Suspend a license
 */
export async function suspendLicense(
	licenseId: string,
	reason: string,
	suspendUntil: Date | undefined,
	actorId: string,
): Promise<{ success: boolean; error?: string }> {
	try {
		const license = await prisma.license.findUnique({
			where: { id: licenseId },
		});

		if (!license) {
			return { success: false, error: "License not found" };
		}

		// Update license status
		await prisma.license.update({
			where: { id: licenseId },
			data: {
				status: "SUSPENDED",
				updatedAt: new Date(),
			},
		});

		// Create audit log
		await prisma.auditLog.create({
			data: {
				action: "LICENSE_SUSPENDED", // Type assertion needed due to enum mismatch
				actorId,
				targetId: licenseId,
				licenseKey: license.licenseKey,
				details: {
					reason,
					suspendUntil,
					previousStatus: license.status,
				},
			},
		});

		Logger.info(
			EndpointPrefix.LICENSE_SUSPEND,
			`License suspended: ${license.licenseKey}`,
			{ body: { licenseId, reason, suspendUntil } },
		);

		return { success: true };
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_SUSPEND,
			`Failed to suspend license: ${error}`,
			{ body: { licenseId, reason, actorId } },
		);
		return { success: false, error: "Failed to suspend license" };
	}
}

/**
 * Reactivate a suspended license
 */
export async function reactivateLicense(
	licenseId: string,
	reason: string | undefined,
	actorId: string,
): Promise<{ success: boolean; error?: string }> {
	try {
		const license = await prisma.license.findUnique({
			where: { id: licenseId },
		});

		if (!license) {
			return { success: false, error: "License not found" };
		}

		// Update license status
		await prisma.license.update({
			where: { id: licenseId },
			data: {
				status: "ACTIVE",
				updatedAt: new Date(),
			},
		});

		// Create audit log
		await prisma.auditLog.create({
			data: {
				action: "LICENSE_REACTIVATED", // Type assertion needed due to enum mismatch
				actorId,
				targetId: licenseId,
				licenseKey: license.licenseKey,
				details: {
					reason: reason || "Admin reactivation",
					previousStatus: license.status,
				},
			},
		});

		Logger.info(
			EndpointPrefix.LICENSE_REACTIVATE,
			`License reactivated: ${license.licenseKey}`,
			{ body: { licenseId, reason } },
		);

		return { success: true };
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_REACTIVATE,
			`Failed to reactivate license: ${error}`,
			{ body: { licenseId, reason, actorId } },
		);
		return { success: false, error: "Failed to reactivate license" };
	}
}

export interface CreateLicenseOptions {
	email: string;
	licenseType: LicenseType;
	deviceId?: string;
	paymentIntentId?: string;
	trialDurationDays?: number;
	createdBy?: string; // Admin user ID who created this license
}

export interface CreateLicenseFromWebhookOptions {
	email: string;
	licenseType: LicenseType;
	deviceId?: string;
	stripePaymentIntentId: string;
	webhookEventId: string;
	amount: number;
	customerName?: string;
}

export interface CreateLicenseResult {
	success: boolean;
	license?: License;
	error?: string;
	existingLicense?: License;
}

export interface ValidateLicenseOptions {
	licenseKey: string;
	deviceId: string;
	appVersion?: string;
	deviceMetadata?: DeviceMetadata;
}

export interface ValidateLicenseResult {
	success: boolean;
	license?: {
		id: string;
		licenseKey: string;
		licenseType: string;
		status: string;
		maxDevices: number;
		usedDevices: number;
		expiresAt: Date | null;
		customerEmail: string;
	};
	device?: {
		id: string;
		firstSeen: Date;
		lastSeen: Date;
		status: string;
	};
	error?: string;
	errorCode?: string;
}

// ============================================================================
// LICENSE CREATION
// ============================================================================

/**
 * Create a new license with the new normalized schema
 */
export async function createLicense(
	options: CreateLicenseOptions,
): Promise<CreateLicenseResult> {
	try {
		Logger.info(
			EndpointPrefix.LICENSE_CREATE,
			`Creating ${options.licenseType} license for: ${options.email}`,
			{ body: { licenseType: options.licenseType, email: options.email } },
		);

		// STRICT ONE-LICENSE-PER-EMAIL POLICY
		const existingLicense = await prisma.license.findFirst({
			where: {
				customerEmail: options.email.toLowerCase(),
				status: "ACTIVE",
			},
		});

		if (existingLicense) {
			Logger.warn(
				EndpointPrefix.LICENSE_CREATE,
				`License already exists for email: ${options.email}`,
				{ body: { existingLicenseId: existingLicense.id } },
			);

			return {
				success: false,
				error: "A license already exists for this email address",
				existingLicense,
			};
		}

		// Generate unique license key
		const licenseKey = generateLicenseKey();

		// Calculate device limits based on license type
		const maxDevices = options.licenseType === "TRIAL" ? 1 : 2;

		// Calculate expiration date for trial licenses
		let expiresAt: Date | null = null;
		if (options.licenseType === "TRIAL") {
			const trialDays = options.trialDurationDays || 14;
			expiresAt = new Date();
			expiresAt.setDate(expiresAt.getDate() + trialDays);
		}

		// Create license with new schema structure
		const license = await prisma.license.create({
			data: {
				licenseKey,
				customerEmail: options.email.toLowerCase(),
				licenseType: options.licenseType,
				status: "ACTIVE",
				maxDevices,
				usedDevices: 0,
				expiresAt,
				paymentIntentId: options.paymentIntentId,
				createdBy: options.createdBy,
				activatedAt: options.deviceId ? new Date() : null,
				createdAt: new Date(),
			},
		});

		// Register initial device if provided
		if (options.deviceId) {
			const { hash: deviceHash, salt } = hashDeviceId(options.deviceId);

			await prisma.device.create({
				data: {
					licenseId: license.id,
					deviceHash,
					salt,
					firstSeen: new Date(),
					lastSeen: new Date(),
					status: "ACTIVE",
				},
			});

			// Update used devices count
			await prisma.license.update({
				where: { id: license.id },
				data: { usedDevices: 1 },
			});

			Logger.info(
				EndpointPrefix.LICENSE_CREATE,
				`Initial device registered for license: ${licenseKey}`,
				{ body: { licenseId: license.id, deviceHash } },
			);
		}

		// Send license email for non-webhook created licenses
		if (!options.paymentIntentId) {
			try {
				await sendLicenseEmail(
					options.email,
					licenseKey,
					options.licenseType,
					expiresAt,
				);

				// Update email delivery status
				await prisma.license.update({
					where: { id: license.id },
					data: {
						emailSentAt: new Date(),
						emailDeliveryStatus: "sent",
					},
				});

				Logger.info(
					EndpointPrefix.LICENSE_CREATE,
					`License email sent: ${options.email}`,
					{ body: { licenseId: license.id } },
				);
			} catch (emailError) {
				Logger.error(
					EndpointPrefix.LICENSE_CREATE,
					`Failed to send license email: ${emailError}`,
					{ body: { licenseId: license.id, email: options.email } },
				);

				// Update email delivery status
				await prisma.license.update({
					where: { id: license.id },
					data: { emailDeliveryStatus: "failed" },
				});
			}
		}

		Logger.info(
			EndpointPrefix.LICENSE_CREATE,
			`License created successfully: ${licenseKey}`,
			{ body: { licenseId: license.id, licenseType: options.licenseType } },
		);

		return { success: true, license };
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_CREATE,
			`Failed to create license: ${error}`,
			{ body: { email: options.email, licenseType: options.licenseType } },
		);

		return { success: false, error: "Failed to create license" };
	}
}

// ============================================================================
// WEBHOOK LICENSE CREATION
// ============================================================================

/**
 * Create license from webhook with PaymentIntent integration
 */
export async function createLicenseFromWebhook(
	options: CreateLicenseFromWebhookOptions,
): Promise<CreateLicenseResult> {
	try {
		Logger.info(
			EndpointPrefix.LICENSE_CREATE,
			`Creating license from webhook for: ${options.email}`,
			{
				body: {
					email: options.email,
					stripePaymentIntentId: options.stripePaymentIntentId,
					webhookEventId: options.webhookEventId,
				},
			},
		);

		// Check for existing license for this email
		const existingEmailLicense = await prisma.license.findFirst({
			where: {
				customerEmail: options.email.toLowerCase(),
				status: "ACTIVE",
			},
		});

		if (existingEmailLicense) {
			Logger.warn(
				EndpointPrefix.LICENSE_CREATE,
				`License already exists for email: ${options.email}`,
				{ body: { existingLicenseId: existingEmailLicense.id } },
			);

			return {
				success: false,
				error: "A license already exists for this email address",
				existingLicense: existingEmailLicense,
			};
		}

		// Find or create PaymentIntent record
		let paymentIntent = await prisma.paymentIntent.findUnique({
			where: { stripePaymentIntentId: options.stripePaymentIntentId },
		});

		if (!paymentIntent) {
			paymentIntent = await prisma.paymentIntent.create({
				data: {
					stripePaymentIntentId: options.stripePaymentIntentId,
					amount: options.amount,
					currency: "usd",
					status: "SUCCEEDED",
					paymentType: "LICENSE_PURCHASE",
					customerEmail: options.email.toLowerCase(),
					customerName: options.customerName,
					processedAt: new Date(),
				},
			});
		}

		// Create WebhookEvent record for idempotency
		await prisma.webhookEvent.create({
			data: {
				stripeEventId: options.webhookEventId,
				eventType: "checkout.session.completed",
				processed: true,
				processedAt: new Date(),
				paymentIntentId: paymentIntent.id,
			},
		});

		// Generate license key and create license
		const licenseKey = generateLicenseKey();
		const maxDevices = 2; // Pro licenses get 2 devices

		const license = await prisma.license.create({
			data: {
				licenseKey,
				customerEmail: options.email.toLowerCase(),
				customerName: options.customerName,
				licenseType: options.licenseType,
				status: "ACTIVE",
				maxDevices,
				usedDevices: 0,
				paymentIntentId: paymentIntent.id,
				totalPaidAmount: options.amount,
				activatedAt: options.deviceId ? new Date() : null,
				createdAt: new Date(),
			},
		});

		// Register initial device if provided
		if (options.deviceId) {
			const { hash: deviceHash, salt } = hashDeviceId(options.deviceId);

			await prisma.device.create({
				data: {
					licenseId: license.id,
					deviceHash,
					salt,
					firstSeen: new Date(),
					lastSeen: new Date(),
					status: "ACTIVE",
				},
			});

			// Update used devices count
			await prisma.license.update({
				where: { id: license.id },
				data: { usedDevices: 1 },
			});
		}

		Logger.info(
			EndpointPrefix.LICENSE_CREATE,
			`License created from webhook: ${licenseKey}`,
			{ body: { licenseId: license.id, paymentIntentId: paymentIntent.id } },
		);

		return { success: true, license };
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_CREATE,
			`Failed to create license from webhook: ${error}`,
			{
				body: {
					email: options.email,
					stripePaymentIntentId: options.stripePaymentIntentId,
				},
			},
		);

		return { success: false, error: "Failed to create license from webhook" };
	}
}

// ============================================================================
// LICENSE VALIDATION & DEVICE REGISTRATION
// ============================================================================

/**
 * Validate license and register/update device with new schema
 */
export async function validateLicense(
	options: ValidateLicenseOptions,
): Promise<ValidateLicenseResult> {
	try {
		Logger.info(
			EndpointPrefix.LICENSE_VALIDATE,
			`Validating license: ${options.licenseKey}`,
			{ body: { licenseKey: options.licenseKey } },
		);

		// Find license with devices and payment info
		const license = await prisma.license.findUnique({
			where: { licenseKey: options.licenseKey },
			include: {
				devices: {
					where: { status: "ACTIVE" },
				},
				paymentIntent: true,
				deviceExpansions: true,
			},
		});

		if (!license) {
			Logger.warn(
				EndpointPrefix.LICENSE_VALIDATE,
				`License not found: ${options.licenseKey}`,
			);
			return {
				success: false,
				error: "License not found",
				errorCode: "LICENSE_NOT_FOUND",
			};
		}

		// Check license status
		if (license.status !== "ACTIVE") {
			Logger.warn(
				EndpointPrefix.LICENSE_VALIDATE,
				`License not active: ${options.licenseKey}`,
				{ body: { status: license.status } },
			);
			return {
				success: false,
				error: `License is ${license.status.toLowerCase()}`,
				errorCode: "LICENSE_NOT_ACTIVE",
			};
		}

		// Check expiration for trial licenses
		if (license.expiresAt && license.expiresAt < new Date()) {
			Logger.warn(
				EndpointPrefix.LICENSE_VALIDATE,
				`License expired: ${options.licenseKey}`,
				{ body: { expiresAt: license.expiresAt } },
			);

			// Update license status to expired
			await prisma.license.update({
				where: { id: license.id },
				data: { status: "EXPIRED" },
			});

			return {
				success: false,
				error: "License has expired",
				errorCode: "LICENSE_EXPIRED",
			};
		}

		// Hash the device ID for comparison
		const { hash: deviceHash } = hashDeviceId(options.deviceId);

		// Check if device is already registered
		let device = await prisma.device.findFirst({
			where: {
				licenseId: license.id,
				deviceHash,
				status: "ACTIVE",
			},
		});

		if (device) {
			// Update existing device
			device = await prisma.device.update({
				where: { id: device.id },
				data: {
					lastSeen: new Date(),
					appVersion: options.appVersion,
					...options.deviceMetadata,
				},
			});

			Logger.info(
				EndpointPrefix.LICENSE_VALIDATE,
				`Device updated: ${options.licenseKey}`,
				{ body: { deviceId: device.id } },
			);
		} else {
			// Check device limit
			const activeDeviceCount = license.devices.length;
			if (activeDeviceCount >= license.maxDevices) {
				Logger.warn(
					EndpointPrefix.LICENSE_VALIDATE,
					`Device limit exceeded: ${options.licenseKey}`,
					{
						body: {
							activeDevices: activeDeviceCount,
							maxDevices: license.maxDevices,
						},
					},
				);

				return {
					success: false,
					error: `Device limit exceeded. Maximum ${license.maxDevices} devices allowed.`,
					errorCode: "DEVICE_LIMIT_EXCEEDED",
				};
			}

			// Register new device
			const { salt } = hashDeviceId(options.deviceId);
			device = await prisma.device.create({
				data: {
					licenseId: license.id,
					deviceHash,
					salt,
					firstSeen: new Date(),
					lastSeen: new Date(),
					status: "ACTIVE",
					appVersion: options.appVersion,
					...options.deviceMetadata,
				},
			});

			// Update license used devices count and activation status
			await prisma.license.update({
				where: { id: license.id },
				data: {
					usedDevices: activeDeviceCount + 1,
					activatedAt: license.activatedAt || new Date(),
				},
			});

			Logger.info(
				EndpointPrefix.LICENSE_VALIDATE,
				`New device registered: ${options.licenseKey}`,
				{ body: { deviceId: device.id, deviceCount: activeDeviceCount + 1 } },
			);
		}

		Logger.info(
			EndpointPrefix.LICENSE_VALIDATE,
			`License validation successful: ${options.licenseKey}`,
			{ body: { licenseId: license.id, deviceId: device.id } },
		);

		return {
			success: true,
			license: {
				id: license.id,
				licenseKey: license.licenseKey,
				licenseType: license.licenseType,
				status: license.status,
				maxDevices: license.maxDevices,
				usedDevices: license.usedDevices,
				expiresAt: license.expiresAt,
				customerEmail: license.customerEmail,
			},
			device: {
				id: device.id,
				firstSeen: device.firstSeen,
				lastSeen: device.lastSeen,
				status: device.status,
			},
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_VALIDATE,
			`License validation failed: ${error}`,
			{ body: { licenseKey: options.licenseKey } },
		);

		return {
			success: false,
			error: "License validation failed",
			errorCode: "VALIDATION_ERROR",
		};
	}
}

// ============================================================================
// LICENSE LOOKUP FUNCTIONS
// ============================================================================

/**
 * Get license by email
 */
export async function getLicenseByEmail(email: string) {
	try {
		const license = await prisma.license.findFirst({
			where: {
				customerEmail: email.toLowerCase(),
				status: "ACTIVE",
			},
			include: {
				devices: {
					where: { status: "ACTIVE" },
				},
				deviceExpansions: true,
				paymentIntent: true,
			},
		});

		return license;
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_STATUS,
			`Failed to get license by email: ${error}`,
			{ body: { email } },
		);
		return null;
	}
}

/**
 * Get license by ID
 */

export async function getLicenseById(id: string) {
	try {
		const license = await prisma.license.findUnique({
			where: { id },
			include: {
				devices: {
					where: { status: "ACTIVE" },
				},
				deviceExpansions: true,
				paymentIntent: true,
			},
		});

		return license;
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_STATUS,
			`Failed to get license by ID: ${error}`,
			{ body: { id } },
		);
		return null;
	}
}

/**
 * Get license by key
 */
export async function getLicenseByKey(licenseKey: string) {
	try {
		const license = await prisma.license.findUnique({
			where: { licenseKey },
			include: {
				devices: {
					where: { status: "ACTIVE" },
				},
				deviceExpansions: true,
				paymentIntent: true,
			},
		});

		return license;
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_STATUS,
			`Failed to get license by key: ${error}`,
			{ body: { licenseKey } },
		);
		return null;
	}
}

// ============================================================================
// TIER UPGRADE PROCESSING
// ============================================================================

export interface TierUpgradeOptions {
	licenseId: string;
	paymentIntentId: string;
	fromTier: "PRO" | "ENTERPRISE";
	toTier: "PRO" | "ENTERPRISE";
	amount: number;
}

export interface TierUpgradeResult {
	success: boolean;
	license?: License;
	error?: string;
}

/**
 * Process license tier upgrade
 */
export async function processTierUpgrade(
	options: TierUpgradeOptions,
): Promise<TierUpgradeResult> {
	try {
		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Processing tier upgrade for license: ${options.licenseId}`,
			{
				body: {
					licenseId: options.licenseId,
					fromTier: options.fromTier,
					toTier: options.toTier,
					amount: options.amount,
				},
			},
		);

		// Get current license
		const currentLicense = await prisma.license.findUnique({
			where: { id: options.licenseId },
		});

		if (!currentLicense) {
			return {
				success: false,
				error: "License not found",
			};
		}

		// Validate upgrade path
		if (currentLicense.licenseType !== options.fromTier) {
			return {
				success: false,
				error: `License type mismatch: expected ${options.fromTier}, got ${currentLicense.licenseType}`,
			};
		}

		// Calculate new device limits based on target tier
		const deviceLimits = {
			PRO: 2,
			ENTERPRISE: 5,
		};

		const newMaxDevices = deviceLimits[options.toTier];

		// Update license with new tier and device limits
		const updatedLicense = await prisma.license.update({
			where: { id: options.licenseId },
			data: {
				licenseType: options.toTier,
				maxDevices: newMaxDevices,
				totalPaidAmount: { increment: options.amount },
				updatedAt: new Date(),
			},
		});

		// Create audit log for tier upgrade
		await prisma.auditLog.create({
			data: {
				action: "LICENSE_UPGRADED",
				licenseId: options.licenseId,
				licenseKey: currentLicense.licenseKey,
				customerEmail: currentLicense.customerEmail,
				details: {
					fromTier: options.fromTier,
					toTier: options.toTier,
					previousMaxDevices: currentLicense.maxDevices,
					newMaxDevices,
					upgradeAmount: options.amount,
					paymentIntentId: options.paymentIntentId,
				},
			},
		});

		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Tier upgrade processed successfully: ${options.licenseId}`,
			{
				body: {
					licenseId: options.licenseId,
					fromTier: options.fromTier,
					toTier: options.toTier,
					newMaxDevices,
				},
			},
		);

		return {
			success: true,
			license: updatedLicense,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Failed to process tier upgrade: ${error}`,
			{
				body: {
					licenseId: options.licenseId,
					fromTier: options.fromTier,
					toTier: options.toTier,
				},
			},
		);

		return {
			success: false,
			error: "Failed to process tier upgrade",
		};
	}
}

// ============================================================================
// WEBHOOK BUSINESS LOGIC FUNCTIONS
// ============================================================================

export interface CreateLicenseFromCheckoutOptions {
	customerEmail: string;
	customerName?: string | null;
	paymentIntentId: string;
	amount: number;
	licenseType?: LicenseType;
}

export interface CreateLicenseFromCheckoutResult {
	success: boolean;
	license?: License;
	error?: string;
}

/**
 * Create license from successful checkout (business logic only)
 * This function handles ONLY license creation, not webhook processing
 */
export async function createLicenseFromCheckout(
	options: CreateLicenseFromCheckoutOptions,
): Promise<CreateLicenseFromCheckoutResult> {
	try {
		Logger.info(
			EndpointPrefix.LICENSE_CREATE,
			`Creating license from checkout: ${options.customerEmail}`,
			{
				body: {
					customerEmail: options.customerEmail,
					paymentIntentId: options.paymentIntentId,
					amount: options.amount,
				},
			},
		);

		// Check for existing license for this email
		const existingLicense = await prisma.license.findFirst({
			where: {
				customerEmail: options.customerEmail.toLowerCase(),
				status: "ACTIVE",
			},
		});

		if (existingLicense) {
			Logger.warn(
				EndpointPrefix.LICENSE_CREATE,
				`License already exists for email: ${options.customerEmail}`,
				{ body: { existingLicenseId: existingLicense.id } },
			);

			return {
				success: false,
				error: "A license already exists for this email address",
			};
		}

		// Generate license key and create license
		const licenseKey = generateLicenseKey();
		const licenseType = options.licenseType || "PRO";
		const maxDevices = licenseType === "ENTERPRISE" ? 5 : 2;

		const license = await prisma.license.create({
			data: {
				licenseKey,
				customerEmail: options.customerEmail.toLowerCase(),
				customerName: options.customerName,
				licenseType,
				status: "ACTIVE",
				maxDevices,
				usedDevices: 0,
				paymentIntentId: options.paymentIntentId,
				totalPaidAmount: options.amount,
				createdAt: new Date(),
			},
		});

		Logger.info(
			EndpointPrefix.LICENSE_CREATE,
			`License created from checkout: ${licenseKey}`,
			{
				body: {
					licenseId: license.id,
					paymentIntentId: options.paymentIntentId,
				},
			},
		);

		return { success: true, license };
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_CREATE,
			`Failed to create license from checkout: ${error}`,
			{
				body: {
					customerEmail: options.customerEmail,
					paymentIntentId: options.paymentIntentId,
				},
			},
		);

		return { success: false, error: "Failed to create license from checkout" };
	}
}

/**
 * Update email delivery status for license
 */

export async function updateEmailDeliveryStatus(
	licenseId: string,
	status: "sent" | "failed",
): Promise<boolean> {
	try {
		await prisma.license.update({
			where: { id: licenseId },
			data: {
				emailSentAt: new Date(),
				emailDeliveryStatus: status,
			},
		});

		return true;
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_CREATE,
			`Failed to update email delivery status: ${error}`,
			{ body: { licenseId } },
		);

		return false;
	}
}
