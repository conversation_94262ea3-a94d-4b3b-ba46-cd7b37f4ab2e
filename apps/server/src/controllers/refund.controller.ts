import type { RefundRequest } from "prisma/generated/client";
import { EndpointPrefix, Logger } from "@/utils/logger";
import {
	prepareDatabaseQuery,
	type StandardPaginationParams,
} from "@/utils/pagination";
import prisma from "../../prisma";

// ============================================================================
// REFUND MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Get all refund requests with pagination and filtering (using enhanced utilities)
 */
export async function getRefundRequests(
	params: StandardPaginationParams,
): Promise<{ refundRequests: RefundRequest[]; total: number }> {
	try {
		// Use enhanced pagination utilities
		const { skip, take, where } = prepareDatabaseQuery(
			params,
			["reason", "requestedBy"], // searchFields for refund search
			{
				// Additional filters from params.filters
				...(params.filters?.status && { status: params.filters.status }),
			},
		);

		// Add complex search for license key (nested relation)
		if (params.search && !where.OR) {
			where.OR = [];
		}
		if (params.search && where.OR) {
			where.OR.push({
				license: {
					licenseKey: { contains: params.search, mode: "insensitive" },
				},
			});
		}

		const [refundRequests, total] = await Promise.all([
			prisma.refundRequest.findMany({
				where,
				include: {
					license: {
						select: {
							id: true,
							licenseKey: true,
							customerEmail: true,
							customerName: true,
							licenseType: true,
							totalPaidAmount: true,
						},
					},
					processedByUser: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
				orderBy: { createdAt: "desc" }, // Override default ordering
				skip,
				take,
			}),
			prisma.refundRequest.count({ where }),
		]);

		return { refundRequests, total };
	} catch (error) {
		Logger.error(
			EndpointPrefix.REFUND_LIST,
			`Failed to get refund requests: ${error}`,
			{ body: { ...params } },
		);
		return { refundRequests: [], total: 0 };
	}
}

// ============================================================================
// ADMIN REFUND MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Approve refund request (Admin)
 */
export async function approveRefund(
	refundId: string,
	reason: string | undefined,
	adminNotes: string | undefined,
	actorId: string,
): Promise<{ success: boolean; error?: string }> {
	try {
		const refundRequest = await prisma.refundRequest.findUnique({
			where: { id: refundId },
			include: {
				license: {
					select: {
						id: true,
						licenseKey: true,
						customerEmail: true,
					},
				},
			},
		});

		if (!refundRequest) {
			return { success: false, error: "Refund request not found" };
		}

		if (refundRequest.status !== "PENDING") {
			return { success: false, error: "Refund request is not pending" };
		}

		// Update refund request status
		await prisma.refundRequest.update({
			where: { id: refundId },
			data: {
				status: "APPROVED",
				adminNotes: adminNotes || refundRequest.adminNotes,
				processedAt: new Date(),
			},
		});

		// Create audit log
		await prisma.auditLog.create({
			data: {
				action: "REFUND_APPROVED", // Type assertion needed due to enum mismatch
				actorId,
				targetId: refundId,
				licenseKey: refundRequest.license.licenseKey,
				customerEmail: refundRequest.license.customerEmail,
				details: {
					refundId,
					reason: reason || "Admin approval",
					adminNotes,
					requestedAmount: refundRequest.requestedAmount,
				},
			},
		});

		Logger.info(
			EndpointPrefix.REFUND_APPROVE,
			`Refund request approved: ${refundId}`,
			{
				body: {
					refundId,
					licenseKey: refundRequest.license.licenseKey,
					actorId,
				},
			},
		);

		return { success: true };
	} catch (error) {
		Logger.error(
			EndpointPrefix.REFUND_APPROVE,
			`Failed to approve refund: ${error}`,
			{ body: { refundId, reason, actorId } },
		);
		return { success: false, error: "Failed to approve refund request" };
	}
}

/**
 * Reject refund request (Admin)
 */
export async function rejectRefund(
	refundId: string,
	reason: string,
	adminNotes: string | undefined,
	actorId: string,
): Promise<{ success: boolean; error?: string }> {
	try {
		const refundRequest = await prisma.refundRequest.findUnique({
			where: { id: refundId },
			include: {
				license: {
					select: {
						id: true,
						licenseKey: true,
						customerEmail: true,
					},
				},
			},
		});

		if (!refundRequest) {
			return { success: false, error: "Refund request not found" };
		}

		if (refundRequest.status !== "PENDING") {
			return { success: false, error: "Refund request is not pending" };
		}

		// Update refund request status
		await prisma.refundRequest.update({
			where: { id: refundId },
			data: {
				status: "REJECTED",
				adminNotes: adminNotes || refundRequest.adminNotes,
				processedAt: new Date(),
			},
		});

		// Create audit log
		await prisma.auditLog.create({
			data: {
				action: "REFUND_REJECTED",
				actorId,
				targetId: refundId,
				licenseKey: refundRequest.license.licenseKey,
				customerEmail: refundRequest.license.customerEmail,
				details: {
					refundId,
					reason,
					adminNotes,
					requestedAmount: refundRequest.requestedAmount,
				},
			},
		});

		Logger.info(
			EndpointPrefix.REFUND_REJECT,
			`Refund request rejected: ${refundId}`,
			{
				body: {
					refundId,
					reason,
					licenseKey: refundRequest.license.licenseKey,
					actorId,
				},
			},
		);

		return { success: true };
	} catch (error) {
		Logger.error(
			EndpointPrefix.REFUND_REJECT,
			`Failed to reject refund: ${error}`,
			{ body: { refundId, reason, actorId } },
		);
		return { success: false, error: "Failed to reject refund request" };
	}
}

/**
 * Process approved refund via Stripe (Admin)
 */
export async function processRefund(
	refundId: string,
	amount: number | undefined,
	reason: string | undefined,
	actorId: string,
): Promise<{ success: boolean; stripeRefundId?: string; error?: string }> {
	try {
		const refundRequest = await prisma.refundRequest.findUnique({
			where: { id: refundId },
			include: {
				license: {
					select: {
						id: true,
						licenseKey: true,
						customerEmail: true,
						paymentIntentId: true,
					},
				},
			},
		});

		if (!refundRequest) {
			return { success: false, error: "Refund request not found" };
		}

		if (refundRequest.status !== "APPROVED") {
			return { success: false, error: "Refund request is not approved" };
		}

		if (!refundRequest.license.paymentIntentId) {
			return {
				success: false,
				error: "No payment intent found for this license",
			};
		}

		// Import Stripe dynamically to avoid issues if not configured
		const stripe = (await import("stripe")).default;
		const stripeClient = new stripe(process.env.STRIPE_SECRET_KEY!);

		// Create refund in Stripe
		const refundAmount = amount || refundRequest.requestedAmount;
		const stripeRefund = await stripeClient.refunds.create({
			payment_intent: refundRequest.license.paymentIntentId,
			amount: refundAmount || 0,
			reason: "requested_by_customer",
			metadata: {
				refundRequestId: refundId,
				licenseKey: refundRequest.license.licenseKey,
				processedBy: actorId,
			},
		});

		// Update refund request status
		await prisma.refundRequest.update({
			where: { id: refundId },
			data: {
				status: "PROCESSED",
				approvedAmount: refundAmount,
				stripeRefundIds: { push: stripeRefund.id },
				processedAt: new Date(),
			},
		});

		// Create audit log
		await prisma.auditLog.create({
			data: {
				action: "REFUND_PROCESSED", // Type assertion needed due to enum mismatch
				actorId,
				targetId: refundId,
				licenseKey: refundRequest.license.licenseKey,
				customerEmail: refundRequest.license.customerEmail,
				details: {
					refundId,
					stripeRefundId: stripeRefund.id,
					amount: refundAmount,
					reason: reason || "Admin processing",
					paymentIntentId: refundRequest.license.paymentIntentId,
				},
			},
		});

		Logger.info(
			EndpointPrefix.REFUND_PROCESS,
			`Refund processed via Stripe: ${refundId}`,
			{
				body: {
					refundId,
					stripeRefundId: stripeRefund.id,
					amount: refundAmount,
					actorId,
				},
			},
		);

		return { success: true, stripeRefundId: stripeRefund.id };
	} catch (error) {
		Logger.error(
			EndpointPrefix.REFUND_PROCESS,
			`Failed to process refund: ${error}`,
			{ body: { refundId, amount, reason, actorId } },
		);
		return { success: false, error: "Failed to process refund via Stripe" };
	}
}

/**
 * Update refund request (Admin)
 */
export async function adminUpdateRefund(
	refundId: string,
	updateData: {
		adminNotes?: string;
		internalStatus?: string;
	},
	actorId: string,
): Promise<{ success: boolean; refundRequest?: any; error?: string }> {
	try {
		const existingRefund = await prisma.refundRequest.findUnique({
			where: { id: refundId },
			include: {
				license: {
					select: {
						id: true,
						licenseKey: true,
						customerEmail: true,
					},
				},
			},
		});

		if (!existingRefund) {
			return { success: false, error: "Refund request not found" };
		}

		// Update refund request
		const updatedRefund = await prisma.refundRequest.update({
			where: { id: refundId },
			data: updateData,
			include: {
				license: {
					select: {
						id: true,
						licenseKey: true,
						customerEmail: true,
						customerName: true,
						status: true,
					},
				},
			},
		});

		// Create audit log
		await prisma.auditLog.create({
			data: {
				action: "REFUND_UPDATED",
				actorId,
				targetId: refundId,
				licenseKey: existingRefund.license.licenseKey,
				customerEmail: existingRefund.license.customerEmail,
				details: {
					refundId,
					changes: updateData,
					previousData: {
						adminNotes: existingRefund.adminNotes,
					},
				},
			},
		});

		Logger.info(
			EndpointPrefix.REFUND_UPDATE,
			`Refund request updated: ${refundId}`,
			{ body: { refundId, changes: updateData, actorId } },
		);

		return { success: true, refundRequest: updatedRefund };
	} catch (error) {
		Logger.error(
			EndpointPrefix.REFUND_UPDATE,
			`Failed to update refund: ${error}`,
			{ body: { refundId, updateData, actorId } },
		);
		return { success: false, error: "Failed to update refund request" };
	}
}
