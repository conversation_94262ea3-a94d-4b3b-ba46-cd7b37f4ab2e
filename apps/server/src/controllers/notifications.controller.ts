import { EndpointPrefix, Logger } from "@/utils/logger";
import {
	prepareDatabaseQuery,
	type StandardPaginationParams,
} from "@/utils/pagination";
import prisma from "../../prisma";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface SystemNotification {
	id: string;
	type: "INFO" | "WARNING" | "ERROR" | "SUCCESS";
	title: string;
	message: string;
	userId?: string;
	metadata?: Record<string, any>;
	isRead: boolean;
	createdAt: Date;
	readAt?: Date;
}

export interface NotificationSettings {
	userId: string;
	emailNotifications: boolean;
	pushNotifications: boolean;
	smsNotifications: boolean;
	licenseExpiry: boolean;
	paymentUpdates: boolean;
	systemAlerts: boolean;
	marketingEmails: boolean;
	updatedAt: Date;
}

// ============================================================================
// NOTIFICATION FUNCTIONS
// ============================================================================

/**
 * Get system notifications with pagination and filtering
 */
export async function getSystemNotifications(
	params: StandardPaginationParams & {
		userId?: string;
		type?: "INFO" | "WARNING" | "ERROR" | "SUCCESS";
		isRead?: boolean;
	} = { page: 1, limit: 20 },
): Promise<{
	notifications: SystemNotification[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}> {
	try {
		// Since we don't have a notifications table in the database yet,
		// we'll generate mock notifications based on audit logs and system events
		Logger.info(
			EndpointPrefix.NOTIFICATIONS,
			"Retrieving system notifications",
			{
				body: {
					page: params.page,
					limit: params.limit,
					userId: params.userId,
					type: params.type,
					isRead: params.isRead,
				},
			},
		);

		// Generate mock notifications from recent audit logs
		const recentAuditLogs = await prisma.auditLog.findMany({
			where: {
				createdAt: {
					gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
				},
			},
			orderBy: {
				createdAt: "desc",
			},
			take: 50, // Get recent logs to convert to notifications
		});

		// Convert audit logs to notifications
		const notifications: SystemNotification[] = recentAuditLogs.map(
			(log, index) => {
				let type: "INFO" | "WARNING" | "ERROR" | "SUCCESS" = "INFO";
				let title = "System Activity";
				let message = `Action: ${log.action}`;

				// Determine notification type and content based on action
				switch (log.action) {
					case "LICENSE_CREATED":
						type = "SUCCESS";
						title = "New License Created";
						message = `License created for ${log.customerEmail || "customer"}`;
						break;
					case "REFUND_PROCESSED":
						type = "WARNING";
						title = "Refund Processed";
						message = `Refund processed for license ${log.licenseKey || ""}`;
						break;
					case "DEVICE_EXPANSION_PROCESSED":
						type = "SUCCESS";
						title = "Device Expansion Completed";
						message = `Device expansion processed for license ${log.licenseKey || ""}`;
						break;
					case "BULK_EMAIL_SENT": {
						type = "INFO";
						title = "Bulk Email Sent";
						const details = log.details as Record<string, any>;
						message = `Bulk email sent to ${details?.recipientCount || 0} recipients`;
						break;
					}
					case "USER_UPDATED":
						type = "INFO";
						title = "User Updated";
						message = "User information updated";
						break;
					case "USER_LOGIN":
						type = "INFO";
						title = "User Login";
						message = `User logged in: ${log.customerEmail || "Unknown"}`;
						break;
					default:
						message = `System action: ${log.action}`;
				}

				return {
					id: `notif_${log.id}`,
					type,
					title,
					message,
					userId: params.userId,
					metadata: {
						auditLogId: log.id,
						action: log.action,
						details: log.details,
					},
					isRead: Math.random() > 0.3, // 70% chance of being read (mock data)
					createdAt: log.createdAt,
					readAt:
						Math.random() > 0.3
							? new Date(
									log.createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000,
								)
							: undefined,
				};
			},
		);

		// Apply filters
		let filteredNotifications = notifications;

		if (params.type) {
			filteredNotifications = filteredNotifications.filter(
				(n) => n.type === params.type,
			);
		}

		if (params.isRead !== undefined) {
			filteredNotifications = filteredNotifications.filter(
				(n) => n.isRead === params.isRead,
			);
		}

		// Apply pagination
		const total = filteredNotifications.length;
		const startIndex = (params.page - 1) * params.limit;
		const endIndex = startIndex + params.limit;
		const paginatedNotifications = filteredNotifications.slice(
			startIndex,
			endIndex,
		);

		Logger.info(
			EndpointPrefix.NOTIFICATIONS,
			`System notifications retrieved: ${paginatedNotifications.length} of ${total}`,
			{
				body: {
					page: params.page,
					limit: params.limit,
					total,
					filtered: filteredNotifications.length,
				},
			},
		);

		return {
			notifications: paginatedNotifications,
			pagination: {
				page: params.page,
				limit: params.limit,
				total,
				totalPages: Math.ceil(total / params.limit),
			},
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.NOTIFICATIONS,
			`Failed to get system notifications: ${error}`,
			{ body: { params } },
		);
		throw error;
	}
}

/**
 * Mark notifications as read
 */
export async function markNotificationsAsRead(
	notificationIds: string[],
	userId: string,
): Promise<{ success: boolean; markedCount: number }> {
	try {
		Logger.info(
			EndpointPrefix.NOTIFICATIONS,
			`Marking ${notificationIds.length} notifications as read`,
			{
				body: {
					notificationIds,
					userId,
				},
			},
		);

		// In a real implementation, you would update the notifications in the database
		// For now, we'll just log the action and return success

		// Create audit log for this action
		await prisma.auditLog.create({
			data: {
				action: "NOTIFICATIONS_MARKED_READ",
				actorId: userId,
				details: {
					notificationIds,
					count: notificationIds.length,
				},
			},
		});

		Logger.info(
			EndpointPrefix.NOTIFICATIONS,
			`Successfully marked ${notificationIds.length} notifications as read`,
			{
				body: {
					userId,
					markedCount: notificationIds.length,
				},
			},
		);

		return {
			success: true,
			markedCount: notificationIds.length,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.NOTIFICATIONS,
			`Failed to mark notifications as read: ${error}`,
			{ body: { notificationIds, userId } },
		);
		throw error;
	}
}

/**
 * Get notification settings for a user
 */
export async function getNotificationSettings(
	userId: string,
): Promise<NotificationSettings> {
	try {
		Logger.info(
			EndpointPrefix.NOTIFICATION_SETTINGS,
			`Retrieving notification settings for user: ${userId}`,
			{ body: { userId } },
		);

		// In a real implementation, you would query from a user_notification_settings table
		// For now, we'll return default settings
		const settings: NotificationSettings = {
			userId,
			emailNotifications: true,
			pushNotifications: true,
			smsNotifications: false,
			licenseExpiry: true,
			paymentUpdates: true,
			systemAlerts: true,
			marketingEmails: false,
			updatedAt: new Date(),
		};

		Logger.info(
			EndpointPrefix.NOTIFICATION_SETTINGS,
			`Notification settings retrieved for user: ${userId}`,
			{ body: { userId, settings } },
		);

		return settings;
	} catch (error) {
		Logger.error(
			EndpointPrefix.NOTIFICATION_SETTINGS,
			`Failed to get notification settings: ${error}`,
			{ body: { userId } },
		);
		throw error;
	}
}

/**
 * Update notification settings for a user
 */
export async function updateNotificationSettings(
	userId: string,
	settings: Partial<Omit<NotificationSettings, "userId" | "updatedAt">>,
): Promise<NotificationSettings> {
	try {
		Logger.info(
			EndpointPrefix.NOTIFICATION_SETTINGS,
			`Updating notification settings for user: ${userId}`,
			{
				body: {
					userId,
					settings,
				},
			},
		);

		// Get current settings
		const currentSettings = await getNotificationSettings(userId);

		// Merge with new settings
		const updatedSettings: NotificationSettings = {
			...currentSettings,
			...settings,
			userId,
			updatedAt: new Date(),
		};

		// In a real implementation, you would save to database
		// For now, we'll just create an audit log
		await prisma.auditLog.create({
			data: {
				action: "NOTIFICATION_SETTINGS_UPDATED",
				actorId: userId,
				details: {
					userId,
					changes: Object.keys(settings),
				},
			},
		});

		Logger.info(
			EndpointPrefix.NOTIFICATION_SETTINGS,
			`Notification settings updated for user: ${userId}`,
			{
				body: {
					userId,
					updatedSettings,
				},
			},
		);

		return updatedSettings;
	} catch (error) {
		Logger.error(
			EndpointPrefix.NOTIFICATION_SETTINGS,
			`Failed to update notification settings: ${error}`,
			{ body: { userId, settings } },
		);
		throw error;
	}
}
