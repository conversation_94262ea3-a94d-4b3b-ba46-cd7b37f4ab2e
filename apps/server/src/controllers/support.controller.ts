import type {
	TicketCategory,
	TicketPriority,
	TicketStatus,
} from "prisma/generated/enums";
import { EndpointPrefix, Logger } from "@/utils/logger";
import prisma from "../../prisma";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface CreateSupportTicketParams {
	subject: string;
	description: string;
	category?: TicketCategory;
	customerEmail: string;
	customerName: string;
	licenseKey?: string;
}

export interface SupportTicketResponse {
	id: string;
	ticketId: string;
	subject: string;
	description: string;
	status: TicketStatus;
	priority: TicketPriority;
	category: TicketCategory | null;
	customerEmail: string;
	customerName: string | null;
	licenseKey: string | null;
	createdAt: string;
	updatedAt: string;
}

export interface FaqCategory {
	id: string;
	name: string;
	description?: string;
	questions: FaqQuestion[];
}

export interface FaqQuestion {
	id: string;
	question: string;
	answer: string;
	helpful?: number;
	tags?: string[];
}

export interface FaqData {
	categories: FaqCategory[];
	lastUpdated: string;
}

// ============================================================================
// SUPPORT CONTROLLER FUNCTIONS
// ============================================================================

/**
 * Create a new support ticket
 */
export async function createSupportTicket(
	params: CreateSupportTicketParams,
): Promise<{
	success: boolean;
	ticket?: SupportTicketResponse;
	error?: string;
}> {
	try {
		Logger.info(
			EndpointPrefix.SUPPORT_TICKET_CREATE,
			`Creating support ticket for ${params.customerEmail}`,
			{
				body: {
					subject: params.subject,
					category: params.category,
					customerEmail: params.customerEmail,
					hasLicenseKey: !!params.licenseKey,
				},
			},
		);

		// Generate human-readable ticket ID
		const ticketCount = await prisma.supportTicket.count();
		const ticketId = `SNAP-${new Date().getFullYear()}-${String(ticketCount + 1).padStart(3, "0")}`;

		// Determine priority based on category and content
		let priority: "LOW" | "MEDIUM" | "HIGH" | "URGENT" = "MEDIUM";
		if (params.category === "BILLING" || params.licenseKey) {
			priority = "HIGH";
		} else if (params.category === "TECHNICAL") {
			priority = "MEDIUM";
		} else {
			priority = "LOW";
		}

		// Create the support ticket
		const ticket = await prisma.supportTicket.create({
			data: {
				ticketId,
				subject: params.subject,
				description: params.description,
				category: params.category || "GENERAL",
				customerEmail: params.customerEmail,
				customerName: params.customerName,
				licenseKey: params.licenseKey,
				priority,
				status: "OPEN",
			},
		});

		// Create initial message
		await prisma.supportMessage.create({
			data: {
				ticketId: ticket.id,
				message: params.description,
				isInternal: false,
				authorEmail: params.customerEmail,
			},
		});

		// Log audit trail
		await prisma.auditLog.create({
			data: {
				action: "SUPPORT_TICKET_CREATED",
				customerEmail: params.customerEmail,
				details: {
					ticketId: ticket.ticketId,
					subject: params.subject,
					category: params.category,
					priority,
				},
				ipAddress: "system", // Will be updated by route handler
			},
		});

		Logger.info(
			EndpointPrefix.SUPPORT_TICKET_CREATE,
			`Support ticket created successfully: ${ticket.ticketId}`,
			{
				body: {
					ticketId: ticket.ticketId,
					priority,
					status: ticket.status,
				},
			},
		);

		const response: SupportTicketResponse = {
			id: ticket.id,
			ticketId: ticket.ticketId,
			subject: ticket.subject,
			description: ticket.description,
			status: ticket.status,
			priority: ticket.priority,
			category: ticket.category,
			customerEmail: ticket.customerEmail,
			customerName: ticket.customerName,
			licenseKey: ticket.licenseKey,
			createdAt: ticket.createdAt.toISOString(),
			updatedAt: ticket.updatedAt.toISOString(),
		};

		return { success: true, ticket: response };
	} catch (error) {
		Logger.error(
			EndpointPrefix.SUPPORT_TICKET_CREATE,
			`Failed to create support ticket: ${error instanceof Error ? error.message : "Unknown error"}`,
			{
				body: {
					customerEmail: params.customerEmail,
					error: error instanceof Error ? error.message : "Unknown error",
				},
			},
		);

		return {
			success: false,
			error: "Failed to create support ticket. Please try again.",
		};
	}
}

/**
 * Get FAQ data with categories and questions
 */
export async function getFaqData(): Promise<FaqData> {
	try {
		Logger.info(EndpointPrefix.SUPPORT_FAQ, "Retrieving FAQ data", {
			body: { action: "get_faq_data" },
		});

		// For now, return hardcoded FAQ data
		// In a real implementation, this would come from a database or CMS
		const faqData: FaqData = {
			categories: [
				{
					id: "licensing",
					name: "Licensing & Activation",
					description:
						"Questions about license keys, activation, and device limits",
					questions: [
						{
							id: "license-not-working",
							question: "My license key is not working. What should I do?",
							answer:
								"First, make sure you're entering the license key correctly (24 characters, all uppercase). If it still doesn't work, check if your license has expired or been suspended. You can also try the license lookup feature on our website with your email address.",
							helpful: 45,
							tags: ["license", "activation", "troubleshooting"],
						},
						{
							id: "device-limit",
							question: "How many devices can I use with my license?",
							answer:
								"Standard licenses allow 2 devices, Extended licenses allow 5 devices. You can purchase additional device slots if needed. Check your license details in the app to see your current device count.",
							helpful: 32,
							tags: ["devices", "limits", "expansion"],
						},
						{
							id: "transfer-license",
							question: "Can I transfer my license to a new device?",
							answer:
								"Yes! Simply remove the license from your old device in the app settings, then activate it on your new device. If you're having trouble, contact support and we'll help you transfer it manually.",
							helpful: 28,
							tags: ["transfer", "devices", "activation"],
						},
					],
				},
				{
					id: "billing",
					name: "Billing & Payments",
					description: "Questions about purchases, refunds, and billing",
					questions: [
						{
							id: "refund-policy",
							question: "What is your refund policy?",
							answer:
								"We offer a 30-day money-back guarantee for all purchases. You can request a refund through our website or by contacting support. Refunds are processed within 5-7 business days.",
							helpful: 67,
							tags: ["refund", "policy", "money-back"],
						},
						{
							id: "payment-methods",
							question: "What payment methods do you accept?",
							answer:
								"We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and Apple Pay through our secure Stripe payment processor.",
							helpful: 23,
							tags: ["payment", "credit-card", "paypal"],
						},
					],
				},
				{
					id: "technical",
					name: "Technical Support",
					description: "Technical issues and troubleshooting",
					questions: [
						{
							id: "app-crashes",
							question: "The app keeps crashing. How can I fix this?",
							answer:
								"Try restarting the app first. If that doesn't work, check for updates in the App Store. You can also try restarting your device. If the problem persists, please contact support with your device model and operating system version.",
							helpful: 41,
							tags: ["crash", "troubleshooting", "technical"],
						},
						{
							id: "sync-issues",
							question: "My data isn't syncing between devices. What's wrong?",
							answer:
								"Make sure you're signed in with the same license on both devices and that you have an active internet connection. Cloud sync requires a valid license. Try signing out and back in if sync isn't working.",
							helpful: 35,
							tags: ["sync", "cloud", "devices"],
						},
					],
				},
			],
			lastUpdated: new Date().toISOString(),
		};

		Logger.info(EndpointPrefix.SUPPORT_FAQ, "FAQ data retrieved successfully", {
			body: {
				categoriesCount: faqData.categories.length,
				totalQuestions: faqData.categories.reduce(
					(total, cat) => total + cat.questions.length,
					0,
				),
			},
		});

		return faqData;
	} catch (error) {
		Logger.error(
			EndpointPrefix.SUPPORT_FAQ,
			`Failed to retrieve FAQ data: ${error instanceof Error ? error.message : "Unknown error"}`,
			{
				body: {
					error: error instanceof Error ? error.message : "Unknown error",
				},
			},
		);

		// Return empty FAQ data on error
		return {
			categories: [],
			lastUpdated: new Date().toISOString(),
		};
	}
}
