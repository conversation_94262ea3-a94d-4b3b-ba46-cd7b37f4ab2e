import { prisma } from "@snapback/shared";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { APIError } from "better-auth/api";
import { createAuditLog } from "@/controllers/user.controller";
import { EndpointPrefix, Logger } from "@/utils/logger";

export const auth = betterAuth({
	database: prismaAdapter(prisma, {
		provider: "postgresql",
	}),
	trustedOrigins: [process.env.CORS_ORIGIN || ""],
	emailAndPassword: {
		enabled: true,

		autoSignIn: true,
	},
	// Extend user schema with additional fields
	user: {
		additionalFields: {
			role: {
				type: "string",
				required: true,
				defaultValue: "USER",
				input: false, // Not included in registration form
				returned: true, // Included in response
			},
		},
	},

	// Database hooks for first user bootstrap and invitation integration
	databaseHooks: {
		user: {
			create: {
				before: async (user, ctx) => {
					Logger.info(
						EndpointPrefix.USER_CREATE,
						`Better Auth user creation attempt: ${user.email}`,
						{ body: { email: user.email, name: user.name } },
					);

					// Check if this is the first user (bootstrap admin)
					const userCount = await prisma.user.count();

					if (userCount === 0) {
						Logger.info(
							EndpointPrefix.USER_CREATE,
							`First user bootstrap - assigning SUPER_ADMIN role: ${user.email}`,
							{ body: { email: user.email, isFirstUser: true } },
						);

						return {
							data: {
								...user,
								role: "SUPER_ADMIN",
								isActive: true,
								emailVerified: true, // First user is auto-verified
							},
						};
					}

					// For subsequent users, check if they were already created through invitation acceptance
					const invitation = await prisma.userInvitation.findUnique({
						where: { token: ctx?.body.token },
					});

					if (invitation) {
						// User was created through invitation acceptance - use existing data
						Logger.info(
							EndpointPrefix.USER_CREATE,
							`User exists from invitation - using existing data: ${user.email}`,
							{ body: { email: user.email, role: invitation.role } },
						);

						return {
							data: {
								...user,
								role: invitation.role,
								isActive: true,
								invitedBy: invitation.sentBy,
								invitedAt: invitation.sentAt,
							},
						};
					}

					// If no existing user and not first user, reject direct signup
					Logger.warn(
						EndpointPrefix.USER_CREATE,
						`Direct signup attempt without invitation: ${user.email}`,
						{ body: { email: user.email } },
					);

					throw new APIError("BAD_REQUEST", {
						message:
							"Registration requires a valid invitation. Please use your invitation link to create an account.",
					});
				},
				after: async (user, ctx) => {
					Logger.info(
						EndpointPrefix.USER_CREATE,
						`User created successfully: ${user.email}`,
						{ body: { email: user.email, role: ctx?.body.role } },
					);

					await createAuditLog({
						action: "USER_CREATED",
						actorId: user.id,
						targetId: user.id,
						customerEmail: user.email,
						details: {
							role: ctx?.body.role,
						},
					});

					if (!ctx?.body.token) return;

					// Mark invitation as accepted
					await prisma.userInvitation.update({
						where: { token: ctx?.body.token },
						data: {
							status: "ACCEPTED",
							acceptedAt: new Date(),
							acceptedBy: user.id,
						},
					});
				},
			},
		},
	},
});
