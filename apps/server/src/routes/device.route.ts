import {
	deviceHeartbeatSchema,
	purchaseDeviceExpansionSchema,
	registerDeviceSchema,
	updateDeviceSchema,
} from "@snapback/shared/schemas";
import { Router } from "express";
import {
	purchaseDeviceExpansion,
	registerDevice,
} from "@/controllers/device.controller";

import type { ApiResponse } from "@/types/api";
import { handleError } from "@/utils/errors";
import { EndpointPrefix, Logger } from "@/utils/logger";
import prisma from "../../../../packages/shared/src/db/prisma";

const router: Router = Router();

// ============================================================================
// PUBLIC DEVICE ROUTES (for App)
// ============================================================================

/**
 * POST /api/devices/register
 * Register new device with license
 */
router.post("/register", async (req, res) => {
	try {
		const validatedData = registerDeviceSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.LICENSE_VALIDATE,
			`Device registration request: ${validatedData.licenseKey}`,
			{
				body: {
					licenseKey: validatedData.licenseKey,
					deviceId: validatedData.deviceId,
				},
			},
		);

		// Find license by key
		const license = await prisma.license.findUnique({
			where: { licenseKey: validatedData.licenseKey },
		});

		if (!license) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "License not found",
				error: {
					code: "LICENSE_NOT_FOUND",
					message: "License not found",
				},
			};
			return res.status(404).json(response);
		}

		// Register device
		const result = await registerDevice({
			licenseId: license.id,
			deviceId: validatedData.deviceId,
			appVersion: validatedData.appVersion,
			deviceMetadata: validatedData.deviceMetadata,
		});

		if (result.success) {
			const response: ApiResponse<typeof result.device> = {
				success: true,
				data: result.device,
				message: "Device registered successfully",
			};
			res.status(200).json(response);
		} else {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: result.error || "Device registration failed",
				error: {
					code: result.errorCode || "REGISTRATION_FAILED",
					message: result.error || "Device registration failed",
				},
			};
			res.status(400).json(response);
		}
	} catch (error) {
		handleError(res, error, EndpointPrefix.LICENSE_VALIDATE);
	}
});

/**
 * PATCH /api/devices/:deviceHash/update
 * Update device info (app version, etc.)
 */
router.patch("/:deviceHash/update", async (req, res) => {
	try {
		const { deviceHash } = req.params;
		const validatedData = updateDeviceSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.LICENSE_VALIDATE,
			`Device update request: ${deviceHash}`,
			{ body: { deviceHash } },
		);

		// Find device by hash
		const device = await prisma.device.findFirst({
			where: {
				deviceHash,
				status: "ACTIVE",
			},
		});

		if (!device) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "Device not found",
				error: {
					code: "DEVICE_NOT_FOUND",
					message: "Device not found",
				},
			};
			return res.status(404).json(response);
		}

		// Update device
		const updatedDevice = await prisma.device.update({
			where: { id: device.id },
			data: {
				...validatedData,
				lastSeen: new Date(),
			},
		});

		const response: ApiResponse<typeof updatedDevice> = {
			success: true,
			data: updatedDevice,
			message: "Device updated successfully",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.LICENSE_VALIDATE);
	}
});

/**
 * DELETE /api/devices/:deviceHash/remove
 * Remove device (from app)
 */
router.delete("/:deviceHash/remove", async (req, res) => {
	try {
		const { deviceHash } = req.params;
		const { licenseKey } = req.body;

		if (!licenseKey) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "License key is required",
				error: {
					code: "VALIDATION_ERROR",
					message: "License key is required",
				},
			};
			return res.status(400).json(response);
		}

		Logger.info(
			EndpointPrefix.LICENSE_REMOVE_DEVICE,
			`Device removal request: ${deviceHash}`,
			{ body: { deviceHash, licenseKey } },
		);

		// Find license by key
		const license = await prisma.license.findUnique({
			where: { licenseKey },
		});

		if (!license) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "License not found",
				error: {
					code: "LICENSE_NOT_FOUND",
					message: "License not found",
				},
			};
			return res.status(404).json(response);
		}

		// Remove device (this uses deviceId, but we need to convert from hash)
		// For now, we'll update the device status directly
		const device = await prisma.device.findFirst({
			where: {
				licenseId: license.id,
				deviceHash,
				status: "ACTIVE",
			},
		});

		if (!device) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "Device not found",
				error: {
					code: "DEVICE_NOT_FOUND",
					message: "Device not found",
				},
			};
			return res.status(404).json(response);
		}

		// Update device status to removed
		await prisma.device.update({
			where: { id: device.id },
			data: {
				status: "REMOVED",
				removedAt: new Date(),
			},
		});

		// Update license used devices count
		const activeDevicesCount = await prisma.device.count({
			where: {
				licenseId: license.id,
				status: "ACTIVE",
			},
		});

		await prisma.license.update({
			where: { id: license.id },
			data: {
				usedDevices: activeDevicesCount,
			},
		});

		const response: ApiResponse<{ removed: boolean }> = {
			success: true,
			data: { removed: true },
			message: "Device removed successfully",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.LICENSE_REMOVE_DEVICE);
	}
});

/**
 * POST /api/devices/heartbeat
 * Device heartbeat/ping
 */
router.post("/heartbeat", async (req, res) => {
	try {
		const validatedData = deviceHeartbeatSchema.parse(req.body);

		// Find license and device
		const license = await prisma.license.findUnique({
			where: { licenseKey: validatedData.licenseKey },
			include: {
				devices: {
					where: { status: "ACTIVE" },
				},
			},
		});

		if (!license) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "License not found",
				error: {
					code: "LICENSE_NOT_FOUND",
					message: "License not found",
				},
			};
			return res.status(404).json(response);
		}

		// Update device last seen (if device exists)
		// Note: We'd need to hash the deviceId to find the device
		// For now, just return success to keep the heartbeat working

		const response: ApiResponse<{
			status: string;
			timestamp: string;
		}> = {
			success: true,
			data: {
				status: "ok",
				timestamp: new Date().toISOString(),
			},
			message: "Heartbeat received",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.LICENSE_VALIDATE);
	}
});

/**
 * POST /api/devices/expand
 * Purchase additional device slots
 */
router.post("/expand", async (req, res) => {
	try {
		const validatedData = purchaseDeviceExpansionSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.DEVICE_EXPANSION,
			`Device expansion request: ${validatedData.licenseKey}`,
			{
				body: {
					licenseKey: validatedData.licenseKey,
					additionalDevices: validatedData.additionalDevices,
				},
			},
		);

		const result = await purchaseDeviceExpansion(
			validatedData.licenseKey,
			validatedData.additionalDevices,
		);

		if (result.success) {
			const response: ApiResponse<{ checkoutUrl: string }> = {
				success: true,
				data: { checkoutUrl: result.checkoutUrl as string },
				message: "Device expansion checkout created successfully",
			};
			res.status(200).json(response);
		} else {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: result.error || "Failed to create device expansion checkout",
				error: {
					code: "DEVICE_EXPANSION_FAILED",
					message: result.error || "Failed to create device expansion checkout",
				},
			};
			res.status(400).json(response);
		}
	} catch (error) {
		handleError(res, error, EndpointPrefix.DEVICE_EXPANSION);
	}
});

export default router;
