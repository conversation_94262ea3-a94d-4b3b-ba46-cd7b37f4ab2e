import {
	adminPaginationSchema,
	adminUpdateDeviceSchema,
	adminUpdateRefundSchema,
	approveRefundSchema,
	bulkEmailSchema,
	createLicenseSchema,
	createUserInvitationSchema,
	createUserSchema,
	dashboardStatsSchema,
	deviceAnalyticsSchema,
	emailDeliveryStatsSchema,
	extendLicenseSchema,
	licenseAnalyticsSchema,
	markNotificationsReadSchema,
	notificationSettingsSchema,
	prisma,
	processDeviceExpansionSchema,
	processRefundSchema,
	reactivateLicenseSchema,
	reactivateUserSchema,
	rejectRefundSchema,
	reportExportSchema,
	resendInvitationSchema,
	retryWebhookSchema,
	revenueAnalyticsSchema,
	suspendLicenseSchema,
	updateInvitationSchema,
	updateLicenseSchema,
	updateUserSchema,
	webhookEventsSchema,
	webhookStatsSchema,
} from "@snapback/shared";
import { Router } from "express";
import {
	getDashboardStats,
	getDeviceAnalytics,
	getLicenseAnalytics,
	getRevenueAnalytics,
} from "@/controllers/analytics.controller";
import {
	adminRemoveDevice,
	adminUpdateDevice,
	getDeviceById,
	getDeviceExpansions,
	getDevices,
	processDeviceExpansionAdmin,
} from "@/controllers/device.controller";
import {
	getEmailDeliveryStats,
	getEmailTemplates,
	sendBulkEmails,
} from "@/controllers/email.controller";
import {
	createLicense,
	deleteLicense,
	extendLicense,
	getLicenses,
	reactivateLicense,
	suspendLicense,
} from "@/controllers/license.controller";
import {
	getNotificationSettings,
	getSystemNotifications,
	markNotificationsAsRead,
	updateNotificationSettings,
} from "@/controllers/notifications.controller";
import {
	adminUpdateRefund,
	approveRefund,
	processRefund,
	rejectRefund,
} from "@/controllers/refund.controller";
import {
	generateDeviceReport,
	generateLicenseReport,
	generateRefundReport,
	generateRevenueReport,
} from "@/controllers/reports.controller";
import {
	cancelInvitation,
	createAuditLog,
	createUser,
	createUserInvitation,
	getAuditLogs,
	getUserById,
	getUserInvitations,
	getUsers,
	reactivateUser,
	resendInvitation,
	updateInvitation,
	updateUser,
} from "@/controllers/user.controller";
import {
	getWebhookEvents,
	getWebhookStats,
	retryWebhookProcessing,
} from "@/controllers/webhook.controller";
import { requireAuth, requireRole } from "@/middleware/auth";
import type { ApiResponse } from "@/types/api";
import { handleError, LicenseErrors } from "@/utils/errors";
import { EndpointPrefix, Logger } from "@/utils/logger";
import { paginate } from "@/utils/pagination";
import prisma from "../../../../packages/shared/src/db/prisma";

const router: Router = Router();

// All admin routes require authentication
router.use(requireAuth);

// ============================================================================
// LICENSE MANAGEMENT ROUTES
// ============================================================================

/**
 * GET /api/admin/licenses
 * List all licenses (with pagination/filters)
 */
router.get(
	"/licenses",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { page, limit, search, sortBy, sortOrder, filters } =
				adminPaginationSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.LICENSE_LIST,
				`Admin license list request - Page: ${page}, Limit: ${limit}`,
				{ body: { page, limit, search } },
			);

			const licenseParams = {
				page,
				limit,
				search,
				sortBy,
				sortOrder,
				filters: {
					status: filters?.status,
					licenseType: filters?.licenseType,
				},
			};

			const { licenses, total } = await getLicenses(licenseParams);

			const pagination = paginate(total, page, limit);

			const response: ApiResponse<{
				licenses: typeof licenses;
				pagination: typeof pagination;
			}> = {
				success: true,
				data: {
					licenses,
					pagination,
				},
				message: `Retrieved ${licenses.length} licenses`,
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.LICENSE_LIST);
		}
	},
);

/**
 * GET /api/admin/licenses/:id
 * Get specific license details
 */
router.get(
	"/licenses/:id",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;

			Logger.info(
				EndpointPrefix.LICENSE_STATUS,
				`Admin license detail request: ${id}`,
				{ body: { licenseId: id } },
			);

			const license = await prisma.license.findUnique({
				where: { id },
				include: {
					devices: {
						select: {
							id: true,
							deviceHash: true,
							deviceName: true,
							deviceType: true,
							deviceModel: true,
							operatingSystem: true,
							architecture: true,
							userNickname: true,
							location: true,
							firstSeen: true,
							lastSeen: true,
							status: true,
							appVersion: true,
						},
					},
					deviceExpansions: {
						include: {
							paymentIntent: {
								select: {
									stripePaymentIntentId: true,
									amount: true,
									status: true,
									createdAt: true,
								},
							},
						},
					},
					paymentIntent: true,
					createdByUser: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					refundRequests: {
						include: {
							processedByUser: {
								select: {
									id: true,
									name: true,
									email: true,
								},
							},
						},
					},
				},
			});

			if (!license) {
				return LicenseErrors.notFound(res);
			}

			const response: ApiResponse<typeof license> = {
				success: true,
				data: license,
				message: "License details retrieved",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.LICENSE_STATUS);
		}
	},
);

/**
 * POST /api/admin/licenses
 * Manually create license (admin only)
 */
router.post(
	"/licenses",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const validatedData = createLicenseSchema.parse(req.body);
			const adminUserId = req.user?.id;

			Logger.info(
				EndpointPrefix.LICENSE_CREATE,
				`Admin license creation request: ${validatedData.email}`,
				{
					body: {
						email: validatedData.email,
						licenseType: validatedData.licenseType,
						adminUserId,
					},
				},
			);

			const result = await createLicense({
				...validatedData,
				createdBy: adminUserId,
			});

			if (result.success) {
				// Create audit log
				await createAuditLog({
					action: "LICENSE_CREATED",
					actorId: adminUserId,
					customerEmail: validatedData.email,
					licenseId: result.license?.id,
					details: {
						licenseType: validatedData.licenseType,
						createdBy: "admin",
					},
				});

				const response: ApiResponse<typeof result.license> = {
					success: true,
					data: result.license,
					message: "License created successfully",
				};
				res.status(201).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to create license",
					error: {
						code: "LICENSE_CREATION_FAILED",
						message: result.error || "Failed to create license",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.LICENSE_CREATE);
		}
	},
);

/**
 * PATCH /api/admin/licenses/:id
 * Update license (extend expiry, change status, etc.)
 */
router.patch(
	"/licenses/:id",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const validatedData = updateLicenseSchema.parse(req.body);
			const adminUserId = req.user?.id;

			Logger.info(
				EndpointPrefix.LICENSE_UPDATE,
				`Admin license update request: ${id}`,
				{
					body: {
						licenseId: id,
						updates: validatedData,
						adminUserId,
					},
				},
			);

			// Check if license exists
			const existingLicense = await prisma.license.findUnique({
				where: { id },
			});

			if (!existingLicense) {
				return LicenseErrors.notFound(res);
			}

			// Prepare update data
			const updateData: any = {};
			if (validatedData.status) updateData.status = validatedData.status;
			if (validatedData.maxDevices)
				updateData.maxDevices = validatedData.maxDevices;
			if (validatedData.expiresAt)
				updateData.expiresAt = new Date(validatedData.expiresAt);
			if (validatedData.customerName)
				updateData.customerName = validatedData.customerName;

			// Update license
			const updatedLicense = await prisma.license.update({
				where: { id },
				data: updateData,
			});

			// Create audit log
			await createAuditLog({
				action: "LICENSE_UPDATED",
				actorId: adminUserId,
				licenseId: id,
				licenseKey: existingLicense.licenseKey,
				customerEmail: existingLicense.customerEmail,
				details: {
					changes: validatedData,
					previousStatus: existingLicense.status,
				},
			});

			const response: ApiResponse<typeof updatedLicense> = {
				success: true,
				data: updatedLicense,
				message: "License updated successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.LICENSE_UPDATE);
		}
	},
);

/**
 * DELETE /api/admin/licenses/:id
 * Cancel/deactivate license
 */
router.delete(
	"/licenses/:id",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			Logger.info(
				EndpointPrefix.LICENSE_DELETE,
				`Admin license deletion request: ${id}`,
				{ body: { licenseId: id, adminUserId } },
			);

			const result = await deleteLicense(id, adminUserId);

			if (result.success) {
				const response: ApiResponse<null> = {
					success: true,
					data: null,
					message: "License cancelled successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to cancel license",
					error: {
						code: "LICENSE_DELETION_FAILED",
						message: result.error || "Failed to cancel license",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.LICENSE_DELETE);
		}
	},
);

/**
 * POST /api/admin/licenses/:id/extend
 * Extend license expiry
 */
router.post(
	"/licenses/:id/extend",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = extendLicenseSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.LICENSE_EXTEND,
				`Admin license extension request: ${id}`,
				{ body: { licenseId: id, extensionDays: validatedData.extensionDays } },
			);

			const result = await extendLicense(
				id,
				validatedData.extensionDays,
				validatedData.reason,
				adminUserId,
			);

			if (result.success) {
				const response: ApiResponse<{ newExpiryDate: Date }> = {
					success: true,
					data: { newExpiryDate: result.newExpiryDate as Date },
					message: `License extended by ${validatedData.extensionDays} days`,
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to extend license",
					error: {
						code: "LICENSE_EXTENSION_FAILED",
						message: result.error || "Failed to extend license",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.LICENSE_EXTEND);
		}
	},
);

/**
 * POST /api/admin/licenses/:id/suspend
 * Suspend license
 */
router.post(
	"/licenses/:id/suspend",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = suspendLicenseSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.LICENSE_SUSPEND,
				`Admin license suspension request: ${id}`,
				{ body: { licenseId: id, reason: validatedData.reason } },
			);

			const result = await suspendLicense(
				id,
				validatedData.reason,
				validatedData.suspendUntil,
				adminUserId,
			);

			if (result.success) {
				const response: ApiResponse<null> = {
					success: true,
					data: null,
					message: "License suspended successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to suspend license",
					error: {
						code: "LICENSE_SUSPENSION_FAILED",
						message: result.error || "Failed to suspend license",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.LICENSE_SUSPEND);
		}
	},
);

/**
 * POST /api/admin/licenses/:id/reactivate
 * Reactivate suspended license
 */
router.post(
	"/licenses/:id/reactivate",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = reactivateLicenseSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.LICENSE_REACTIVATE,
				`Admin license reactivation request: ${id}`,
				{ body: { licenseId: id, reason: validatedData.reason } },
			);

			const result = await reactivateLicense(
				id,
				validatedData.reason,
				adminUserId,
			);

			if (result.success) {
				const response: ApiResponse<null> = {
					success: true,
					data: null,
					message: "License reactivated successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to reactivate license",
					error: {
						code: "LICENSE_REACTIVATION_FAILED",
						message: result.error || "Failed to reactivate license",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.LICENSE_REACTIVATE);
		}
	},
);

// ============================================================================
// DEVICE MANAGEMENT ROUTES
// ============================================================================

/**
 * GET /api/admin/devices
 * List all devices (with pagination/filters)
 */
router.get(
	"/devices",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { page, limit, search, sortBy, sortOrder, filters } =
				adminPaginationSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.DEVICE_LIST,
				`Admin device list request - Page: ${page}, Limit: ${limit}`,
				{ body: { page, limit, search } },
			);

			const deviceParams = {
				page,
				limit,
				search,
				sortBy,
				sortOrder,
				filters: {
					licenseId: filters?.licenseId,
					status: filters?.status,
					deviceType: filters?.deviceType,
					operatingSystem: filters?.operatingSystem,
				},
			};

			const { devices, total } = await getDevices(deviceParams);
			const pagination = paginate(total, page, limit);

			const response: ApiResponse<{
				devices: typeof devices;
				pagination: typeof pagination;
			}> = {
				success: true,
				data: {
					devices,
					pagination,
				},
				message: `Retrieved ${devices.length} devices`,
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.DEVICE_LIST);
		}
	},
);

/**
 * GET /api/admin/devices/:id
 * Get specific device details
 */
router.get(
	"/devices/:id",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;

			Logger.info(
				EndpointPrefix.DEVICE_DETAILS,
				`Admin device detail request: ${id}`,
				{ body: { deviceId: id } },
			);

			const { device, error } = await getDeviceById(id);

			if (device) {
				const response: ApiResponse<typeof device> = {
					success: true,
					data: device,
					message: "Device retrieved successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: error || "Device not found",
					error: {
						code: "DEVICE_NOT_FOUND",
						message: error || "Device not found",
					},
				};
				res.status(404).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.DEVICE_DETAILS);
		}
	},
);

/**
 * DELETE /api/admin/devices/:id
 * Remove device from license
 */
router.delete(
	"/devices/:id",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			Logger.info(
				EndpointPrefix.DEVICE_REMOVE,
				`Admin device removal request: ${id}`,
				{ body: { deviceId: id, adminUserId } },
			);

			const result = await adminRemoveDevice(id, adminUserId);

			if (result.success) {
				const response: ApiResponse<null> = {
					success: true,
					data: null,
					message: "Device removed successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to remove device",
					error: {
						code: "DEVICE_REMOVAL_FAILED",
						message: result.error || "Failed to remove device",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.DEVICE_REMOVE);
		}
	},
);

/**
 * PATCH /api/admin/devices/:id
 * Update device metadata
 */
router.patch(
	"/devices/:id",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = adminUpdateDeviceSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.DEVICE_UPDATE,
				`Admin device update request: ${id}`,
				{ body: { deviceId: id, changes: validatedData } },
			);

			const result = await adminUpdateDevice(id, validatedData, adminUserId);

			if (result.success) {
				const response: ApiResponse<typeof result.device> = {
					success: true,
					data: result.device,
					message: "Device updated successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to update device",
					error: {
						code: "DEVICE_UPDATE_FAILED",
						message: result.error || "Failed to update device",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.DEVICE_UPDATE);
		}
	},
);

// ============================================================================
// DEVICE EXPANSION MANAGEMENT ROUTES
// ============================================================================

/**
 * GET /api/admin/device-expansions
 * List all device expansions (with pagination/filters)
 */
router.get(
	"/device-expansions",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { page, limit, search, sortBy, sortOrder, filters } =
				adminPaginationSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.DEVICE_EXPANSION_LIST,
				`Admin device expansion list request - Page: ${page}, Limit: ${limit}`,
				{ body: { page, limit, search } },
			);

			const expansionParams = {
				page,
				limit,
				search,
				sortBy,
				sortOrder,
				filters: {
					licenseId: filters?.licenseId,
					status: filters?.status,
				},
			};

			const { expansions, total } = await getDeviceExpansions(expansionParams);
			const pagination = paginate(total, page, limit);

			const response: ApiResponse<{
				expansions: typeof expansions;
				pagination: typeof pagination;
			}> = {
				success: true,
				data: {
					expansions,
					pagination,
				},
				message: `Retrieved ${expansions.length} device expansions`,
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.DEVICE_EXPANSION_LIST);
		}
	},
);

/**
 * POST /api/admin/device-expansions/:id/process
 * Manually process expansion
 */
router.post(
	"/device-expansions/:id/process",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = processDeviceExpansionSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.DEVICE_EXPANSION_PROCESS,
				`Admin device expansion processing request: ${id}`,
				{ body: { expansionId: id, approve: validatedData.approve } },
			);

			const result = await processDeviceExpansionAdmin(
				id,
				validatedData.approve,
				validatedData.adminNotes,
				adminUserId,
			);

			if (result.success) {
				const response: ApiResponse<null> = {
					success: true,
					data: null,
					message: `Device expansion ${validatedData.approve ? "approved" : "rejected"} successfully`,
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to process device expansion",
					error: {
						code: "DEVICE_EXPANSION_PROCESS_FAILED",
						message: result.error || "Failed to process device expansion",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.DEVICE_EXPANSION_PROCESS);
		}
	},
);

// ============================================================================
// REFUND MANAGEMENT ROUTES
// ============================================================================

/**
 * POST /api/admin/refunds/:id/approve
 * Approve refund request
 */
router.post(
	"/refunds/:id/approve",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = approveRefundSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.REFUND_APPROVE,
				`Admin refund approval request: ${id}`,
				{ body: { refundId: id, adminUserId } },
			);

			const result = await approveRefund(
				id,
				validatedData.reason,
				validatedData.adminNotes,
				adminUserId,
			);

			if (result.success) {
				const response: ApiResponse<null> = {
					success: true,
					data: null,
					message: "Refund request approved successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to approve refund request",
					error: {
						code: "REFUND_APPROVAL_FAILED",
						message: result.error || "Failed to approve refund request",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.REFUND_APPROVE);
		}
	},
);

/**
 * POST /api/admin/refunds/:id/reject
 * Reject refund request
 */
router.post(
	"/refunds/:id/reject",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = rejectRefundSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.REFUND_REJECT,
				`Admin refund rejection request: ${id}`,
				{ body: { refundId: id, reason: validatedData.reason } },
			);

			const result = await rejectRefund(
				id,
				validatedData.reason,
				validatedData.adminNotes,
				adminUserId,
			);

			if (result.success) {
				const response: ApiResponse<null> = {
					success: true,
					data: null,
					message: "Refund request rejected successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to reject refund request",
					error: {
						code: "REFUND_REJECTION_FAILED",
						message: result.error || "Failed to reject refund request",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.REFUND_REJECT);
		}
	},
);

/**
 * POST /api/admin/refunds/:id/process
 * Process approved refund via Stripe
 */
router.post(
	"/refunds/:id/process",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = processRefundSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.REFUND_PROCESS,
				`Admin refund processing request: ${id}`,
				{ body: { refundId: id, amount: validatedData.amount } },
			);

			const result = await processRefund(
				id,
				validatedData.amount,
				validatedData.reason,
				adminUserId,
			);

			if (result.success) {
				const response: ApiResponse<{ stripeRefundId: string }> = {
					success: true,
					data: { stripeRefundId: result.stripeRefundId! },
					message: "Refund processed successfully via Stripe",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to process refund",
					error: {
						code: "REFUND_PROCESSING_FAILED",
						message: result.error || "Failed to process refund",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.REFUND_PROCESS);
		}
	},
);

/**
 * PATCH /api/admin/refunds/:id
 * Update refund request (add admin notes)
 */
router.patch(
	"/refunds/:id",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = adminUpdateRefundSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.REFUND_UPDATE,
				`Admin refund update request: ${id}`,
				{ body: { refundId: id, changes: validatedData } },
			);

			const result = await adminUpdateRefund(id, validatedData, adminUserId);

			if (result.success) {
				const response: ApiResponse<typeof result.refundRequest> = {
					success: true,
					data: result.refundRequest,
					message: "Refund request updated successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to update refund request",
					error: {
						code: "REFUND_UPDATE_FAILED",
						message: result.error || "Failed to update refund request",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.REFUND_UPDATE);
		}
	},
);

// ============================================================================
// USER INVITATION MANAGEMENT ROUTES
// ============================================================================

/**
 * GET /api/admin/invitations
 * List all pending invitations
 */
router.get(
	"/invitations",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { page, limit, search, sortBy, sortOrder, filters } =
				adminPaginationSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.USER_LIST,
				`Admin invitation list request - Page: ${page}, Limit: ${limit}`,
				{ body: { page, limit, search } },
			);

			const invitationParams = {
				page,
				limit,
				search,
				sortBy,
				sortOrder,
				filters: {}, // No additional filters for invitations
			};

			const { invitations, total } = await getUserInvitations(invitationParams);
			const pagination = paginate(total, page, limit);

			const response: ApiResponse<{
				invitations: typeof invitations;
				pagination: typeof pagination;
			}> = {
				success: true,
				data: {
					invitations,
					pagination,
				},
				message: `Retrieved ${invitations.length} invitations`,
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_LIST);
		}
	},
);

/**
 * POST /api/admin/invitations
 * Send invitation to new admin
 */
router.post(
	"/invitations",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const validatedData = createUserInvitationSchema.parse(req.body);
			const adminUserId = req.user?.id;

			Logger.info(
				EndpointPrefix.USER_INVITE,
				`Admin invitation creation request: ${validatedData.email}`,
				{
					body: {
						email: validatedData.email,
						role: validatedData.role,
						adminUserId,
					},
				},
			);

			const result = await createUserInvitation({
				...validatedData,
				sentBy: adminUserId!,
			});

			if (result.success) {
				const response: ApiResponse<typeof result.invitation> = {
					success: true,
					data: result.invitation,
					message: "Invitation sent successfully",
				};
				res.status(201).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to send invitation",
					error: {
						code: "INVITATION_FAILED",
						message: result.error || "Failed to send invitation",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_INVITE);
		}
	},
);

/**
 * POST /api/admin/invitations/:id/resend
 * Resend invitation email
 */
router.post(
	"/invitations/:id/resend",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = resendInvitationSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.USER_INVITE,
				`Admin invitation resend request: ${id}`,
				{ body: { invitationId: id, adminUserId } },
			);

			const result = await resendInvitation(
				id,
				validatedData.customMessage,
				adminUserId,
			);

			if (result.success) {
				const response: ApiResponse<null> = {
					success: true,
					data: null,
					message: "Invitation resent successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to resend invitation",
					error: {
						code: "INVITATION_RESEND_FAILED",
						message: result.error || "Failed to resend invitation",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_INVITE);
		}
	},
);

/**
 * DELETE /api/admin/invitations/:id
 * Cancel/delete invitation
 */
router.delete(
	"/invitations/:id",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			Logger.info(
				EndpointPrefix.USER_INVITE,
				`Admin invitation cancellation request: ${id}`,
				{ body: { invitationId: id, adminUserId } },
			);

			const result = await cancelInvitation(id, adminUserId);

			if (result.success) {
				const response: ApiResponse<null> = {
					success: true,
					data: null,
					message: "Invitation cancelled successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to cancel invitation",
					error: {
						code: "INVITATION_CANCEL_FAILED",
						message: result.error || "Failed to cancel invitation",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_INVITE);
		}
	},
);

/**
 * PATCH /api/admin/invitations/:id
 * Update invitation details
 */
router.patch(
	"/invitations/:id",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = updateInvitationSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.USER_INVITE,
				`Admin invitation update request: ${id}`,
				{ body: { invitationId: id, changes: validatedData } },
			);

			const result = await updateInvitation(id, validatedData, adminUserId);

			if (result.success) {
				const response: ApiResponse<typeof result.invitation> = {
					success: true,
					data: result.invitation,
					message: "Invitation updated successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to update invitation",
					error: {
						code: "INVITATION_UPDATE_FAILED",
						message: result.error || "Failed to update invitation",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_INVITE);
		}
	},
);

// ============================================================================
// AUDIT LOG ROUTES
// ============================================================================

/**
 * GET /api/admin/audit-logs
 * List audit logs (with filters)
 */
router.get(
	"/audit-logs",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const {
				page,
				limit,
				search,
				sortBy,
				sortOrder,
				filters: queryFilters,
			} = adminPaginationSchema.parse(req.query);

			// Legacy support: extract filters from query params for backward compatibility
			const { action, customerEmail, licenseId } = req.query;

			Logger.info(
				EndpointPrefix.USER_LIST,
				`Admin audit log list request - Page: ${page}, Limit: ${limit}`,
				{
					body: {
						page,
						limit,
						search,
						sortBy,
						sortOrder,
						queryFilters,
						action,
						customerEmail,
						licenseId,
					},
				},
			);

			// Build filters object - prioritize queryFilters, fallback to legacy query params
			const auditFilters: any = { ...queryFilters };
			if (!auditFilters.action && action && typeof action === "string")
				auditFilters.action = action;
			if (
				!auditFilters.customerEmail &&
				customerEmail &&
				typeof customerEmail === "string"
			)
				auditFilters.customerEmail = customerEmail;
			if (!auditFilters.licenseId && licenseId && typeof licenseId === "string")
				auditFilters.licenseId = licenseId;

			const auditParams = {
				page,
				limit,
				search,
				sortBy,
				sortOrder,
				filters: auditFilters,
			};

			const { logs, total } = await getAuditLogs(auditParams);
			const pagination = paginate(total, page, limit);

			const response: ApiResponse<{
				logs: typeof logs;
				pagination: typeof pagination;
			}> = {
				success: true,
				data: {
					logs,
					pagination,
				},
				message: `Retrieved ${logs.length} audit logs`,
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_LIST);
		}
	},
);

// ============================================================================
// USER MANAGEMENT ROUTES
// ============================================================================

/**
 * GET /api/admin/users
 * List all admin users with pagination and filtering
 */
router.get(
	"/users",
	requireAuth,
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { page, limit, search, sortBy, sortOrder, filters } =
				adminPaginationSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.USER_LIST,
				`Listing users - Page: ${page}, Limit: ${limit}`,
				{ body: { search, filters } },
			);

			const userParams = {
				page,
				limit,
				search,
				sortBy,
				sortOrder,
				filters: {
					role: filters?.role,
					isActive:
						filters?.isActive === "true"
							? true
							: filters?.isActive === "false"
								? false
								: undefined,
				},
			};

			const { users, total } = await getUsers(userParams);

			const response: ApiResponse<{
				users: typeof users;
				pagination: {
					page: number;
					limit: number;
					total: number;
					totalPages: number;
				};
			}> = {
				success: true,
				data: {
					users,
					pagination: {
						page,
						limit,
						total,
						totalPages: Math.ceil(total / limit),
					},
				},
				message: `Retrieved ${users.length} users`,
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_LIST);
		}
	},
);

/**
 * GET /api/admin/users/:id
 * Get specific admin user details
 */
router.get(
	"/users/:id",
	requireAuth,
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;

			Logger.info(
				EndpointPrefix.USER_LIST,
				`Getting user details for ID: ${id}`,
			);

			const user = await getUserById(id);

			if (!user) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "User not found",
					error: {
						code: "USER_NOT_FOUND",
						message: "The specified user does not exist",
					},
				};
				res.status(404).json(response);
				return;
			}

			const response: ApiResponse<typeof user> = {
				success: true,
				data: user,
				message: "User retrieved successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_LIST);
		}
	},
);

/**
 * POST /api/admin/users
 * Create new admin user
 */
router.post(
	"/users",
	requireAuth,
	requireRole("SUPER_ADMIN"),
	async (req, res) => {
		try {
			const userData = createUserSchema.parse(req.body);
			const currentUserId = req.user?.id;

			if (!currentUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: {
						code: "UNAUTHORIZED",
						message: "User ID not found in request",
					},
				};
				res.status(401).json(response);
				return;
			}

			Logger.info(
				EndpointPrefix.USER_CREATE,
				`Creating new user: ${userData.email}`,
				{ body: { role: userData.role, createdBy: currentUserId } },
			);

			const result = await createUser({
				...userData,
				createdBy: currentUserId,
			});

			if (!result.success) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to create user",
					error: {
						code: "USER_CREATION_FAILED",
						message: result.error || "Failed to create user",
					},
				};
				res.status(400).json(response);
				return;
			}

			const response: ApiResponse<typeof result.user> = {
				success: true,
				data: result.user,
				message: "User created successfully",
			};

			res.status(201).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_CREATE);
		}
	},
);

/**
 * PATCH /api/admin/users/:id
 * Update admin user (role, status, etc.)
 */
router.patch(
	"/users/:id",
	requireAuth,
	requireRole("SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const updateData = updateUserSchema.parse(req.body);
			const currentUserId = req.user?.id;

			if (!currentUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: {
						code: "UNAUTHORIZED",
						message: "User ID not found in request",
					},
				};
				res.status(401).json(response);
				return;
			}

			Logger.info(EndpointPrefix.USER_UPDATE, `Updating user: ${id}`, {
				body: { updateData, updatedBy: currentUserId },
			});

			const result = await updateUser({
				userId: id,
				...updateData,
				updatedBy: currentUserId,
			});

			if (!result.success) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to update user",
					error: {
						code: "USER_UPDATE_FAILED",
						message: result.error || "Failed to update user",
					},
				};
				res.status(400).json(response);
				return;
			}

			const response: ApiResponse<typeof result.user> = {
				success: true,
				data: result.user,
				message: "User updated successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_UPDATE);
		}
	},
);

/**
 * DELETE /api/admin/users/:id
 * Deactivate admin user
 */
router.delete(
	"/users/:id",
	requireAuth,
	requireRole("SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const currentUserId = req.user?.id;

			if (!currentUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: {
						code: "UNAUTHORIZED",
						message: "User ID not found in request",
					},
				};
				res.status(401).json(response);
				return;
			}

			Logger.info(EndpointPrefix.USER_DELETE, `Deactivating user: ${id}`, {
				body: { deactivatedBy: currentUserId },
			});

			const result = await updateUser({
				userId: id,
				isActive: false,
				updatedBy: currentUserId,
			});

			if (!result.success) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to deactivate user",
					error: {
						code: "USER_DEACTIVATION_FAILED",
						message: result.error || "Failed to deactivate user",
					},
				};
				res.status(400).json(response);
				return;
			}

			const response: ApiResponse<typeof result.user> = {
				success: true,
				data: result.user,
				message: "User deactivated successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_DELETE);
		}
	},
);

/**
 * POST /api/admin/users/:id/reactivate
 * Reactivate admin user
 */
router.post(
	"/users/:id/reactivate",
	requireAuth,
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Authentication required",
					error: { code: "UNAUTHORIZED", message: "Authentication required" },
				};
				return res.status(401).json(response);
			}

			const validatedData = reactivateUserSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.USER_REACTIVATE,
				`Admin user reactivation request: ${id}`,
				{ body: { userId: id, reason: validatedData.reason } },
			);

			const result = await reactivateUser(
				id,
				validatedData.reason,
				adminUserId,
			);

			if (result.success) {
				const response: ApiResponse<typeof result.user> = {
					success: true,
					data: result.user,
					message: "User reactivated successfully",
				};
				res.status(200).json(response);
			} else {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: result.error || "Failed to reactivate user",
					error: {
						code: "USER_REACTIVATION_FAILED",
						message: result.error || "Failed to reactivate user",
					},
				};
				res.status(400).json(response);
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.USER_REACTIVATE);
		}
	},
);

// ============================================================================
// ANALYTICS & REPORTING ROUTES
// ============================================================================

/**
 * GET /api/admin/dashboard/stats
 * Get dashboard overview statistics
 */
router.get(
	"/dashboard/stats",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { period } = dashboardStatsSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.ANALYTICS_DASHBOARD,
				`Dashboard stats request - Period: ${period}`,
				{ body: { period } },
			);

			const stats = await getDashboardStats(period);

			const response: ApiResponse<typeof stats> = {
				success: true,
				data: stats,
				message: "Dashboard statistics retrieved successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.ANALYTICS_DASHBOARD);
		}
	},
);

/**
 * GET /api/admin/dashboard/revenue
 * Revenue analytics (monthly/yearly)
 */
router.get(
	"/dashboard/revenue",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { period, year } = revenueAnalyticsSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.ANALYTICS_REVENUE,
				`Revenue analytics request - Period: ${period}, Year: ${year}`,
				{ body: { period, year } },
			);

			const analytics = await getRevenueAnalytics(period, year);

			const response: ApiResponse<typeof analytics> = {
				success: true,
				data: analytics,
				message: "Revenue analytics retrieved successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.ANALYTICS_REVENUE);
		}
	},
);

/**
 * GET /api/admin/dashboard/licenses
 * License analytics (growth, types, etc.)
 */
router.get(
	"/dashboard/licenses",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { period, groupBy } = licenseAnalyticsSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.ANALYTICS_LICENSES,
				`License analytics request - Period: ${period}, GroupBy: ${groupBy}`,
				{ body: { period, groupBy } },
			);

			const analytics = await getLicenseAnalytics(period, groupBy);

			const response: ApiResponse<typeof analytics> = {
				success: true,
				data: analytics,
				message: "License analytics retrieved successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.ANALYTICS_LICENSES);
		}
	},
);

/**
 * GET /api/admin/dashboard/devices
 * Device analytics (registrations, active devices)
 */
router.get(
	"/dashboard/devices",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { period, groupBy } = deviceAnalyticsSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.ANALYTICS_DEVICES,
				`Device analytics request - Period: ${period}, GroupBy: ${groupBy}`,
				{ body: { period, groupBy } },
			);

			const analytics = await getDeviceAnalytics(period, groupBy);

			const response: ApiResponse<typeof analytics> = {
				success: true,
				data: analytics,
				message: "Device analytics retrieved successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.ANALYTICS_DEVICES);
		}
	},
);

// ============================================================================
// REPORT EXPORT ROUTES
// ============================================================================

/**
 * GET /api/admin/reports/licenses
 * License report (CSV export)
 */
router.get(
	"/reports/licenses",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { format, startDate, endDate, filters } = reportExportSchema.parse(
				req.query,
			);

			Logger.info(
				EndpointPrefix.REPORTS_EXPORT,
				`License report export request - Format: ${format}`,
				{ body: { format, startDate, endDate, filters } },
			);

			const report = await generateLicenseReport({
				format,
				filters: {
					...filters,
					startDate: startDate ? new Date(startDate) : undefined,
					endDate: endDate ? new Date(endDate) : undefined,
				},
			});

			res.setHeader("Content-Type", report.contentType);
			res.setHeader(
				"Content-Disposition",
				`attachment; filename="${report.filename}"`,
			);
			res.status(200).send(report.data);
		} catch (error) {
			handleError(res, error, EndpointPrefix.REPORTS_EXPORT);
		}
	},
);

/**
 * GET /api/admin/reports/revenue
 * Revenue report (CSV export)
 */
router.get(
	"/reports/revenue",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { format, startDate, endDate, filters } = reportExportSchema.parse(
				req.query,
			);

			Logger.info(
				EndpointPrefix.REPORTS_EXPORT,
				`Revenue report export request - Format: ${format}`,
				{ body: { format, startDate, endDate, filters } },
			);

			const report = await generateRevenueReport({
				format,
				filters: {
					...filters,
					startDate: startDate ? new Date(startDate) : undefined,
					endDate: endDate ? new Date(endDate) : undefined,
				},
			});

			res.setHeader("Content-Type", report.contentType);
			res.setHeader(
				"Content-Disposition",
				`attachment; filename="${report.filename}"`,
			);
			res.status(200).send(report.data);
		} catch (error) {
			handleError(res, error, EndpointPrefix.REPORTS_EXPORT);
		}
	},
);

/**
 * GET /api/admin/reports/devices
 * Device usage report
 */
router.get(
	"/reports/devices",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { format, startDate, endDate, filters } = reportExportSchema.parse(
				req.query,
			);

			Logger.info(
				EndpointPrefix.REPORTS_EXPORT,
				`Device report export request - Format: ${format}`,
				{ body: { format, startDate, endDate, filters } },
			);

			const report = await generateDeviceReport({
				format,
				filters: {
					...filters,
					startDate: startDate ? new Date(startDate) : undefined,
					endDate: endDate ? new Date(endDate) : undefined,
				},
			});

			res.setHeader("Content-Type", report.contentType);
			res.setHeader(
				"Content-Disposition",
				`attachment; filename="${report.filename}"`,
			);
			res.status(200).send(report.data);
		} catch (error) {
			handleError(res, error, EndpointPrefix.REPORTS_EXPORT);
		}
	},
);

/**
 * GET /api/admin/reports/refunds
 * Refunds report
 */
router.get(
	"/reports/refunds",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { format, startDate, endDate, filters } = reportExportSchema.parse(
				req.query,
			);

			Logger.info(
				EndpointPrefix.REPORTS_EXPORT,
				`Refund report export request - Format: ${format}`,
				{ body: { format, startDate, endDate, filters } },
			);

			const report = await generateRefundReport({
				format,
				filters: {
					...filters,
					startDate: startDate ? new Date(startDate) : undefined,
					endDate: endDate ? new Date(endDate) : undefined,
				},
			});

			res.setHeader("Content-Type", report.contentType);
			res.setHeader(
				"Content-Disposition",
				`attachment; filename="${report.filename}"`,
			);
			res.status(200).send(report.data);
		} catch (error) {
			handleError(res, error, EndpointPrefix.REPORTS_EXPORT);
		}
	},
);

// ============================================================================
// EMAIL & NOTIFICATIONS ROUTES
// ============================================================================

/**
 * GET /api/admin/emails/templates
 * List email templates
 */
router.get(
	"/emails/templates",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { page, limit } = adminPaginationSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.EMAIL_TEMPLATES,
				`Email templates request - Page: ${page}, Limit: ${limit}`,
				{ body: { page, limit } },
			);

			const result = await getEmailTemplates({ page, limit });

			const response: ApiResponse<typeof result> = {
				success: true,
				data: result,
				message: "Email templates retrieved successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.EMAIL_TEMPLATES);
		}
	},
);

/**
 * POST /api/admin/emails/send-bulk
 * Send bulk emails to customers
 */
router.post(
	"/emails/send-bulk",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const validatedData = bulkEmailSchema.parse(req.body);
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				return res.status(401).json({
					success: false,
					error: "Authentication required",
				});
			}

			Logger.info(
				EndpointPrefix.EMAIL_BULK,
				`Bulk email send request - Recipients: ${validatedData.recipients.length}`,
				{
					body: {
						subject: validatedData.subject,
						recipientCount: validatedData.recipients.length,
						templateId: validatedData.templateId,
					},
				},
			);

			const result = await sendBulkEmails(
				{
					...validatedData,
					sendAt: validatedData.sendAt
						? new Date(validatedData.sendAt)
						: undefined,
				},
				adminUserId,
			);

			const response: ApiResponse<typeof result> = {
				success: true,
				data: result,
				message: `Bulk email operation completed: ${result.sent} sent, ${result.failed} failed`,
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.EMAIL_BULK);
		}
	},
);

/**
 * GET /api/admin/emails/delivery-stats
 * Email delivery statistics
 */
router.get(
	"/emails/delivery-stats",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { startDate, endDate, templateId } = emailDeliveryStatsSchema.parse(
				req.query,
			);

			Logger.info(EndpointPrefix.EMAIL_STATS, "Email delivery stats request", {
				body: { startDate, endDate, templateId },
			});

			const stats = await getEmailDeliveryStats(
				startDate ? new Date(startDate) : undefined,
				endDate ? new Date(endDate) : undefined,
				templateId,
			);

			const response: ApiResponse<typeof stats> = {
				success: true,
				data: stats,
				message: "Email delivery statistics retrieved successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.EMAIL_STATS);
		}
	},
);

/**
 * GET /api/admin/notifications
 * List system notifications
 */
router.get(
	"/notifications",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { page, limit, search, filters } = adminPaginationSchema.parse(
				req.query,
			);
			const adminUserId = req.user?.id;

			Logger.info(
				EndpointPrefix.NOTIFICATIONS,
				`System notifications request - Page: ${page}, Limit: ${limit}`,
				{ body: { page, limit, userId: adminUserId } },
			);

			const result = await getSystemNotifications({
				page,
				limit,
				userId: adminUserId,
				type: filters?.type,
				isRead:
					filters?.isRead === "true"
						? true
						: filters?.isRead === "false"
							? false
							: undefined,
			});

			const response: ApiResponse<typeof result> = {
				success: true,
				data: result,
				message: "System notifications retrieved successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.NOTIFICATIONS);
		}
	},
);

/**
 * POST /api/admin/notifications/mark-read
 * Mark notifications as read
 */
router.post(
	"/notifications/mark-read",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { notificationIds } = markNotificationsReadSchema.parse(req.body);
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				return res.status(401).json({
					success: false,
					error: "Authentication required",
				});
			}

			Logger.info(
				EndpointPrefix.NOTIFICATIONS,
				`Mark notifications as read request - Count: ${notificationIds.length}`,
				{ body: { notificationIds, userId: adminUserId } },
			);

			const result = await markNotificationsAsRead(
				notificationIds,
				adminUserId,
			);

			const response: ApiResponse<typeof result> = {
				success: true,
				data: result,
				message: `Successfully marked ${result.markedCount} notifications as read`,
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.NOTIFICATIONS);
		}
	},
);

/**
 * GET /api/admin/notifications/settings
 * Get notification preferences
 */
router.get(
	"/notifications/settings",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				return res.status(401).json({
					success: false,
					error: "Authentication required",
				});
			}

			Logger.info(
				EndpointPrefix.NOTIFICATION_SETTINGS,
				"Get notification settings request",
				{ body: { userId: adminUserId } },
			);

			const settings = await getNotificationSettings(adminUserId);

			const response: ApiResponse<typeof settings> = {
				success: true,
				data: settings,
				message: "Notification settings retrieved successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.NOTIFICATION_SETTINGS);
		}
	},
);

/**
 * PATCH /api/admin/notifications/settings
 * Update notification preferences
 */
router.patch(
	"/notifications/settings",
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const validatedData = notificationSettingsSchema.parse(req.body);
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				return res.status(401).json({
					success: false,
					error: "Authentication required",
				});
			}

			Logger.info(
				EndpointPrefix.NOTIFICATION_SETTINGS,
				"Update notification settings request",
				{ body: { userId: adminUserId, settings: validatedData } },
			);

			const updatedSettings = await updateNotificationSettings(
				adminUserId,
				validatedData,
			);

			const response: ApiResponse<typeof updatedSettings> = {
				success: true,
				data: updatedSettings,
				message: "Notification settings updated successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.NOTIFICATION_SETTINGS);
		}
	},
);

// ============================================================================
// WEBHOOK MANAGEMENT ROUTES
// ============================================================================

/**
 * GET /api/admin/webhooks
 * List webhook events (with filters)
 */
router.get(
	"/webhooks",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { page, limit, eventType, processed, startDate, endDate } = {
				...adminPaginationSchema.parse(req.query),
				...webhookEventsSchema.parse(req.query),
			};

			Logger.info(
				EndpointPrefix.WEBHOOK_MANAGEMENT,
				`Webhook events request - Page: ${page}, Limit: ${limit}`,
				{
					body: {
						page,
						limit,
						eventType,
						processed,
						startDate,
						endDate,
					},
				},
			);

			const result = await getWebhookEvents({
				page,
				limit,
				eventType,
				processed,
				startDate: startDate ? new Date(startDate) : undefined,
				endDate: endDate ? new Date(endDate) : undefined,
			});

			const response: ApiResponse<typeof result> = {
				success: true,
				data: result,
				message: "Webhook events retrieved successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.WEBHOOK_MANAGEMENT);
		}
	},
);

/**
 * POST /api/admin/webhooks/:id/retry
 * Retry failed webhook processing
 */
router.post(
	"/webhooks/:id/retry",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;
			const { reason } = retryWebhookSchema.parse(req.body);
			const adminUserId = req.user?.id;

			if (!adminUserId) {
				return res.status(401).json({
					success: false,
					error: "Authentication required",
				});
			}

			Logger.info(
				EndpointPrefix.WEBHOOK_MANAGEMENT,
				`Webhook retry request: ${id}`,
				{
					body: {
						webhookEventId: id,
						reason,
						adminUserId,
					},
				},
			);

			const result = await retryWebhookProcessing(id, reason, adminUserId);

			if (result.success) {
				const response: ApiResponse<{ success: boolean }> = {
					success: true,
					data: { success: true },
					message: "Webhook retry initiated successfully",
				};

				res.status(200).json(response);
			} else {
				res.status(400).json({
					success: false,
					error: result.error || "Failed to retry webhook processing",
				});
			}
		} catch (error) {
			handleError(res, error, EndpointPrefix.WEBHOOK_MANAGEMENT);
		}
	},
);

/**
 * GET /api/admin/webhooks/stats
 * Webhook processing statistics
 */
router.get(
	"/webhooks/stats",
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { period, eventType } = webhookStatsSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.WEBHOOK_MANAGEMENT,
				`Webhook stats request - Period: ${period}`,
				{
					body: {
						period,
						eventType,
					},
				},
			);

			const stats = await getWebhookStats(period, eventType);

			const response: ApiResponse<typeof stats> = {
				success: true,
				data: stats,
				message: "Webhook statistics retrieved successfully",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.WEBHOOK_MANAGEMENT);
		}
	},
);

export default router;
