import {
	generateLicenseKeySchema,
	validateEmailSchema,
} from "@snapback/shared/schemas";
import { Router } from "express";
import { stripe } from "@/lib/stripe";
import { requireAuth, requireRole } from "@/middleware/auth";
import type { ApiResponse } from "@/types/api";
import { generateLicenseKey } from "@/utils";
import { handleError } from "@/utils/errors";
import { EndpointPrefix, Logger } from "@/utils/logger";
import prisma from "../../prisma";

const router: Router = Router();

// ============================================================================
// PUBLIC SYSTEM ROUTES
// ============================================================================

/**
 * GET /api/health
 * System health check
 */
router.get("/health", async (req, res) => {
	try {
		const startTime = Date.now();

		// Check database connection
		const dbStatus = "healthy";
		let dbResponseTime = 0;
		try {
			const dbStart = Date.now();
			await prisma.$queryRaw`SELECT 1`;
			dbResponseTime = Date.now() - dbStart;
		} catch (error) {
			handleError(res, error, EndpointPrefix.SYSTEM_HEALTH);
		}

		// Check Stripe connection
		const stripeStatus = "healthy";
		let stripeResponseTime = 0;
		try {
			const stripeStart = Date.now();
			await stripe.accounts.retrieve();
			stripeResponseTime = Date.now() - stripeStart;
		} catch (error) {
			handleError(res, error, EndpointPrefix.SYSTEM_HEALTH);
		}

		const totalResponseTime = Date.now() - startTime;
		const overallStatus =
			dbStatus === "healthy" && stripeStatus === "healthy"
				? "healthy"
				: "degraded";

		const healthData = {
			status: overallStatus,
			timestamp: new Date().toISOString(),
			uptime: process.uptime(),
			responseTime: totalResponseTime,
			services: {
				database: {
					status: dbStatus,
					responseTime: dbResponseTime,
				},
				stripe: {
					status: stripeStatus,
					responseTime: stripeResponseTime,
				},
			},
			memory: {
				used:
					Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) /
					100,
				total:
					Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) /
					100,
			},
			environment: process.env.NODE_ENV || "development",
		};

		const response: ApiResponse<typeof healthData> = {
			success: overallStatus === "healthy",
			data: healthData,
			message: `System is ${overallStatus}`,
		};

		const statusCode = overallStatus === "healthy" ? 200 : 503;
		res.status(statusCode).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.SYSTEM_HEALTH);
	}
});

// ============================================================================
// ADMIN SYSTEM ROUTES
// ============================================================================

/**
 * GET /api/admin/system/status
 * Detailed system status (admin only)
 */
router.get(
	"/admin/status",
	requireAuth,
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			Logger.info(EndpointPrefix.SYSTEM_STATUS, "Admin system status request", {
				body: { adminId: req.user?.id },
			});

			// Get database statistics
			const [
				totalLicenses,
				activeLicenses,
				totalDevices,
				activeDevices,
				totalPayments,
				totalRefunds,
				pendingRefunds,
			] = await Promise.all([
				prisma.license.count(),
				prisma.license.count({ where: { status: "ACTIVE" } }),
				prisma.device.count(),
				prisma.device.count({ where: { status: "ACTIVE" } }),
				prisma.paymentIntent.count(),
				prisma.refundRequest.count(),
				prisma.refundRequest.count({ where: { status: "PENDING" } }),
			]);

			// Get recent activity
			const recentLicenses = await prisma.license.count({
				where: {
					createdAt: {
						gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
					},
				},
			});

			const recentPayments = await prisma.paymentIntent.count({
				where: {
					createdAt: {
						gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
					},
				},
			});

			// System metrics
			const memoryUsage = process.memoryUsage();
			const cpuUsage = process.cpuUsage();

			const systemStatus = {
				timestamp: new Date().toISOString(),
				uptime: process.uptime(),
				environment: process.env.NODE_ENV || "development",
				version: process.env.npm_package_version || "unknown",
				nodeVersion: process.version,

				database: {
					totalLicenses,
					activeLicenses,
					totalDevices,
					activeDevices,
					totalPayments,
					totalRefunds,
					pendingRefunds,
				},

				activity: {
					recentLicenses,
					recentPayments,
				},

				system: {
					memory: {
						rss: Math.round((memoryUsage.rss / 1024 / 1024) * 100) / 100,
						heapTotal:
							Math.round((memoryUsage.heapTotal / 1024 / 1024) * 100) / 100,
						heapUsed:
							Math.round((memoryUsage.heapUsed / 1024 / 1024) * 100) / 100,
						external:
							Math.round((memoryUsage.external / 1024 / 1024) * 100) / 100,
					},
					cpu: {
						user: cpuUsage.user,
						system: cpuUsage.system,
					},
				},
			};

			const response: ApiResponse<typeof systemStatus> = {
				success: true,
				data: systemStatus,
				message: "System status retrieved",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.SYSTEM_STATUS);
		}
	},
);

/**
 * GET /api/admin/system/metrics
 * System metrics (DB connections, etc.)
 */
router.get(
	"/admin/metrics",
	requireAuth,
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			Logger.info(
				EndpointPrefix.SYSTEM_METRICS,
				"Admin system metrics request",
				{ body: { adminId: req.user?.id } },
			);

			// Get database connection info
			const dbMetrics = {
				// Note: Prisma doesn't expose connection pool metrics directly
				// These would need to be implemented based on your database setup
				connections: {
					active: "N/A",
					idle: "N/A",
					total: "N/A",
				},
			};

			// Get application metrics
			const appMetrics = {
				uptime: process.uptime(),
				memory: process.memoryUsage(),
				cpu: process.cpuUsage(),
				eventLoop: {
					// These would require additional monitoring libraries
					lag: "N/A",
					utilization: "N/A",
				},
			};

			const metrics = {
				timestamp: new Date().toISOString(),
				database: dbMetrics,
				application: appMetrics,
			};

			const response: ApiResponse<typeof metrics> = {
				success: true,
				data: metrics,
				message: "System metrics retrieved",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.SYSTEM_METRICS);
		}
	},
);

// ============================================================================
// ADMIN UTILITY ROUTES
// ============================================================================

/**
 * POST /api/admin/utils/generate-license-key
 * Generate new license key(s)
 */
router.post(
	"/utils/generate-license-key",
	requireAuth,
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { count } = generateLicenseKeySchema.parse(req.body);

			Logger.info(
				EndpointPrefix.LICENSE_CREATE,
				`Generating ${count} license key(s)`,
				{ body: { count, adminId: req.user?.id } },
			);

			const licenseKeys = [];
			for (let i = 0; i < count; i++) {
				licenseKeys.push(generateLicenseKey());
			}

			const response: ApiResponse<{
				licenseKeys: string[];
				count: number;
			}> = {
				success: true,
				data: {
					licenseKeys,
					count: licenseKeys.length,
				},
				message: `Generated ${licenseKeys.length} license key(s)`,
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.LICENSE_CREATE);
		}
	},
);

/**
 * POST /api/admin/utils/validate-email
 * Validate email deliverability
 */
router.post(
	"/utils/validate-email",
	requireAuth,
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { email } = validateEmailSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.EMAIL_VALIDATION,
				`Email validation request: ${email}`,
				{ body: { email, adminId: req.user?.id } },
			);

			// Basic email validation (could be enhanced with external service)
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			const isValidFormat = emailRegex.test(email);

			// Check if email domain exists (basic check)
			const domain = email.split("@")[1];
			let domainExists = true;

			try {
				// This is a basic check - in production you might want to use a service like ZeroBounce
				const dns = require("dns").promises;
				await dns.resolveMx(domain);
			} catch (error) {
				domainExists = false;
				handleError(res, error, EndpointPrefix.EMAIL_VALIDATION);
			}

			const validation = {
				email,
				valid: isValidFormat && domainExists,
				checks: {
					format: isValidFormat,
					domain: domainExists,
				},
				timestamp: new Date().toISOString(),
			};

			const response: ApiResponse<typeof validation> = {
				success: true,
				data: validation,
				message: `Email validation completed for ${email}`,
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.EMAIL_VALIDATION);
		}
	},
);

/**
 * GET /api/admin/utils/stripe-status
 * Check Stripe connection status
 */
router.get(
	"/utils/stripe-status",
	requireAuth,
	requireRole("ADMIN", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			Logger.info(EndpointPrefix.STRIPE_STATUS, "Stripe status check request", {
				body: { adminId: req.user?.id },
			});

			let stripeStatus = {
				connected: false,
				account: null as any,
				error: null as string | null,
			};

			try {
				const account = await stripe.accounts.retrieve();
				stripeStatus = {
					connected: true,
					account: {
						id: account.id,
						country: account.country,
						defaultCurrency: account.default_currency,
						email: account.email,
						chargesEnabled: account.charges_enabled,
						payoutsEnabled: account.payouts_enabled,
					},
					error: null,
				};
			} catch (error) {
				stripeStatus = {
					connected: false,
					account: null,
					error: error instanceof Error ? error.message : "Unknown error",
				};
				handleError(res, error, EndpointPrefix.STRIPE_STATUS);
			}

			const response: ApiResponse<typeof stripeStatus> = {
				success: stripeStatus.connected,
				data: stripeStatus,
				message: stripeStatus.connected
					? "Stripe connection is healthy"
					: "Stripe connection failed",
			};

			res.status(stripeStatus.connected ? 200 : 503).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.STRIPE_STATUS);
		}
	},
);

export default router;
