import type { z } from "zod";
import { PrismaClient } from "./db/prisma/generated/client";

import type {
	adminPaginationSchema,
	contactSchema,
	createCheckoutSessionSchema,
	createExpansionCheckoutSchema,
	createLicenseSchema,
	createUserInvitationSchema,
	createUserSchema,
	dashboardStatsSchema,
	deviceAnalyticsSchema,
	deviceHeartbeatSchema,
	deviceListSchema,
	generateLicenseKeySchema,
	licenseAnalyticsSchema,
	licenseLookupSchema,
	licensePingSchema,
	licenseStatusSchema,
	paginationSchema,
	pricingSchema,
	refundListSchema,
	registerDeviceSchema,
	requestRefundSchema,
	revenueAnalyticsSchema,
	updateDeviceSchema,
	updateLicenseSchema,
	updateRefundSchema,
	updateUserSchema,
	validateEmailSchema,
	validateLicenseSchema,
	validatePurchaseSchema,
} from "./schemas";

/**
 * API Types and Interfaces
 *
 * This file contains TypeScript types for API requests, responses, and business logic.
 * Database model types are auto-generated by Prisma and should be imported directly
 * from the server app: import type { License } from "prisma/generated/client"
 */

// ============================================================================
// API REQUEST TYPES
// ============================================================================

export type ValidateLicenseRequest = z.infer<typeof validateLicenseSchema>;
export type CreateCheckoutSessionRequest = z.infer<
	typeof createCheckoutSessionSchema
>;
export type CreateExpansionCheckoutRequest = z.infer<
	typeof createExpansionCheckoutSchema
>;
export type LicenseLookupRequest = z.infer<typeof licenseLookupSchema>;
export type LicenseStatusRequest = z.infer<typeof licenseStatusSchema>;
export type LicensePingRequest = z.infer<typeof licensePingSchema>;
export type RegisterDeviceRequest = z.infer<typeof registerDeviceSchema>;
export type UpdateDeviceRequest = z.infer<typeof updateDeviceSchema>;
export type DeviceHeartbeatRequest = z.infer<typeof deviceHeartbeatSchema>;
export type CreateLicenseRequest = z.infer<typeof createLicenseSchema>;
export type UpdateLicenseRequest = z.infer<typeof updateLicenseSchema>;
export type CreateUserInvitationRequest = z.infer<
	typeof createUserInvitationSchema
>;
export type PaginationRequest = z.infer<typeof paginationSchema>;
export type AdminPaginationRequest = z.infer<typeof adminPaginationSchema>;
export type DeviceListRequest = z.infer<typeof deviceListSchema>;
export type RefundListRequest = z.infer<typeof refundListSchema>;
export type PricingRequest = z.infer<typeof pricingSchema>;
export type ValidatePurchaseRequest = z.infer<typeof validatePurchaseSchema>;
export type ContactRequest = z.infer<typeof contactSchema>;
export type RequestRefundRequest = z.infer<typeof requestRefundSchema>;
export type UpdateRefundRequest = z.infer<typeof updateRefundSchema>;
export type ValidateEmailRequest = z.infer<typeof validateEmailSchema>;
export type GenerateLicenseKeyRequest = z.infer<
	typeof generateLicenseKeySchema
>;

// Analytics request types
export type DashboardStatsRequest = z.infer<typeof dashboardStatsSchema>;
export type RevenueAnalyticsRequest = z.infer<typeof revenueAnalyticsSchema>;
export type LicenseAnalyticsRequest = z.infer<typeof licenseAnalyticsSchema>;
export type DeviceAnalyticsRequest = z.infer<typeof deviceAnalyticsSchema>;

// User management request types (schema-derived for consistency)
export type CreateUserRequest = z.infer<typeof createUserSchema>;
export type UpdateUserRequest = z.infer<typeof updateUserSchema>;
export type SendInvitationRequest = z.infer<typeof createUserInvitationSchema>;
// ============================================================================
// API RESPONSE TYPES
// ============================================================================

/**
 * Standardized API response wrapper
 */
export interface ApiResponse<T> {
	success: boolean;
	data: T;
	message: string;
	error?: {
		code: string;
		message: string;
		details?: Record<string, unknown>;
	};
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
	page: number;
	limit: number;
	totalCount: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPreviousPage: boolean;
}

/**
 * Paginated response wrapper
 */
export interface PaginatedResponse<T> {
	data: T[];
	pagination: PaginationMeta;
}

// ============================================================================
// BUSINESS LOGIC TYPES
// ============================================================================

/**
 * License validation response
 */
export interface ValidateLicenseResponse {
	valid: boolean;
	license?: {
		licenseKey: string;
		licenseType: string;
		status: string;
		maxDevices: number;
		usedDevices: number;
		expiresAt?: string;
	};
	device?: {
		id: string;
		status: string;
		firstSeen: string;
		lastSeen: string;
	};
	error?: string;
}

/**
 * Dashboard statistics response
 */
export interface GetDashboardStatsResponse {
	totalLicenses: number;
	activeLicenses: number;
	totalRevenue: number;
	monthlyRevenue: number;
	totalDevices: number;
	activeDevices: number;
	pendingRefunds: number;
	recentGrowth: {
		licenses: number; // percentage change
		revenue: number; // percentage change
		devices: number; // percentage change
	};
}

/**
 * Revenue analytics response
 */
export interface GetRevenueAnalyticsResponse {
	period: "monthly" | "yearly";
	data: Array<{
		period: string; // "2024-01" or "2024"
		revenue: number;
		licenses: number;
		averageValue: number;
	}>;
	totals: {
		revenue: number;
		licenses: number;
		averageValue: number;
	};
}

/**
 * License analytics response
 */
export interface GetLicenseAnalyticsResponse {
	byType: Array<{
		type: string;
		count: number;
		percentage: number;
	}>;
	byStatus: Array<{
		status: string;
		count: number;
		percentage: number;
	}>;
	growth: Array<{
		date: string;
		count: number;
		cumulative: number;
	}>;
}

/**
 * Device analytics response
 */
export interface GetDeviceAnalyticsResponse {
	registrations: Array<{
		date: string;
		count: number;
		cumulative: number;
	}>;
	activeDevices: {
		total: number;
		byLicenseType: Array<{
			type: string;
			count: number;
		}>;
	};
	averageDevicesPerLicense: number;
}

/**
 * User data response
 */
export interface GetUserResponse {
	id: string;
	name: string;
	email: string;
	role: "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "USER" | "VIEWER";
	isActive: boolean;
	createdAt: string;
	updatedAt: string;
	lastLoginAt?: string;
	invitedBy?: string;
	invitedAt?: string;
}

/**
 * License data response (for admin license management)
 */
export interface GetLicenseResponse {
	id: string;
	licenseKey: string;
	licenseType: "TRIAL" | "PRO" | "ENTERPRISE";
	status: "ACTIVE" | "EXPIRED" | "SUSPENDED" | "REFUNDED" | "CANCELLED";
	maxDevices: number;
	usedDevices: number;
	customerEmail: string;
	customerName?: string;
	createdAt: string;
	updatedAt: string;
	expiresAt?: string;
	activatedAt?: string;
	totalPaidAmount: number;
	devices?: GetDeviceResponse[];
	paymentIntent?: {
		id: string;
		stripePaymentIntentId: string;
		amount: number;
		currency: string;
		status: string;
	};
}

/**
 * Device data response (for admin device management)
 */
export interface GetDeviceResponse {
	id: string;
	deviceId: string;
	deviceName?: string;
	deviceType?: string;
	operatingSystem?: string;
	appVersion?: string;
	status: "ACTIVE" | "INACTIVE" | "REMOVED";
	firstSeen: string;
	lastSeen: string;
	licenseId: string;
	license?: {
		licenseKey: string;
		customerEmail: string;
		licenseType: string;
	};
}

/**
 * User invitation data response
 */
export interface GetUserInvitationResponse {
	id: string;
	email: string;
	role: "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "USER" | "VIEWER";
	status: "PENDING" | "ACCEPTED" | "EXPIRED" | "REVOKED";
	token: string;
	expiresAt: string;
	createdAt: string;
	sentBy: {
		id: string;
		name: string;
		email: string;
	};
}

export interface SendInvitationResponse {
	invitation: GetUserInvitationResponse;
	message: string;
}

/**
 * Pricing information
 */
export interface PricingInfo {
	standard: {
		price: number;
		devices: number;
		features: string[];
	};
	extended: {
		price: number;
		devices: number;
		features: string[];
	};
	additionalDevice: {
		price: number;
	};
}

/**
 * Comprehensive error codes used across the application
 * Consolidated from server and shared definitions for consistency
 */
export const ErrorCodes = {
	// Validation Errors
	VALIDATION_ERROR: "VALIDATION_ERROR",
	INVALID_INPUT: "INVALID_INPUT",
	INVALID_REQUEST_DATA: "INVALID_REQUEST_DATA",
	MISSING_REQUIRED_FIELD: "MISSING_REQUIRED_FIELD",
	INVALID_DEVICE_ID: "INVALID_DEVICE_ID",
	INVALID_EMAIL_ADDRESS: "INVALID_EMAIL_ADDRESS",

	// Authentication & Authorization
	UNAUTHORIZED: "UNAUTHORIZED",
	FORBIDDEN: "FORBIDDEN",
	INSUFFICIENT_PERMISSIONS: "INSUFFICIENT_PERMISSIONS",
	INVALID_TOKEN: "INVALID_TOKEN",
	TOKEN_EXPIRED: "TOKEN_EXPIRED",

	// Resource Errors
	NOT_FOUND: "NOT_FOUND",
	ALREADY_EXISTS: "ALREADY_EXISTS",
	CONFLICT: "CONFLICT",

	// License-Specific Errors
	LICENSE_NOT_FOUND: "LICENSE_NOT_FOUND",
	LICENSE_EXPIRED: "LICENSE_EXPIRED",
	LICENSE_INVALID: "LICENSE_INVALID",
	LICENSE_SUSPENDED: "LICENSE_SUSPENDED",
	INVALID_LICENSE_KEY: "INVALID_LICENSE_KEY",
	LICENSE_EXISTS: "LICENSE_EXISTS",
	MAX_DEVICES_REACHED: "MAX_DEVICES_REACHED",
	DEVICE_LIMIT_EXCEEDED: "DEVICE_LIMIT_EXCEEDED",
	DEVICE_NOT_REGISTERED: "DEVICE_NOT_REGISTERED",

	// Device Management
	DEVICE_NOT_FOUND: "DEVICE_NOT_FOUND",
	DEVICE_ALREADY_REGISTERED: "DEVICE_ALREADY_REGISTERED",

	// Payment Errors
	PAYMENT_FAILED: "PAYMENT_FAILED",
	PAYMENT_INCOMPLETE: "PAYMENT_INCOMPLETE",
	PAYMENT_NOT_COMPLETED: "PAYMENT_NOT_COMPLETED",
	PAYMENT_NOT_FOUND: "PAYMENT_NOT_FOUND",
	INVALID_PAYMENT: "INVALID_PAYMENT",
	PAYMENT_INTENT_REQUIRED: "PAYMENT_INTENT_REQUIRED",
	PAYMENT_AMOUNT_MISMATCH: "PAYMENT_AMOUNT_MISMATCH",
	PAYMENT_INTENT_REUSED: "PAYMENT_INTENT_REUSED",
	PAYMENT_VERIFICATION_FAILED: "PAYMENT_VERIFICATION_FAILED",
	PAYMENT_ALREADY_PROCESSED: "PAYMENT_ALREADY_PROCESSED",

	// Refund Management
	REFUND_REQUEST_NOT_FOUND: "REFUND_REQUEST_NOT_FOUND",
	REFUND_REQUEST_EXISTS: "REFUND_REQUEST_EXISTS",
	REFUND_NOT_ELIGIBLE: "REFUND_NOT_ELIGIBLE",
	ALREADY_REFUNDED: "ALREADY_REFUNDED",

	// User Management
	USER_NOT_FOUND: "USER_NOT_FOUND",
	USER_EXISTS: "USER_EXISTS",
	USER_ALREADY_EXISTS: "USER_ALREADY_EXISTS",
	INVALID_ROLE_ASSIGNMENT: "INVALID_ROLE_ASSIGNMENT",
	INVALID_ROLE_CHANGE: "INVALID_ROLE_CHANGE",
	CANNOT_MODIFY_SELF: "CANNOT_MODIFY_SELF",
	ACCOUNT_DEACTIVATED: "ACCOUNT_DEACTIVATED",

	// Invitation Management
	INVITATION_NOT_FOUND: "INVITATION_NOT_FOUND",
	INVITATION_ALREADY_EXISTS: "INVITATION_ALREADY_EXISTS",
	INVITATION_EXPIRED: "INVITATION_EXPIRED",
	INVITATION_INVALID: "INVITATION_INVALID",
	INVITATION_ALREADY_ACCEPTED: "INVITATION_ALREADY_ACCEPTED",

	// Rate Limiting
	RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED",

	// System Errors
	INTERNAL_ERROR: "INTERNAL_ERROR",
	DATABASE_ERROR: "DATABASE_ERROR",
	EXTERNAL_SERVICE_ERROR: "EXTERNAL_SERVICE_ERROR",
	SERVICE_UNAVAILABLE: "SERVICE_UNAVAILABLE",
	ENDPOINT_DEPRECATED: "ENDPOINT_DEPRECATED",
} as const;

export type ErrorCode = (typeof ErrorCodes)[keyof typeof ErrorCodes];

