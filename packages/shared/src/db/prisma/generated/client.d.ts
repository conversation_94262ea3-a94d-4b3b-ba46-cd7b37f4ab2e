/**
 * This file should be your main import to use Prisma. Through it you get access to all the models, enums, and input types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library";
import * as $Enums from "./enums";
import * as $Class from "./internal/class";
import * as Prisma from "./internal/prismaNamespace";
export * as $Enums from './enums';
/**
 * ## Prisma Client
 *
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export declare const PrismaClient: $Class.PrismaClientConstructor;
export type PrismaClient<ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions, Log = $Class.LogOptions<ClientOptions>, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = $Class.PrismaClient<ClientOptions, Log, ExtArgs>;
export { Prisma };
/**
 * Model User
 *
 */
export type User = Prisma.UserModel;
/**
 * Model Session
 *
 */
export type Session = Prisma.SessionModel;
/**
 * Model Account
 *
 */
export type Account = Prisma.AccountModel;
/**
 * Model Verification
 *
 */
export type Verification = Prisma.VerificationModel;
/**
 * Model UserInvitation
 *
 */
export type UserInvitation = Prisma.UserInvitationModel;
/**
 * Model PaymentIntent
 *
 */
export type PaymentIntent = Prisma.PaymentIntentModel;
/**
 * Model WebhookEvent
 *
 */
export type WebhookEvent = Prisma.WebhookEventModel;
/**
 * Model License
 *
 */
export type License = Prisma.LicenseModel;
/**
 * Model Device
 *
 */
export type Device = Prisma.DeviceModel;
/**
 * Model DeviceExpansion
 *
 */
export type DeviceExpansion = Prisma.DeviceExpansionModel;
/**
 * Model RefundRequest
 *
 */
export type RefundRequest = Prisma.RefundRequestModel;
/**
 * Model AuditLog
 *
 */
export type AuditLog = Prisma.AuditLogModel;
/**
 * Model RateLimit
 *
 */
export type RateLimit = Prisma.RateLimitModel;
/**
 * Model SupportTicket
 *
 */
export type SupportTicket = Prisma.SupportTicketModel;
/**
 * Model SupportMessage
 *
 */
export type SupportMessage = Prisma.SupportMessageModel;
export type UserRole = $Enums.UserRole;
export declare const UserRole: {
    readonly SUPER_ADMIN: "SUPER_ADMIN";
    readonly ADMIN: "ADMIN";
    readonly MANAGER: "MANAGER";
    readonly USER: "USER";
    readonly VIEWER: "VIEWER";
};
export type InvitationStatus = $Enums.InvitationStatus;
export declare const InvitationStatus: {
    readonly PENDING: "PENDING";
    readonly ACCEPTED: "ACCEPTED";
    readonly EXPIRED: "EXPIRED";
    readonly REVOKED: "REVOKED";
};
export type LicenseType = $Enums.LicenseType;
export declare const LicenseType: {
    readonly TRIAL: "TRIAL";
    readonly PRO: "PRO";
    readonly ENTERPRISE: "ENTERPRISE";
};
export type LicenseStatus = $Enums.LicenseStatus;
export declare const LicenseStatus: {
    readonly ACTIVE: "ACTIVE";
    readonly EXPIRED: "EXPIRED";
    readonly SUSPENDED: "SUSPENDED";
    readonly REFUNDED: "REFUNDED";
    readonly CANCELLED: "CANCELLED";
};
export type DeviceStatus = $Enums.DeviceStatus;
export declare const DeviceStatus: {
    readonly ACTIVE: "ACTIVE";
    readonly INACTIVE: "INACTIVE";
    readonly REMOVED: "REMOVED";
};
export type PaymentStatus = $Enums.PaymentStatus;
export declare const PaymentStatus: {
    readonly PENDING: "PENDING";
    readonly PROCESSING: "PROCESSING";
    readonly SUCCEEDED: "SUCCEEDED";
    readonly FAILED: "FAILED";
    readonly CANCELLED: "CANCELLED";
    readonly REFUNDED: "REFUNDED";
};
export type PaymentType = $Enums.PaymentType;
export declare const PaymentType: {
    readonly LICENSE_PURCHASE: "LICENSE_PURCHASE";
    readonly DEVICE_EXPANSION: "DEVICE_EXPANSION";
    readonly SUBSCRIPTION: "SUBSCRIPTION";
};
export type DeviceExpansionStatus = $Enums.DeviceExpansionStatus;
export declare const DeviceExpansionStatus: {
    readonly PENDING: "PENDING";
    readonly PROCESSED: "PROCESSED";
    readonly FAILED: "FAILED";
};
export type RefundStatus = $Enums.RefundStatus;
export declare const RefundStatus: {
    readonly PENDING: "PENDING";
    readonly APPROVED: "APPROVED";
    readonly REJECTED: "REJECTED";
    readonly PROCESSED: "PROCESSED";
    readonly FAILED: "FAILED";
};
export type AuditAction = $Enums.AuditAction;
export declare const AuditAction: {
    readonly LICENSE_CREATED: "LICENSE_CREATED";
    readonly TRIAL_LICENSE_CREATED: "TRIAL_LICENSE_CREATED";
    readonly LICENSE_ACTIVATED: "LICENSE_ACTIVATED";
    readonly LICENSE_VALIDATED: "LICENSE_VALIDATED";
    readonly LICENSE_EXPIRED: "LICENSE_EXPIRED";
    readonly LICENSE_SUSPENDED: "LICENSE_SUSPENDED";
    readonly LICENSE_REACTIVATED: "LICENSE_REACTIVATED";
    readonly LICENSE_DELETED: "LICENSE_DELETED";
    readonly LICENSE_EXTENDED: "LICENSE_EXTENDED";
    readonly LICENSE_CANCELLED: "LICENSE_CANCELLED";
    readonly LICENSE_UPGRADED: "LICENSE_UPGRADED";
    readonly DEVICE_REGISTERED: "DEVICE_REGISTERED";
    readonly DEVICE_UPDATED: "DEVICE_UPDATED";
    readonly DEVICE_REMOVED: "DEVICE_REMOVED";
    readonly DEVICE_EXPANSION_PURCHASED: "DEVICE_EXPANSION_PURCHASED";
    readonly DEVICE_EXPANSION_PROCESSED: "DEVICE_EXPANSION_PROCESSED";
    readonly PAYMENT_INITIATED: "PAYMENT_INITIATED";
    readonly PAYMENT_SUCCEEDED: "PAYMENT_SUCCEEDED";
    readonly PAYMENT_FAILED: "PAYMENT_FAILED";
    readonly PAYMENT_REFUNDED: "PAYMENT_REFUNDED";
    readonly REFUND_REQUESTED: "REFUND_REQUESTED";
    readonly REFUND_APPROVED: "REFUND_APPROVED";
    readonly REFUND_REJECTED: "REFUND_REJECTED";
    readonly REFUND_PROCESSED: "REFUND_PROCESSED";
    readonly REFUND_FAILED: "REFUND_FAILED";
    readonly REFUND_UPDATED: "REFUND_UPDATED";
    readonly USER_CREATED: "USER_CREATED";
    readonly USER_UPDATED: "USER_UPDATED";
    readonly USER_ROLE_CHANGED: "USER_ROLE_CHANGED";
    readonly USER_ACTIVATED: "USER_ACTIVATED";
    readonly USER_DEACTIVATED: "USER_DEACTIVATED";
    readonly USER_SUSPENDED: "USER_SUSPENDED";
    readonly USER_DELETED: "USER_DELETED";
    readonly USER_LOGIN: "USER_LOGIN";
    readonly USER_LOGOUT: "USER_LOGOUT";
    readonly USER_EMAIL_VERIFIED: "USER_EMAIL_VERIFIED";
    readonly FIRST_USER_ADMIN_GRANTED: "FIRST_USER_ADMIN_GRANTED";
    readonly INVITATION_SENT: "INVITATION_SENT";
    readonly INVITATION_ACCEPTED: "INVITATION_ACCEPTED";
    readonly INVITATION_EXPIRED: "INVITATION_EXPIRED";
    readonly INVITATION_REVOKED: "INVITATION_REVOKED";
    readonly INVITATION_RESENT: "INVITATION_RESENT";
    readonly INVITATION_UPDATED: "INVITATION_UPDATED";
    readonly INVITATION_CANCELLED: "INVITATION_CANCELLED";
    readonly USER_CREATED_FROM_INVITATION: "USER_CREATED_FROM_INVITATION";
    readonly SUSPICIOUS_ACTIVITY_DETECTED: "SUSPICIOUS_ACTIVITY_DETECTED";
    readonly RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED";
    readonly UNAUTHORIZED_ACCESS_ATTEMPT: "UNAUTHORIZED_ACCESS_ATTEMPT";
    readonly MULTIPLE_DEVICE_LIMIT_EXCEEDED: "MULTIPLE_DEVICE_LIMIT_EXCEEDED";
    readonly WEBHOOK_PROCESSED: "WEBHOOK_PROCESSED";
    readonly WEBHOOK_FAILED: "WEBHOOK_FAILED";
    readonly DATA_EXPORT_REQUESTED: "DATA_EXPORT_REQUESTED";
    readonly DATA_DELETED: "DATA_DELETED";
    readonly EMAIL_SENT: "EMAIL_SENT";
    readonly BULK_EMAIL_SENT: "BULK_EMAIL_SENT";
    readonly EMAIL_DELIVERY_FAILED: "EMAIL_DELIVERY_FAILED";
    readonly EMAIL_TEMPLATE_UPDATED: "EMAIL_TEMPLATE_UPDATED";
    readonly NOTIFICATION_CREATED: "NOTIFICATION_CREATED";
    readonly NOTIFICATIONS_MARKED_READ: "NOTIFICATIONS_MARKED_READ";
    readonly NOTIFICATION_SETTINGS_UPDATED: "NOTIFICATION_SETTINGS_UPDATED";
    readonly SUPPORT_TICKET_CREATED: "SUPPORT_TICKET_CREATED";
    readonly SUPPORT_TICKET_UPDATED: "SUPPORT_TICKET_UPDATED";
    readonly SUPPORT_TICKET_RESOLVED: "SUPPORT_TICKET_RESOLVED";
    readonly SUPPORT_MESSAGE_SENT: "SUPPORT_MESSAGE_SENT";
};
export type TicketStatus = $Enums.TicketStatus;
export declare const TicketStatus: {
    readonly OPEN: "OPEN";
    readonly IN_PROGRESS: "IN_PROGRESS";
    readonly WAITING_CUSTOMER: "WAITING_CUSTOMER";
    readonly RESOLVED: "RESOLVED";
    readonly CLOSED: "CLOSED";
};
export type TicketCategory = $Enums.TicketCategory;
export declare const TicketCategory: {
    readonly LICENSE: "LICENSE";
    readonly BILLING: "BILLING";
    readonly TECHNICAL: "TECHNICAL";
    readonly GENERAL: "GENERAL";
};
export type TicketPriority = $Enums.TicketPriority;
export declare const TicketPriority: {
    readonly LOW: "LOW";
    readonly MEDIUM: "MEDIUM";
    readonly HIGH: "HIGH";
    readonly URGENT: "URGENT";
};
