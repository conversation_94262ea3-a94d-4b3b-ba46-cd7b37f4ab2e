/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file should be your main import to use Prisma. Through it you get access to all the models, enums, and input types.
 *
 * 🟢 You can import this file directly.
 */
import * as process from 'node:process';
import * as path from 'node:path';
import { fileURLToPath } from 'node:url';
const __dirname = path.dirname(fileURLToPath(import.meta.url));
import * as runtime from "@prisma/client/runtime/library";
import * as $Enums from "./enums";
import * as $Class from "./internal/class";
import * as Prisma from "./internal/prismaNamespace";
export * as $Enums from './enums';
/**
 * ## Prisma Client
 *
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export const PrismaClient = $Class.getPrismaClientClass(__dirname);
export { Prisma };
// file annotations for bundling tools to include these files
path.join(__dirname, "libquery_engine-darwin-arm64.dylib.node");
path.join(process.cwd(), "prisma/generated/libquery_engine-darwin-arm64.dylib.node");
export const UserRole = $Enums.UserRole;
export const InvitationStatus = $Enums.InvitationStatus;
export const LicenseType = $Enums.LicenseType;
export const LicenseStatus = $Enums.LicenseStatus;
export const DeviceStatus = $Enums.DeviceStatus;
export const PaymentStatus = $Enums.PaymentStatus;
export const PaymentType = $Enums.PaymentType;
export const DeviceExpansionStatus = $Enums.DeviceExpansionStatus;
export const RefundStatus = $Enums.RefundStatus;
export const AuditAction = $Enums.AuditAction;
export const TicketStatus = $Enums.TicketStatus;
export const TicketCategory = $Enums.TicketCategory;
export const TicketPriority = $Enums.TicketPriority;
