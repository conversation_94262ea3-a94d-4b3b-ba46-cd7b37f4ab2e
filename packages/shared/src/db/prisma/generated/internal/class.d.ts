/**
 * WARNING: This is an internal file that is subject to change!
 *
 * 🛑 Under no circumstances should you import this file directly! 🛑
 *
 * Please import the `PrismaClient` class from the `client.ts` file instead.
 */
import * as runtime from "@prisma/client/runtime/library";
import type * as Prisma from "./prismaNamespace";
export type LogOptions<ClientOptions extends Prisma.PrismaClientOptions> = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never;
export interface PrismaClientConstructor {
    /**
   * ## Prisma Client
   *
   * Type-safe database client for TypeScript
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */
    new <ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions, const U = LogOptions<ClientOptions>, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs>(options?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>): PrismaClient<ClientOptions, U, ExtArgs>;
}
/**
 * ## Prisma Client
 *
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export interface PrismaClient<ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions, U = LogOptions<ClientOptions>, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> {
    [K: symbol]: {
        types: Prisma.TypeMap<ExtArgs>['other'];
    };
    $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;
    /**
     * Connect with the database
     */
    $connect(): runtime.Types.Utils.JsPromise<void>;
    /**
     * Disconnect from the database
     */
    $disconnect(): runtime.Types.Utils.JsPromise<void>;
    /**
     * Add a middleware
     * @deprecated since 4.16.0. For new code, prefer client extensions instead.
     * @see https://pris.ly/d/extensions
     */
    $use(cb: Prisma.Middleware): void;
    /**
       * Executes a prepared raw query and returns the number of affected rows.
       * @example
       * ```
       * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
       * ```
       *
       * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
       */
    $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;
    /**
     * Executes a raw query and returns the number of affected rows.
     * Susceptible to SQL injections, see documentation.
     * @example
     * ```
     * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
     * ```
     *
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
     */
    $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;
    /**
     * Performs a prepared raw query and returns the `SELECT` data.
     * @example
     * ```
     * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
     * ```
     *
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
     */
    $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;
    /**
     * Performs a raw query and returns the `SELECT` data.
     * Susceptible to SQL injections, see documentation.
     * @example
     * ```
     * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
     * ```
     *
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
     */
    $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;
    /**
     * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
     * @example
     * ```
     * const [george, bob, alice] = await prisma.$transaction([
     *   prisma.user.create({ data: { name: 'George' } }),
     *   prisma.user.create({ data: { name: 'Bob' } }),
     *   prisma.user.create({ data: { name: 'Alice' } }),
     * ])
     * ```
     *
     * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
     */
    $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: {
        isolationLevel?: Prisma.TransactionIsolationLevel;
    }): runtime.Types.Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>;
    $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => runtime.Types.Utils.JsPromise<R>, options?: {
        maxWait?: number;
        timeout?: number;
        isolationLevel?: Prisma.TransactionIsolationLevel;
    }): runtime.Types.Utils.JsPromise<R>;
    $extends: runtime.Types.Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, runtime.Types.Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
        extArgs: ExtArgs;
    }>>;
    /**
 * `prisma.user`: Exposes CRUD operations for the **User** model.
  * Example usage:
  * ```ts
  * // Fetch zero or more Users
  * const users = await prisma.user.findMany()
  * ```
  */
    get user(): Prisma.UserDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.session`: Exposes CRUD operations for the **Session** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more Sessions
      * const sessions = await prisma.session.findMany()
      * ```
      */
    get session(): Prisma.SessionDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.account`: Exposes CRUD operations for the **Account** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more Accounts
      * const accounts = await prisma.account.findMany()
      * ```
      */
    get account(): Prisma.AccountDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.verification`: Exposes CRUD operations for the **Verification** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more Verifications
      * const verifications = await prisma.verification.findMany()
      * ```
      */
    get verification(): Prisma.VerificationDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.userInvitation`: Exposes CRUD operations for the **UserInvitation** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more UserInvitations
      * const userInvitations = await prisma.userInvitation.findMany()
      * ```
      */
    get userInvitation(): Prisma.UserInvitationDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.paymentIntent`: Exposes CRUD operations for the **PaymentIntent** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more PaymentIntents
      * const paymentIntents = await prisma.paymentIntent.findMany()
      * ```
      */
    get paymentIntent(): Prisma.PaymentIntentDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.webhookEvent`: Exposes CRUD operations for the **WebhookEvent** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more WebhookEvents
      * const webhookEvents = await prisma.webhookEvent.findMany()
      * ```
      */
    get webhookEvent(): Prisma.WebhookEventDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.license`: Exposes CRUD operations for the **License** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more Licenses
      * const licenses = await prisma.license.findMany()
      * ```
      */
    get license(): Prisma.LicenseDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.device`: Exposes CRUD operations for the **Device** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more Devices
      * const devices = await prisma.device.findMany()
      * ```
      */
    get device(): Prisma.DeviceDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.deviceExpansion`: Exposes CRUD operations for the **DeviceExpansion** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more DeviceExpansions
      * const deviceExpansions = await prisma.deviceExpansion.findMany()
      * ```
      */
    get deviceExpansion(): Prisma.DeviceExpansionDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.refundRequest`: Exposes CRUD operations for the **RefundRequest** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more RefundRequests
      * const refundRequests = await prisma.refundRequest.findMany()
      * ```
      */
    get refundRequest(): Prisma.RefundRequestDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.auditLog`: Exposes CRUD operations for the **AuditLog** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more AuditLogs
      * const auditLogs = await prisma.auditLog.findMany()
      * ```
      */
    get auditLog(): Prisma.AuditLogDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.rateLimit`: Exposes CRUD operations for the **RateLimit** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more RateLimits
      * const rateLimits = await prisma.rateLimit.findMany()
      * ```
      */
    get rateLimit(): Prisma.RateLimitDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.supportTicket`: Exposes CRUD operations for the **SupportTicket** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more SupportTickets
      * const supportTickets = await prisma.supportTicket.findMany()
      * ```
      */
    get supportTicket(): Prisma.SupportTicketDelegate<ExtArgs, ClientOptions>;
    /**
     * `prisma.supportMessage`: Exposes CRUD operations for the **SupportMessage** model.
      * Example usage:
      * ```ts
      * // Fetch zero or more SupportMessages
      * const supportMessages = await prisma.supportMessage.findMany()
      * ```
      */
    get supportMessage(): Prisma.SupportMessageDelegate<ExtArgs, ClientOptions>;
}
export declare function getPrismaClientClass(dirname: string): PrismaClientConstructor;
