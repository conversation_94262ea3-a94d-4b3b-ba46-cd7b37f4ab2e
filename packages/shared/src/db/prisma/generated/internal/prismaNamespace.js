/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * WARNING: This is an internal file that is subject to change!
 *
 * 🛑 Under no circumstances should you import this file directly! 🛑
 *
 * All exports from this file are wrapped under a `Prisma` namespace object in the client.ts file.
 * While this enables partial backward compatibility, it is not part of the stable public API.
 *
 * If you are looking for your Models, Enums, and Input Types, please import them from the respective
 * model files in the `model` directory!
 */
import * as runtime from "@prisma/client/runtime/library";
import {} from "./class";
/**
 * Validator
 */
export const validator = runtime.Public.validator;
/**
 * Prisma Errors
 */
export const PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError;
export const PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError;
export const PrismaClientRustPanicError = runtime.PrismaClientRustPanicError;
export const PrismaClientInitializationError = runtime.PrismaClientInitializationError;
export const PrismaClientValidationError = runtime.PrismaClientValidationError;
/**
 * Re-export of sql-template-tag
 */
export const sql = runtime.sqltag;
export const empty = runtime.empty;
export const join = runtime.join;
export const raw = runtime.raw;
export const Sql = runtime.Sql;
/**
 * Decimal.js
 */
export const Decimal = runtime.Decimal;
export const getExtensionContext = runtime.Extensions.getExtensionContext;
/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
export const prismaVersion = {
    client: "6.13.0",
    engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
};
export const NullTypes = {
    DbNull: runtime.objectEnumValues.classes.DbNull,
    JsonNull: runtime.objectEnumValues.classes.JsonNull,
    AnyNull: runtime.objectEnumValues.classes.AnyNull,
};
/**
 * Helper for filtering JSON entries that have `null` on the database (empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const DbNull = runtime.objectEnumValues.instances.DbNull;
/**
 * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const JsonNull = runtime.objectEnumValues.instances.JsonNull;
/**
 * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
 *
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
 */
export const AnyNull = runtime.objectEnumValues.instances.AnyNull;
export const ModelName = {
    User: 'User',
    Session: 'Session',
    Account: 'Account',
    Verification: 'Verification',
    UserInvitation: 'UserInvitation',
    PaymentIntent: 'PaymentIntent',
    WebhookEvent: 'WebhookEvent',
    License: 'License',
    Device: 'Device',
    DeviceExpansion: 'DeviceExpansion',
    RefundRequest: 'RefundRequest',
    AuditLog: 'AuditLog',
    RateLimit: 'RateLimit',
    SupportTicket: 'SupportTicket',
    SupportMessage: 'SupportMessage'
};
/**
 * Enums
 */
export const TransactionIsolationLevel = runtime.makeStrictEnum({
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
});
export const UserScalarFieldEnum = {
    id: 'id',
    name: 'name',
    email: 'email',
    emailVerified: 'emailVerified',
    image: 'image',
    role: 'role',
    isActive: 'isActive',
    invitedBy: 'invitedBy',
    invitedAt: 'invitedAt',
    lastLoginAt: 'lastLoginAt',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
};
export const SessionScalarFieldEnum = {
    id: 'id',
    expiresAt: 'expiresAt',
    token: 'token',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    ipAddress: 'ipAddress',
    userAgent: 'userAgent',
    userId: 'userId'
};
export const AccountScalarFieldEnum = {
    id: 'id',
    accountId: 'accountId',
    providerId: 'providerId',
    userId: 'userId',
    accessToken: 'accessToken',
    refreshToken: 'refreshToken',
    idToken: 'idToken',
    accessTokenExpiresAt: 'accessTokenExpiresAt',
    refreshTokenExpiresAt: 'refreshTokenExpiresAt',
    scope: 'scope',
    password: 'password',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
};
export const VerificationScalarFieldEnum = {
    id: 'id',
    identifier: 'identifier',
    value: 'value',
    expiresAt: 'expiresAt',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
};
export const UserInvitationScalarFieldEnum = {
    id: 'id',
    email: 'email',
    role: 'role',
    token: 'token',
    status: 'status',
    expiresAt: 'expiresAt',
    sentAt: 'sentAt',
    acceptedAt: 'acceptedAt',
    sentBy: 'sentBy',
    acceptedBy: 'acceptedBy'
};
export const PaymentIntentScalarFieldEnum = {
    id: 'id',
    stripePaymentIntentId: 'stripePaymentIntentId',
    stripeCheckoutSessionId: 'stripeCheckoutSessionId',
    amount: 'amount',
    currency: 'currency',
    status: 'status',
    paymentType: 'paymentType',
    customerEmail: 'customerEmail',
    customerName: 'customerName',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    processedAt: 'processedAt'
};
export const WebhookEventScalarFieldEnum = {
    id: 'id',
    stripeEventId: 'stripeEventId',
    eventType: 'eventType',
    processed: 'processed',
    processedAt: 'processedAt',
    errorMessage: 'errorMessage',
    retryCount: 'retryCount',
    createdAt: 'createdAt',
    paymentIntentId: 'paymentIntentId'
};
export const LicenseScalarFieldEnum = {
    id: 'id',
    licenseKey: 'licenseKey',
    licenseType: 'licenseType',
    status: 'status',
    maxDevices: 'maxDevices',
    usedDevices: 'usedDevices',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    expiresAt: 'expiresAt',
    activatedAt: 'activatedAt',
    customerEmail: 'customerEmail',
    customerName: 'customerName',
    createdBy: 'createdBy',
    paymentIntentId: 'paymentIntentId',
    totalPaidAmount: 'totalPaidAmount',
    refundedAt: 'refundedAt',
    refundReason: 'refundReason',
    refundAmount: 'refundAmount',
    emailSentAt: 'emailSentAt',
    emailDeliveryStatus: 'emailDeliveryStatus'
};
export const DeviceScalarFieldEnum = {
    id: 'id',
    licenseId: 'licenseId',
    deviceHash: 'deviceHash',
    salt: 'salt',
    status: 'status',
    firstSeen: 'firstSeen',
    lastSeen: 'lastSeen',
    removedAt: 'removedAt',
    appVersion: 'appVersion',
    deviceName: 'deviceName',
    deviceType: 'deviceType',
    deviceModel: 'deviceModel',
    operatingSystem: 'operatingSystem',
    architecture: 'architecture',
    screenResolution: 'screenResolution',
    totalMemory: 'totalMemory',
    userNickname: 'userNickname',
    location: 'location',
    notes: 'notes'
};
export const DeviceExpansionScalarFieldEnum = {
    id: 'id',
    licenseId: 'licenseId',
    paymentIntentId: 'paymentIntentId',
    additionalDevices: 'additionalDevices',
    amount: 'amount',
    status: 'status',
    createdAt: 'createdAt',
    processedAt: 'processedAt'
};
export const RefundRequestScalarFieldEnum = {
    id: 'id',
    licenseId: 'licenseId',
    requestedBy: 'requestedBy',
    reason: 'reason',
    status: 'status',
    requestedAmount: 'requestedAmount',
    approvedAmount: 'approvedAmount',
    stripeRefundIds: 'stripeRefundIds',
    adminNotes: 'adminNotes',
    processedBy: 'processedBy',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    processedAt: 'processedAt'
};
export const AuditLogScalarFieldEnum = {
    id: 'id',
    action: 'action',
    licenseKey: 'licenseKey',
    deviceHash: 'deviceHash',
    licenseId: 'licenseId',
    deviceId: 'deviceId',
    actorId: 'actorId',
    actorEmail: 'actorEmail',
    targetId: 'targetId',
    customerEmail: 'customerEmail',
    ipAddress: 'ipAddress',
    userAgent: 'userAgent',
    details: 'details',
    createdAt: 'createdAt'
};
export const RateLimitScalarFieldEnum = {
    id: 'id',
    identifier: 'identifier',
    action: 'action',
    count: 'count',
    windowStart: 'windowStart',
    expiresAt: 'expiresAt'
};
export const SupportTicketScalarFieldEnum = {
    id: 'id',
    ticketId: 'ticketId',
    subject: 'subject',
    description: 'description',
    status: 'status',
    priority: 'priority',
    category: 'category',
    customerEmail: 'customerEmail',
    customerName: 'customerName',
    licenseKey: 'licenseKey',
    assignedTo: 'assignedTo',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    resolvedAt: 'resolvedAt'
};
export const SupportMessageScalarFieldEnum = {
    id: 'id',
    ticketId: 'ticketId',
    message: 'message',
    isInternal: 'isInternal',
    authorEmail: 'authorEmail',
    authorId: 'authorId',
    createdAt: 'createdAt'
};
export const SortOrder = {
    asc: 'asc',
    desc: 'desc'
};
export const NullableJsonNullValueInput = {
    DbNull: DbNull,
    JsonNull: JsonNull
};
export const QueryMode = {
    default: 'default',
    insensitive: 'insensitive'
};
export const NullsOrder = {
    first: 'first',
    last: 'last'
};
export const JsonNullValueFilter = {
    DbNull: DbNull,
    JsonNull: JsonNull,
    AnyNull: AnyNull
};
export const defineExtension = runtime.Extensions.defineExtension;
