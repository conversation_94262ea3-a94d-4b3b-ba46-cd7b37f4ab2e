/**
* This file exports all enum related types from the schema.
*
* 🟢 You can import this file directly.
*/
export declare const UserRole: {
    readonly SUPER_ADMIN: "SUPER_ADMIN";
    readonly ADMIN: "ADMIN";
    readonly MANAGER: "MANAGER";
    readonly USER: "USER";
    readonly VIEWER: "VIEWER";
};
export type UserRole = (typeof UserRole)[keyof typeof UserRole];
export declare const InvitationStatus: {
    readonly PENDING: "PENDING";
    readonly ACCEPTED: "ACCEPTED";
    readonly EXPIRED: "EXPIRED";
    readonly REVOKED: "REVOKED";
};
export type InvitationStatus = (typeof InvitationStatus)[keyof typeof InvitationStatus];
export declare const LicenseType: {
    readonly TRIAL: "TRIAL";
    readonly PRO: "PRO";
    readonly ENTERPRISE: "ENTERPRISE";
};
export type LicenseType = (typeof LicenseType)[keyof typeof LicenseType];
export declare const LicenseStatus: {
    readonly ACTIVE: "ACTIVE";
    readonly EXPIRED: "EXPIRED";
    readonly SUSPENDED: "SUSPENDED";
    readonly REFUNDED: "REFUNDED";
    readonly CANCELLED: "CANCELLED";
};
export type LicenseStatus = (typeof LicenseStatus)[keyof typeof LicenseStatus];
export declare const DeviceStatus: {
    readonly ACTIVE: "ACTIVE";
    readonly INACTIVE: "INACTIVE";
    readonly REMOVED: "REMOVED";
};
export type DeviceStatus = (typeof DeviceStatus)[keyof typeof DeviceStatus];
export declare const PaymentStatus: {
    readonly PENDING: "PENDING";
    readonly PROCESSING: "PROCESSING";
    readonly SUCCEEDED: "SUCCEEDED";
    readonly FAILED: "FAILED";
    readonly CANCELLED: "CANCELLED";
    readonly REFUNDED: "REFUNDED";
};
export type PaymentStatus = (typeof PaymentStatus)[keyof typeof PaymentStatus];
export declare const PaymentType: {
    readonly LICENSE_PURCHASE: "LICENSE_PURCHASE";
    readonly DEVICE_EXPANSION: "DEVICE_EXPANSION";
    readonly SUBSCRIPTION: "SUBSCRIPTION";
};
export type PaymentType = (typeof PaymentType)[keyof typeof PaymentType];
export declare const DeviceExpansionStatus: {
    readonly PENDING: "PENDING";
    readonly PROCESSED: "PROCESSED";
    readonly FAILED: "FAILED";
};
export type DeviceExpansionStatus = (typeof DeviceExpansionStatus)[keyof typeof DeviceExpansionStatus];
export declare const RefundStatus: {
    readonly PENDING: "PENDING";
    readonly APPROVED: "APPROVED";
    readonly REJECTED: "REJECTED";
    readonly PROCESSED: "PROCESSED";
    readonly FAILED: "FAILED";
};
export type RefundStatus = (typeof RefundStatus)[keyof typeof RefundStatus];
export declare const AuditAction: {
    readonly LICENSE_CREATED: "LICENSE_CREATED";
    readonly TRIAL_LICENSE_CREATED: "TRIAL_LICENSE_CREATED";
    readonly LICENSE_ACTIVATED: "LICENSE_ACTIVATED";
    readonly LICENSE_VALIDATED: "LICENSE_VALIDATED";
    readonly LICENSE_EXPIRED: "LICENSE_EXPIRED";
    readonly LICENSE_SUSPENDED: "LICENSE_SUSPENDED";
    readonly LICENSE_REACTIVATED: "LICENSE_REACTIVATED";
    readonly LICENSE_DELETED: "LICENSE_DELETED";
    readonly LICENSE_EXTENDED: "LICENSE_EXTENDED";
    readonly LICENSE_CANCELLED: "LICENSE_CANCELLED";
    readonly LICENSE_UPGRADED: "LICENSE_UPGRADED";
    readonly DEVICE_REGISTERED: "DEVICE_REGISTERED";
    readonly DEVICE_UPDATED: "DEVICE_UPDATED";
    readonly DEVICE_REMOVED: "DEVICE_REMOVED";
    readonly DEVICE_EXPANSION_PURCHASED: "DEVICE_EXPANSION_PURCHASED";
    readonly DEVICE_EXPANSION_PROCESSED: "DEVICE_EXPANSION_PROCESSED";
    readonly PAYMENT_INITIATED: "PAYMENT_INITIATED";
    readonly PAYMENT_SUCCEEDED: "PAYMENT_SUCCEEDED";
    readonly PAYMENT_FAILED: "PAYMENT_FAILED";
    readonly PAYMENT_REFUNDED: "PAYMENT_REFUNDED";
    readonly REFUND_REQUESTED: "REFUND_REQUESTED";
    readonly REFUND_APPROVED: "REFUND_APPROVED";
    readonly REFUND_REJECTED: "REFUND_REJECTED";
    readonly REFUND_PROCESSED: "REFUND_PROCESSED";
    readonly REFUND_FAILED: "REFUND_FAILED";
    readonly REFUND_UPDATED: "REFUND_UPDATED";
    readonly USER_CREATED: "USER_CREATED";
    readonly USER_UPDATED: "USER_UPDATED";
    readonly USER_ROLE_CHANGED: "USER_ROLE_CHANGED";
    readonly USER_ACTIVATED: "USER_ACTIVATED";
    readonly USER_DEACTIVATED: "USER_DEACTIVATED";
    readonly USER_SUSPENDED: "USER_SUSPENDED";
    readonly USER_DELETED: "USER_DELETED";
    readonly USER_LOGIN: "USER_LOGIN";
    readonly USER_LOGOUT: "USER_LOGOUT";
    readonly USER_EMAIL_VERIFIED: "USER_EMAIL_VERIFIED";
    readonly FIRST_USER_ADMIN_GRANTED: "FIRST_USER_ADMIN_GRANTED";
    readonly INVITATION_SENT: "INVITATION_SENT";
    readonly INVITATION_ACCEPTED: "INVITATION_ACCEPTED";
    readonly INVITATION_EXPIRED: "INVITATION_EXPIRED";
    readonly INVITATION_REVOKED: "INVITATION_REVOKED";
    readonly INVITATION_RESENT: "INVITATION_RESENT";
    readonly INVITATION_UPDATED: "INVITATION_UPDATED";
    readonly INVITATION_CANCELLED: "INVITATION_CANCELLED";
    readonly USER_CREATED_FROM_INVITATION: "USER_CREATED_FROM_INVITATION";
    readonly SUSPICIOUS_ACTIVITY_DETECTED: "SUSPICIOUS_ACTIVITY_DETECTED";
    readonly RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED";
    readonly UNAUTHORIZED_ACCESS_ATTEMPT: "UNAUTHORIZED_ACCESS_ATTEMPT";
    readonly MULTIPLE_DEVICE_LIMIT_EXCEEDED: "MULTIPLE_DEVICE_LIMIT_EXCEEDED";
    readonly WEBHOOK_PROCESSED: "WEBHOOK_PROCESSED";
    readonly WEBHOOK_FAILED: "WEBHOOK_FAILED";
    readonly DATA_EXPORT_REQUESTED: "DATA_EXPORT_REQUESTED";
    readonly DATA_DELETED: "DATA_DELETED";
    readonly EMAIL_SENT: "EMAIL_SENT";
    readonly BULK_EMAIL_SENT: "BULK_EMAIL_SENT";
    readonly EMAIL_DELIVERY_FAILED: "EMAIL_DELIVERY_FAILED";
    readonly EMAIL_TEMPLATE_UPDATED: "EMAIL_TEMPLATE_UPDATED";
    readonly NOTIFICATION_CREATED: "NOTIFICATION_CREATED";
    readonly NOTIFICATIONS_MARKED_READ: "NOTIFICATIONS_MARKED_READ";
    readonly NOTIFICATION_SETTINGS_UPDATED: "NOTIFICATION_SETTINGS_UPDATED";
    readonly SUPPORT_TICKET_CREATED: "SUPPORT_TICKET_CREATED";
    readonly SUPPORT_TICKET_UPDATED: "SUPPORT_TICKET_UPDATED";
    readonly SUPPORT_TICKET_RESOLVED: "SUPPORT_TICKET_RESOLVED";
    readonly SUPPORT_MESSAGE_SENT: "SUPPORT_MESSAGE_SENT";
};
export type AuditAction = (typeof AuditAction)[keyof typeof AuditAction];
export declare const TicketStatus: {
    readonly OPEN: "OPEN";
    readonly IN_PROGRESS: "IN_PROGRESS";
    readonly WAITING_CUSTOMER: "WAITING_CUSTOMER";
    readonly RESOLVED: "RESOLVED";
    readonly CLOSED: "CLOSED";
};
export type TicketStatus = (typeof TicketStatus)[keyof typeof TicketStatus];
export declare const TicketCategory: {
    readonly LICENSE: "LICENSE";
    readonly BILLING: "BILLING";
    readonly TECHNICAL: "TECHNICAL";
    readonly GENERAL: "GENERAL";
};
export type TicketCategory = (typeof TicketCategory)[keyof typeof TicketCategory];
export declare const TicketPriority: {
    readonly LOW: "LOW";
    readonly MEDIUM: "MEDIUM";
    readonly HIGH: "HIGH";
    readonly URGENT: "URGENT";
};
export type TicketPriority = (typeof TicketPriority)[keyof typeof TicketPriority];
