
/* !!! This is code generated by <PERSON>rism<PERSON>. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
* This file exports all enum related types from the schema.
*
* 🟢 You can import this file directly.
*/
export const UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  MANAGER: 'MANAGER',
  USER: 'USER',
  VIEWER: 'VIEWER'
} as const

export type UserRole = (typeof UserRole)[keyof typeof UserRole]


export const InvitationStatus = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  EXPIRED: 'EXPIRED',
  REVOKED: 'REVOKED'
} as const

export type InvitationStatus = (typeof InvitationStatus)[keyof typeof InvitationStatus]


export const LicenseType = {
  TRIAL: 'TRIAL',
  PRO: 'PRO',
  ENTERPRISE: 'ENTERPRISE'
} as const

export type LicenseType = (typeof LicenseType)[keyof typeof LicenseType]


export const LicenseStatus = {
  ACTIVE: 'ACTIVE',
  EXPIRED: 'EXPIRED',
  SUSPENDED: 'SUSPENDED',
  REFUNDED: 'REFUNDED',
  CANCELLED: 'CANCELLED'
} as const

export type LicenseStatus = (typeof LicenseStatus)[keyof typeof LicenseStatus]


export const DeviceStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  REMOVED: 'REMOVED'
} as const

export type DeviceStatus = (typeof DeviceStatus)[keyof typeof DeviceStatus]


export const PaymentStatus = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  SUCCEEDED: 'SUCCEEDED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED'
} as const

export type PaymentStatus = (typeof PaymentStatus)[keyof typeof PaymentStatus]


export const PaymentType = {
  LICENSE_PURCHASE: 'LICENSE_PURCHASE',
  DEVICE_EXPANSION: 'DEVICE_EXPANSION',
  SUBSCRIPTION: 'SUBSCRIPTION'
} as const

export type PaymentType = (typeof PaymentType)[keyof typeof PaymentType]


export const DeviceExpansionStatus = {
  PENDING: 'PENDING',
  PROCESSED: 'PROCESSED',
  FAILED: 'FAILED'
} as const

export type DeviceExpansionStatus = (typeof DeviceExpansionStatus)[keyof typeof DeviceExpansionStatus]


export const RefundStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  PROCESSED: 'PROCESSED',
  FAILED: 'FAILED'
} as const

export type RefundStatus = (typeof RefundStatus)[keyof typeof RefundStatus]


export const AuditAction = {
  LICENSE_CREATED: 'LICENSE_CREATED',
  TRIAL_LICENSE_CREATED: 'TRIAL_LICENSE_CREATED',
  LICENSE_ACTIVATED: 'LICENSE_ACTIVATED',
  LICENSE_VALIDATED: 'LICENSE_VALIDATED',
  LICENSE_EXPIRED: 'LICENSE_EXPIRED',
  LICENSE_SUSPENDED: 'LICENSE_SUSPENDED',
  LICENSE_REACTIVATED: 'LICENSE_REACTIVATED',
  LICENSE_DELETED: 'LICENSE_DELETED',
  LICENSE_EXTENDED: 'LICENSE_EXTENDED',
  LICENSE_CANCELLED: 'LICENSE_CANCELLED',
  LICENSE_UPGRADED: 'LICENSE_UPGRADED',
  DEVICE_REGISTERED: 'DEVICE_REGISTERED',
  DEVICE_UPDATED: 'DEVICE_UPDATED',
  DEVICE_REMOVED: 'DEVICE_REMOVED',
  DEVICE_EXPANSION_PURCHASED: 'DEVICE_EXPANSION_PURCHASED',
  DEVICE_EXPANSION_PROCESSED: 'DEVICE_EXPANSION_PROCESSED',
  PAYMENT_INITIATED: 'PAYMENT_INITIATED',
  PAYMENT_SUCCEEDED: 'PAYMENT_SUCCEEDED',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  PAYMENT_REFUNDED: 'PAYMENT_REFUNDED',
  REFUND_REQUESTED: 'REFUND_REQUESTED',
  REFUND_APPROVED: 'REFUND_APPROVED',
  REFUND_REJECTED: 'REFUND_REJECTED',
  REFUND_PROCESSED: 'REFUND_PROCESSED',
  REFUND_FAILED: 'REFUND_FAILED',
  REFUND_UPDATED: 'REFUND_UPDATED',
  USER_CREATED: 'USER_CREATED',
  USER_UPDATED: 'USER_UPDATED',
  USER_ROLE_CHANGED: 'USER_ROLE_CHANGED',
  USER_ACTIVATED: 'USER_ACTIVATED',
  USER_DEACTIVATED: 'USER_DEACTIVATED',
  USER_SUSPENDED: 'USER_SUSPENDED',
  USER_DELETED: 'USER_DELETED',
  USER_LOGIN: 'USER_LOGIN',
  USER_LOGOUT: 'USER_LOGOUT',
  USER_EMAIL_VERIFIED: 'USER_EMAIL_VERIFIED',
  FIRST_USER_ADMIN_GRANTED: 'FIRST_USER_ADMIN_GRANTED',
  INVITATION_SENT: 'INVITATION_SENT',
  INVITATION_ACCEPTED: 'INVITATION_ACCEPTED',
  INVITATION_EXPIRED: 'INVITATION_EXPIRED',
  INVITATION_REVOKED: 'INVITATION_REVOKED',
  INVITATION_RESENT: 'INVITATION_RESENT',
  INVITATION_UPDATED: 'INVITATION_UPDATED',
  INVITATION_CANCELLED: 'INVITATION_CANCELLED',
  USER_CREATED_FROM_INVITATION: 'USER_CREATED_FROM_INVITATION',
  SUSPICIOUS_ACTIVITY_DETECTED: 'SUSPICIOUS_ACTIVITY_DETECTED',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  UNAUTHORIZED_ACCESS_ATTEMPT: 'UNAUTHORIZED_ACCESS_ATTEMPT',
  MULTIPLE_DEVICE_LIMIT_EXCEEDED: 'MULTIPLE_DEVICE_LIMIT_EXCEEDED',
  WEBHOOK_PROCESSED: 'WEBHOOK_PROCESSED',
  WEBHOOK_FAILED: 'WEBHOOK_FAILED',
  DATA_EXPORT_REQUESTED: 'DATA_EXPORT_REQUESTED',
  DATA_DELETED: 'DATA_DELETED',
  EMAIL_SENT: 'EMAIL_SENT',
  BULK_EMAIL_SENT: 'BULK_EMAIL_SENT',
  EMAIL_DELIVERY_FAILED: 'EMAIL_DELIVERY_FAILED',
  EMAIL_TEMPLATE_UPDATED: 'EMAIL_TEMPLATE_UPDATED',
  NOTIFICATION_CREATED: 'NOTIFICATION_CREATED',
  NOTIFICATIONS_MARKED_READ: 'NOTIFICATIONS_MARKED_READ',
  NOTIFICATION_SETTINGS_UPDATED: 'NOTIFICATION_SETTINGS_UPDATED',
  SUPPORT_TICKET_CREATED: 'SUPPORT_TICKET_CREATED',
  SUPPORT_TICKET_UPDATED: 'SUPPORT_TICKET_UPDATED',
  SUPPORT_TICKET_RESOLVED: 'SUPPORT_TICKET_RESOLVED',
  SUPPORT_MESSAGE_SENT: 'SUPPORT_MESSAGE_SENT'
} as const

export type AuditAction = (typeof AuditAction)[keyof typeof AuditAction]


export const TicketStatus = {
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  WAITING_CUSTOMER: 'WAITING_CUSTOMER',
  RESOLVED: 'RESOLVED',
  CLOSED: 'CLOSED'
} as const

export type TicketStatus = (typeof TicketStatus)[keyof typeof TicketStatus]


export const TicketCategory = {
  LICENSE: 'LICENSE',
  BILLING: 'BILLING',
  TECHNICAL: 'TECHNICAL',
  GENERAL: 'GENERAL'
} as const

export type TicketCategory = (typeof TicketCategory)[keyof typeof TicketCategory]


export const TicketPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT'
} as const

export type TicketPriority = (typeof TicketPriority)[keyof typeof TicketPriority]
