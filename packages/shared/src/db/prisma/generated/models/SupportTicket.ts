
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `SupportTicket` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model SupportTicket
 * 
 */
export type SupportTicketModel = runtime.Types.Result.DefaultSelection<Prisma.$SupportTicketPayload>

export type AggregateSupportTicket = {
  _count: SupportTicketCountAggregateOutputType | null
  _min: SupportTicketMinAggregateOutputType | null
  _max: SupportTicketMaxAggregateOutputType | null
}

export type SupportTicketMinAggregateOutputType = {
  id: string | null
  ticketId: string | null
  subject: string | null
  description: string | null
  status: $Enums.TicketStatus | null
  priority: $Enums.TicketPriority | null
  category: $Enums.TicketCategory | null
  customerEmail: string | null
  customerName: string | null
  licenseKey: string | null
  assignedTo: string | null
  createdAt: Date | null
  updatedAt: Date | null
  resolvedAt: Date | null
}

export type SupportTicketMaxAggregateOutputType = {
  id: string | null
  ticketId: string | null
  subject: string | null
  description: string | null
  status: $Enums.TicketStatus | null
  priority: $Enums.TicketPriority | null
  category: $Enums.TicketCategory | null
  customerEmail: string | null
  customerName: string | null
  licenseKey: string | null
  assignedTo: string | null
  createdAt: Date | null
  updatedAt: Date | null
  resolvedAt: Date | null
}

export type SupportTicketCountAggregateOutputType = {
  id: number
  ticketId: number
  subject: number
  description: number
  status: number
  priority: number
  category: number
  customerEmail: number
  customerName: number
  licenseKey: number
  assignedTo: number
  createdAt: number
  updatedAt: number
  resolvedAt: number
  _all: number
}


export type SupportTicketMinAggregateInputType = {
  id?: true
  ticketId?: true
  subject?: true
  description?: true
  status?: true
  priority?: true
  category?: true
  customerEmail?: true
  customerName?: true
  licenseKey?: true
  assignedTo?: true
  createdAt?: true
  updatedAt?: true
  resolvedAt?: true
}

export type SupportTicketMaxAggregateInputType = {
  id?: true
  ticketId?: true
  subject?: true
  description?: true
  status?: true
  priority?: true
  category?: true
  customerEmail?: true
  customerName?: true
  licenseKey?: true
  assignedTo?: true
  createdAt?: true
  updatedAt?: true
  resolvedAt?: true
}

export type SupportTicketCountAggregateInputType = {
  id?: true
  ticketId?: true
  subject?: true
  description?: true
  status?: true
  priority?: true
  category?: true
  customerEmail?: true
  customerName?: true
  licenseKey?: true
  assignedTo?: true
  createdAt?: true
  updatedAt?: true
  resolvedAt?: true
  _all?: true
}

export type SupportTicketAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which SupportTicket to aggregate.
   */
  where?: Prisma.SupportTicketWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of SupportTickets to fetch.
   */
  orderBy?: Prisma.SupportTicketOrderByWithRelationInput | Prisma.SupportTicketOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.SupportTicketWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` SupportTickets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` SupportTickets.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned SupportTickets
  **/
  _count?: true | SupportTicketCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: SupportTicketMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: SupportTicketMaxAggregateInputType
}

export type GetSupportTicketAggregateType<T extends SupportTicketAggregateArgs> = {
      [P in keyof T & keyof AggregateSupportTicket]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSupportTicket[P]>
    : Prisma.GetScalarType<T[P], AggregateSupportTicket[P]>
}




export type SupportTicketGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.SupportTicketWhereInput
  orderBy?: Prisma.SupportTicketOrderByWithAggregationInput | Prisma.SupportTicketOrderByWithAggregationInput[]
  by: Prisma.SupportTicketScalarFieldEnum[] | Prisma.SupportTicketScalarFieldEnum
  having?: Prisma.SupportTicketScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SupportTicketCountAggregateInputType | true
  _min?: SupportTicketMinAggregateInputType
  _max?: SupportTicketMaxAggregateInputType
}

export type SupportTicketGroupByOutputType = {
  id: string
  ticketId: string
  subject: string
  description: string
  status: $Enums.TicketStatus
  priority: $Enums.TicketPriority
  category: $Enums.TicketCategory | null
  customerEmail: string
  customerName: string | null
  licenseKey: string | null
  assignedTo: string | null
  createdAt: Date
  updatedAt: Date
  resolvedAt: Date | null
  _count: SupportTicketCountAggregateOutputType | null
  _min: SupportTicketMinAggregateOutputType | null
  _max: SupportTicketMaxAggregateOutputType | null
}

type GetSupportTicketGroupByPayload<T extends SupportTicketGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SupportTicketGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof SupportTicketGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], SupportTicketGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], SupportTicketGroupByOutputType[P]>
      }
    >
  >



export type SupportTicketWhereInput = {
  AND?: Prisma.SupportTicketWhereInput | Prisma.SupportTicketWhereInput[]
  OR?: Prisma.SupportTicketWhereInput[]
  NOT?: Prisma.SupportTicketWhereInput | Prisma.SupportTicketWhereInput[]
  id?: Prisma.StringFilter<"SupportTicket"> | string
  ticketId?: Prisma.StringFilter<"SupportTicket"> | string
  subject?: Prisma.StringFilter<"SupportTicket"> | string
  description?: Prisma.StringFilter<"SupportTicket"> | string
  status?: Prisma.EnumTicketStatusFilter<"SupportTicket"> | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFilter<"SupportTicket"> | $Enums.TicketPriority
  category?: Prisma.EnumTicketCategoryNullableFilter<"SupportTicket"> | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFilter<"SupportTicket"> | string
  customerName?: Prisma.StringNullableFilter<"SupportTicket"> | string | null
  licenseKey?: Prisma.StringNullableFilter<"SupportTicket"> | string | null
  assignedTo?: Prisma.StringNullableFilter<"SupportTicket"> | string | null
  createdAt?: Prisma.DateTimeFilter<"SupportTicket"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"SupportTicket"> | Date | string
  resolvedAt?: Prisma.DateTimeNullableFilter<"SupportTicket"> | Date | string | null
  assignedToUser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.UserWhereInput> | null
  messages?: Prisma.SupportMessageListRelationFilter
}

export type SupportTicketOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  ticketId?: Prisma.SortOrder
  subject?: Prisma.SortOrder
  description?: Prisma.SortOrder
  status?: Prisma.SortOrder
  priority?: Prisma.SortOrder
  category?: Prisma.SortOrderInput | Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrderInput | Prisma.SortOrder
  licenseKey?: Prisma.SortOrderInput | Prisma.SortOrder
  assignedTo?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  resolvedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  assignedToUser?: Prisma.UserOrderByWithRelationInput
  messages?: Prisma.SupportMessageOrderByRelationAggregateInput
}

export type SupportTicketWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  ticketId?: string
  AND?: Prisma.SupportTicketWhereInput | Prisma.SupportTicketWhereInput[]
  OR?: Prisma.SupportTicketWhereInput[]
  NOT?: Prisma.SupportTicketWhereInput | Prisma.SupportTicketWhereInput[]
  subject?: Prisma.StringFilter<"SupportTicket"> | string
  description?: Prisma.StringFilter<"SupportTicket"> | string
  status?: Prisma.EnumTicketStatusFilter<"SupportTicket"> | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFilter<"SupportTicket"> | $Enums.TicketPriority
  category?: Prisma.EnumTicketCategoryNullableFilter<"SupportTicket"> | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFilter<"SupportTicket"> | string
  customerName?: Prisma.StringNullableFilter<"SupportTicket"> | string | null
  licenseKey?: Prisma.StringNullableFilter<"SupportTicket"> | string | null
  assignedTo?: Prisma.StringNullableFilter<"SupportTicket"> | string | null
  createdAt?: Prisma.DateTimeFilter<"SupportTicket"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"SupportTicket"> | Date | string
  resolvedAt?: Prisma.DateTimeNullableFilter<"SupportTicket"> | Date | string | null
  assignedToUser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.UserWhereInput> | null
  messages?: Prisma.SupportMessageListRelationFilter
}, "id" | "ticketId">

export type SupportTicketOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  ticketId?: Prisma.SortOrder
  subject?: Prisma.SortOrder
  description?: Prisma.SortOrder
  status?: Prisma.SortOrder
  priority?: Prisma.SortOrder
  category?: Prisma.SortOrderInput | Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrderInput | Prisma.SortOrder
  licenseKey?: Prisma.SortOrderInput | Prisma.SortOrder
  assignedTo?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  resolvedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.SupportTicketCountOrderByAggregateInput
  _max?: Prisma.SupportTicketMaxOrderByAggregateInput
  _min?: Prisma.SupportTicketMinOrderByAggregateInput
}

export type SupportTicketScalarWhereWithAggregatesInput = {
  AND?: Prisma.SupportTicketScalarWhereWithAggregatesInput | Prisma.SupportTicketScalarWhereWithAggregatesInput[]
  OR?: Prisma.SupportTicketScalarWhereWithAggregatesInput[]
  NOT?: Prisma.SupportTicketScalarWhereWithAggregatesInput | Prisma.SupportTicketScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"SupportTicket"> | string
  ticketId?: Prisma.StringWithAggregatesFilter<"SupportTicket"> | string
  subject?: Prisma.StringWithAggregatesFilter<"SupportTicket"> | string
  description?: Prisma.StringWithAggregatesFilter<"SupportTicket"> | string
  status?: Prisma.EnumTicketStatusWithAggregatesFilter<"SupportTicket"> | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityWithAggregatesFilter<"SupportTicket"> | $Enums.TicketPriority
  category?: Prisma.EnumTicketCategoryNullableWithAggregatesFilter<"SupportTicket"> | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringWithAggregatesFilter<"SupportTicket"> | string
  customerName?: Prisma.StringNullableWithAggregatesFilter<"SupportTicket"> | string | null
  licenseKey?: Prisma.StringNullableWithAggregatesFilter<"SupportTicket"> | string | null
  assignedTo?: Prisma.StringNullableWithAggregatesFilter<"SupportTicket"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"SupportTicket"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"SupportTicket"> | Date | string
  resolvedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"SupportTicket"> | Date | string | null
}

export type SupportTicketCreateInput = {
  id?: string
  ticketId: string
  subject: string
  description: string
  status?: $Enums.TicketStatus
  priority?: $Enums.TicketPriority
  category?: $Enums.TicketCategory | null
  customerEmail: string
  customerName?: string | null
  licenseKey?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  resolvedAt?: Date | string | null
  assignedToUser?: Prisma.UserCreateNestedOneWithoutAssignedTicketsInput
  messages?: Prisma.SupportMessageCreateNestedManyWithoutTicketInput
}

export type SupportTicketUncheckedCreateInput = {
  id?: string
  ticketId: string
  subject: string
  description: string
  status?: $Enums.TicketStatus
  priority?: $Enums.TicketPriority
  category?: $Enums.TicketCategory | null
  customerEmail: string
  customerName?: string | null
  licenseKey?: string | null
  assignedTo?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  resolvedAt?: Date | string | null
  messages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutTicketInput
}

export type SupportTicketUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumTicketStatusFieldUpdateOperationsInput | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFieldUpdateOperationsInput | $Enums.TicketPriority
  category?: Prisma.NullableEnumTicketCategoryFieldUpdateOperationsInput | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  resolvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  assignedToUser?: Prisma.UserUpdateOneWithoutAssignedTicketsNestedInput
  messages?: Prisma.SupportMessageUpdateManyWithoutTicketNestedInput
}

export type SupportTicketUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumTicketStatusFieldUpdateOperationsInput | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFieldUpdateOperationsInput | $Enums.TicketPriority
  category?: Prisma.NullableEnumTicketCategoryFieldUpdateOperationsInput | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  assignedTo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  resolvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  messages?: Prisma.SupportMessageUncheckedUpdateManyWithoutTicketNestedInput
}

export type SupportTicketCreateManyInput = {
  id?: string
  ticketId: string
  subject: string
  description: string
  status?: $Enums.TicketStatus
  priority?: $Enums.TicketPriority
  category?: $Enums.TicketCategory | null
  customerEmail: string
  customerName?: string | null
  licenseKey?: string | null
  assignedTo?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  resolvedAt?: Date | string | null
}

export type SupportTicketUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumTicketStatusFieldUpdateOperationsInput | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFieldUpdateOperationsInput | $Enums.TicketPriority
  category?: Prisma.NullableEnumTicketCategoryFieldUpdateOperationsInput | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  resolvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type SupportTicketUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumTicketStatusFieldUpdateOperationsInput | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFieldUpdateOperationsInput | $Enums.TicketPriority
  category?: Prisma.NullableEnumTicketCategoryFieldUpdateOperationsInput | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  assignedTo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  resolvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type SupportTicketListRelationFilter = {
  every?: Prisma.SupportTicketWhereInput
  some?: Prisma.SupportTicketWhereInput
  none?: Prisma.SupportTicketWhereInput
}

export type SupportTicketOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type SupportTicketCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  ticketId?: Prisma.SortOrder
  subject?: Prisma.SortOrder
  description?: Prisma.SortOrder
  status?: Prisma.SortOrder
  priority?: Prisma.SortOrder
  category?: Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrder
  assignedTo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  resolvedAt?: Prisma.SortOrder
}

export type SupportTicketMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  ticketId?: Prisma.SortOrder
  subject?: Prisma.SortOrder
  description?: Prisma.SortOrder
  status?: Prisma.SortOrder
  priority?: Prisma.SortOrder
  category?: Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrder
  assignedTo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  resolvedAt?: Prisma.SortOrder
}

export type SupportTicketMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  ticketId?: Prisma.SortOrder
  subject?: Prisma.SortOrder
  description?: Prisma.SortOrder
  status?: Prisma.SortOrder
  priority?: Prisma.SortOrder
  category?: Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrder
  assignedTo?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  resolvedAt?: Prisma.SortOrder
}

export type SupportTicketScalarRelationFilter = {
  is?: Prisma.SupportTicketWhereInput
  isNot?: Prisma.SupportTicketWhereInput
}

export type SupportTicketCreateNestedManyWithoutAssignedToUserInput = {
  create?: Prisma.XOR<Prisma.SupportTicketCreateWithoutAssignedToUserInput, Prisma.SupportTicketUncheckedCreateWithoutAssignedToUserInput> | Prisma.SupportTicketCreateWithoutAssignedToUserInput[] | Prisma.SupportTicketUncheckedCreateWithoutAssignedToUserInput[]
  connectOrCreate?: Prisma.SupportTicketCreateOrConnectWithoutAssignedToUserInput | Prisma.SupportTicketCreateOrConnectWithoutAssignedToUserInput[]
  createMany?: Prisma.SupportTicketCreateManyAssignedToUserInputEnvelope
  connect?: Prisma.SupportTicketWhereUniqueInput | Prisma.SupportTicketWhereUniqueInput[]
}

export type SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput = {
  create?: Prisma.XOR<Prisma.SupportTicketCreateWithoutAssignedToUserInput, Prisma.SupportTicketUncheckedCreateWithoutAssignedToUserInput> | Prisma.SupportTicketCreateWithoutAssignedToUserInput[] | Prisma.SupportTicketUncheckedCreateWithoutAssignedToUserInput[]
  connectOrCreate?: Prisma.SupportTicketCreateOrConnectWithoutAssignedToUserInput | Prisma.SupportTicketCreateOrConnectWithoutAssignedToUserInput[]
  createMany?: Prisma.SupportTicketCreateManyAssignedToUserInputEnvelope
  connect?: Prisma.SupportTicketWhereUniqueInput | Prisma.SupportTicketWhereUniqueInput[]
}

export type SupportTicketUpdateManyWithoutAssignedToUserNestedInput = {
  create?: Prisma.XOR<Prisma.SupportTicketCreateWithoutAssignedToUserInput, Prisma.SupportTicketUncheckedCreateWithoutAssignedToUserInput> | Prisma.SupportTicketCreateWithoutAssignedToUserInput[] | Prisma.SupportTicketUncheckedCreateWithoutAssignedToUserInput[]
  connectOrCreate?: Prisma.SupportTicketCreateOrConnectWithoutAssignedToUserInput | Prisma.SupportTicketCreateOrConnectWithoutAssignedToUserInput[]
  upsert?: Prisma.SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInput | Prisma.SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInput[]
  createMany?: Prisma.SupportTicketCreateManyAssignedToUserInputEnvelope
  set?: Prisma.SupportTicketWhereUniqueInput | Prisma.SupportTicketWhereUniqueInput[]
  disconnect?: Prisma.SupportTicketWhereUniqueInput | Prisma.SupportTicketWhereUniqueInput[]
  delete?: Prisma.SupportTicketWhereUniqueInput | Prisma.SupportTicketWhereUniqueInput[]
  connect?: Prisma.SupportTicketWhereUniqueInput | Prisma.SupportTicketWhereUniqueInput[]
  update?: Prisma.SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInput | Prisma.SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInput[]
  updateMany?: Prisma.SupportTicketUpdateManyWithWhereWithoutAssignedToUserInput | Prisma.SupportTicketUpdateManyWithWhereWithoutAssignedToUserInput[]
  deleteMany?: Prisma.SupportTicketScalarWhereInput | Prisma.SupportTicketScalarWhereInput[]
}

export type SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput = {
  create?: Prisma.XOR<Prisma.SupportTicketCreateWithoutAssignedToUserInput, Prisma.SupportTicketUncheckedCreateWithoutAssignedToUserInput> | Prisma.SupportTicketCreateWithoutAssignedToUserInput[] | Prisma.SupportTicketUncheckedCreateWithoutAssignedToUserInput[]
  connectOrCreate?: Prisma.SupportTicketCreateOrConnectWithoutAssignedToUserInput | Prisma.SupportTicketCreateOrConnectWithoutAssignedToUserInput[]
  upsert?: Prisma.SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInput | Prisma.SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInput[]
  createMany?: Prisma.SupportTicketCreateManyAssignedToUserInputEnvelope
  set?: Prisma.SupportTicketWhereUniqueInput | Prisma.SupportTicketWhereUniqueInput[]
  disconnect?: Prisma.SupportTicketWhereUniqueInput | Prisma.SupportTicketWhereUniqueInput[]
  delete?: Prisma.SupportTicketWhereUniqueInput | Prisma.SupportTicketWhereUniqueInput[]
  connect?: Prisma.SupportTicketWhereUniqueInput | Prisma.SupportTicketWhereUniqueInput[]
  update?: Prisma.SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInput | Prisma.SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInput[]
  updateMany?: Prisma.SupportTicketUpdateManyWithWhereWithoutAssignedToUserInput | Prisma.SupportTicketUpdateManyWithWhereWithoutAssignedToUserInput[]
  deleteMany?: Prisma.SupportTicketScalarWhereInput | Prisma.SupportTicketScalarWhereInput[]
}

export type EnumTicketStatusFieldUpdateOperationsInput = {
  set?: $Enums.TicketStatus
}

export type EnumTicketPriorityFieldUpdateOperationsInput = {
  set?: $Enums.TicketPriority
}

export type NullableEnumTicketCategoryFieldUpdateOperationsInput = {
  set?: $Enums.TicketCategory | null
}

export type SupportTicketCreateNestedOneWithoutMessagesInput = {
  create?: Prisma.XOR<Prisma.SupportTicketCreateWithoutMessagesInput, Prisma.SupportTicketUncheckedCreateWithoutMessagesInput>
  connectOrCreate?: Prisma.SupportTicketCreateOrConnectWithoutMessagesInput
  connect?: Prisma.SupportTicketWhereUniqueInput
}

export type SupportTicketUpdateOneRequiredWithoutMessagesNestedInput = {
  create?: Prisma.XOR<Prisma.SupportTicketCreateWithoutMessagesInput, Prisma.SupportTicketUncheckedCreateWithoutMessagesInput>
  connectOrCreate?: Prisma.SupportTicketCreateOrConnectWithoutMessagesInput
  upsert?: Prisma.SupportTicketUpsertWithoutMessagesInput
  connect?: Prisma.SupportTicketWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.SupportTicketUpdateToOneWithWhereWithoutMessagesInput, Prisma.SupportTicketUpdateWithoutMessagesInput>, Prisma.SupportTicketUncheckedUpdateWithoutMessagesInput>
}

export type SupportTicketCreateWithoutAssignedToUserInput = {
  id?: string
  ticketId: string
  subject: string
  description: string
  status?: $Enums.TicketStatus
  priority?: $Enums.TicketPriority
  category?: $Enums.TicketCategory | null
  customerEmail: string
  customerName?: string | null
  licenseKey?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  resolvedAt?: Date | string | null
  messages?: Prisma.SupportMessageCreateNestedManyWithoutTicketInput
}

export type SupportTicketUncheckedCreateWithoutAssignedToUserInput = {
  id?: string
  ticketId: string
  subject: string
  description: string
  status?: $Enums.TicketStatus
  priority?: $Enums.TicketPriority
  category?: $Enums.TicketCategory | null
  customerEmail: string
  customerName?: string | null
  licenseKey?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  resolvedAt?: Date | string | null
  messages?: Prisma.SupportMessageUncheckedCreateNestedManyWithoutTicketInput
}

export type SupportTicketCreateOrConnectWithoutAssignedToUserInput = {
  where: Prisma.SupportTicketWhereUniqueInput
  create: Prisma.XOR<Prisma.SupportTicketCreateWithoutAssignedToUserInput, Prisma.SupportTicketUncheckedCreateWithoutAssignedToUserInput>
}

export type SupportTicketCreateManyAssignedToUserInputEnvelope = {
  data: Prisma.SupportTicketCreateManyAssignedToUserInput | Prisma.SupportTicketCreateManyAssignedToUserInput[]
  skipDuplicates?: boolean
}

export type SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInput = {
  where: Prisma.SupportTicketWhereUniqueInput
  update: Prisma.XOR<Prisma.SupportTicketUpdateWithoutAssignedToUserInput, Prisma.SupportTicketUncheckedUpdateWithoutAssignedToUserInput>
  create: Prisma.XOR<Prisma.SupportTicketCreateWithoutAssignedToUserInput, Prisma.SupportTicketUncheckedCreateWithoutAssignedToUserInput>
}

export type SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInput = {
  where: Prisma.SupportTicketWhereUniqueInput
  data: Prisma.XOR<Prisma.SupportTicketUpdateWithoutAssignedToUserInput, Prisma.SupportTicketUncheckedUpdateWithoutAssignedToUserInput>
}

export type SupportTicketUpdateManyWithWhereWithoutAssignedToUserInput = {
  where: Prisma.SupportTicketScalarWhereInput
  data: Prisma.XOR<Prisma.SupportTicketUpdateManyMutationInput, Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserInput>
}

export type SupportTicketScalarWhereInput = {
  AND?: Prisma.SupportTicketScalarWhereInput | Prisma.SupportTicketScalarWhereInput[]
  OR?: Prisma.SupportTicketScalarWhereInput[]
  NOT?: Prisma.SupportTicketScalarWhereInput | Prisma.SupportTicketScalarWhereInput[]
  id?: Prisma.StringFilter<"SupportTicket"> | string
  ticketId?: Prisma.StringFilter<"SupportTicket"> | string
  subject?: Prisma.StringFilter<"SupportTicket"> | string
  description?: Prisma.StringFilter<"SupportTicket"> | string
  status?: Prisma.EnumTicketStatusFilter<"SupportTicket"> | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFilter<"SupportTicket"> | $Enums.TicketPriority
  category?: Prisma.EnumTicketCategoryNullableFilter<"SupportTicket"> | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFilter<"SupportTicket"> | string
  customerName?: Prisma.StringNullableFilter<"SupportTicket"> | string | null
  licenseKey?: Prisma.StringNullableFilter<"SupportTicket"> | string | null
  assignedTo?: Prisma.StringNullableFilter<"SupportTicket"> | string | null
  createdAt?: Prisma.DateTimeFilter<"SupportTicket"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"SupportTicket"> | Date | string
  resolvedAt?: Prisma.DateTimeNullableFilter<"SupportTicket"> | Date | string | null
}

export type SupportTicketCreateWithoutMessagesInput = {
  id?: string
  ticketId: string
  subject: string
  description: string
  status?: $Enums.TicketStatus
  priority?: $Enums.TicketPriority
  category?: $Enums.TicketCategory | null
  customerEmail: string
  customerName?: string | null
  licenseKey?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  resolvedAt?: Date | string | null
  assignedToUser?: Prisma.UserCreateNestedOneWithoutAssignedTicketsInput
}

export type SupportTicketUncheckedCreateWithoutMessagesInput = {
  id?: string
  ticketId: string
  subject: string
  description: string
  status?: $Enums.TicketStatus
  priority?: $Enums.TicketPriority
  category?: $Enums.TicketCategory | null
  customerEmail: string
  customerName?: string | null
  licenseKey?: string | null
  assignedTo?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  resolvedAt?: Date | string | null
}

export type SupportTicketCreateOrConnectWithoutMessagesInput = {
  where: Prisma.SupportTicketWhereUniqueInput
  create: Prisma.XOR<Prisma.SupportTicketCreateWithoutMessagesInput, Prisma.SupportTicketUncheckedCreateWithoutMessagesInput>
}

export type SupportTicketUpsertWithoutMessagesInput = {
  update: Prisma.XOR<Prisma.SupportTicketUpdateWithoutMessagesInput, Prisma.SupportTicketUncheckedUpdateWithoutMessagesInput>
  create: Prisma.XOR<Prisma.SupportTicketCreateWithoutMessagesInput, Prisma.SupportTicketUncheckedCreateWithoutMessagesInput>
  where?: Prisma.SupportTicketWhereInput
}

export type SupportTicketUpdateToOneWithWhereWithoutMessagesInput = {
  where?: Prisma.SupportTicketWhereInput
  data: Prisma.XOR<Prisma.SupportTicketUpdateWithoutMessagesInput, Prisma.SupportTicketUncheckedUpdateWithoutMessagesInput>
}

export type SupportTicketUpdateWithoutMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumTicketStatusFieldUpdateOperationsInput | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFieldUpdateOperationsInput | $Enums.TicketPriority
  category?: Prisma.NullableEnumTicketCategoryFieldUpdateOperationsInput | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  resolvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  assignedToUser?: Prisma.UserUpdateOneWithoutAssignedTicketsNestedInput
}

export type SupportTicketUncheckedUpdateWithoutMessagesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumTicketStatusFieldUpdateOperationsInput | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFieldUpdateOperationsInput | $Enums.TicketPriority
  category?: Prisma.NullableEnumTicketCategoryFieldUpdateOperationsInput | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  assignedTo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  resolvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type SupportTicketCreateManyAssignedToUserInput = {
  id?: string
  ticketId: string
  subject: string
  description: string
  status?: $Enums.TicketStatus
  priority?: $Enums.TicketPriority
  category?: $Enums.TicketCategory | null
  customerEmail: string
  customerName?: string | null
  licenseKey?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  resolvedAt?: Date | string | null
}

export type SupportTicketUpdateWithoutAssignedToUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumTicketStatusFieldUpdateOperationsInput | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFieldUpdateOperationsInput | $Enums.TicketPriority
  category?: Prisma.NullableEnumTicketCategoryFieldUpdateOperationsInput | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  resolvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  messages?: Prisma.SupportMessageUpdateManyWithoutTicketNestedInput
}

export type SupportTicketUncheckedUpdateWithoutAssignedToUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumTicketStatusFieldUpdateOperationsInput | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFieldUpdateOperationsInput | $Enums.TicketPriority
  category?: Prisma.NullableEnumTicketCategoryFieldUpdateOperationsInput | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  resolvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  messages?: Prisma.SupportMessageUncheckedUpdateManyWithoutTicketNestedInput
}

export type SupportTicketUncheckedUpdateManyWithoutAssignedToUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  subject?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumTicketStatusFieldUpdateOperationsInput | $Enums.TicketStatus
  priority?: Prisma.EnumTicketPriorityFieldUpdateOperationsInput | $Enums.TicketPriority
  category?: Prisma.NullableEnumTicketCategoryFieldUpdateOperationsInput | $Enums.TicketCategory | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  licenseKey?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  resolvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}


/**
 * Count Type SupportTicketCountOutputType
 */

export type SupportTicketCountOutputType = {
  messages: number
}

export type SupportTicketCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  messages?: boolean | SupportTicketCountOutputTypeCountMessagesArgs
}

/**
 * SupportTicketCountOutputType without action
 */
export type SupportTicketCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicketCountOutputType
   */
  select?: Prisma.SupportTicketCountOutputTypeSelect<ExtArgs> | null
}

/**
 * SupportTicketCountOutputType without action
 */
export type SupportTicketCountOutputTypeCountMessagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.SupportMessageWhereInput
}


export type SupportTicketSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  ticketId?: boolean
  subject?: boolean
  description?: boolean
  status?: boolean
  priority?: boolean
  category?: boolean
  customerEmail?: boolean
  customerName?: boolean
  licenseKey?: boolean
  assignedTo?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  resolvedAt?: boolean
  assignedToUser?: boolean | Prisma.SupportTicket$assignedToUserArgs<ExtArgs>
  messages?: boolean | Prisma.SupportTicket$messagesArgs<ExtArgs>
  _count?: boolean | Prisma.SupportTicketCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["supportTicket"]>

export type SupportTicketSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  ticketId?: boolean
  subject?: boolean
  description?: boolean
  status?: boolean
  priority?: boolean
  category?: boolean
  customerEmail?: boolean
  customerName?: boolean
  licenseKey?: boolean
  assignedTo?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  resolvedAt?: boolean
  assignedToUser?: boolean | Prisma.SupportTicket$assignedToUserArgs<ExtArgs>
}, ExtArgs["result"]["supportTicket"]>

export type SupportTicketSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  ticketId?: boolean
  subject?: boolean
  description?: boolean
  status?: boolean
  priority?: boolean
  category?: boolean
  customerEmail?: boolean
  customerName?: boolean
  licenseKey?: boolean
  assignedTo?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  resolvedAt?: boolean
  assignedToUser?: boolean | Prisma.SupportTicket$assignedToUserArgs<ExtArgs>
}, ExtArgs["result"]["supportTicket"]>

export type SupportTicketSelectScalar = {
  id?: boolean
  ticketId?: boolean
  subject?: boolean
  description?: boolean
  status?: boolean
  priority?: boolean
  category?: boolean
  customerEmail?: boolean
  customerName?: boolean
  licenseKey?: boolean
  assignedTo?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  resolvedAt?: boolean
}

export type SupportTicketOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "ticketId" | "subject" | "description" | "status" | "priority" | "category" | "customerEmail" | "customerName" | "licenseKey" | "assignedTo" | "createdAt" | "updatedAt" | "resolvedAt", ExtArgs["result"]["supportTicket"]>
export type SupportTicketInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  assignedToUser?: boolean | Prisma.SupportTicket$assignedToUserArgs<ExtArgs>
  messages?: boolean | Prisma.SupportTicket$messagesArgs<ExtArgs>
  _count?: boolean | Prisma.SupportTicketCountOutputTypeDefaultArgs<ExtArgs>
}
export type SupportTicketIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  assignedToUser?: boolean | Prisma.SupportTicket$assignedToUserArgs<ExtArgs>
}
export type SupportTicketIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  assignedToUser?: boolean | Prisma.SupportTicket$assignedToUserArgs<ExtArgs>
}

export type $SupportTicketPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "SupportTicket"
  objects: {
    assignedToUser: Prisma.$UserPayload<ExtArgs> | null
    messages: Prisma.$SupportMessagePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    ticketId: string
    subject: string
    description: string
    status: $Enums.TicketStatus
    priority: $Enums.TicketPriority
    category: $Enums.TicketCategory | null
    customerEmail: string
    customerName: string | null
    licenseKey: string | null
    assignedTo: string | null
    createdAt: Date
    updatedAt: Date
    resolvedAt: Date | null
  }, ExtArgs["result"]["supportTicket"]>
  composites: {}
}

export type SupportTicketGetPayload<S extends boolean | null | undefined | SupportTicketDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload, S>

export type SupportTicketCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<SupportTicketFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: SupportTicketCountAggregateInputType | true
  }

export interface SupportTicketDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['SupportTicket'], meta: { name: 'SupportTicket' } }
  /**
   * Find zero or one SupportTicket that matches the filter.
   * @param {SupportTicketFindUniqueArgs} args - Arguments to find a SupportTicket
   * @example
   * // Get one SupportTicket
   * const supportTicket = await prisma.supportTicket.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends SupportTicketFindUniqueArgs>(args: Prisma.SelectSubset<T, SupportTicketFindUniqueArgs<ExtArgs>>): Prisma.Prisma__SupportTicketClient<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one SupportTicket that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {SupportTicketFindUniqueOrThrowArgs} args - Arguments to find a SupportTicket
   * @example
   * // Get one SupportTicket
   * const supportTicket = await prisma.supportTicket.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends SupportTicketFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, SupportTicketFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__SupportTicketClient<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first SupportTicket that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportTicketFindFirstArgs} args - Arguments to find a SupportTicket
   * @example
   * // Get one SupportTicket
   * const supportTicket = await prisma.supportTicket.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends SupportTicketFindFirstArgs>(args?: Prisma.SelectSubset<T, SupportTicketFindFirstArgs<ExtArgs>>): Prisma.Prisma__SupportTicketClient<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first SupportTicket that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportTicketFindFirstOrThrowArgs} args - Arguments to find a SupportTicket
   * @example
   * // Get one SupportTicket
   * const supportTicket = await prisma.supportTicket.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends SupportTicketFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, SupportTicketFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__SupportTicketClient<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more SupportTickets that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportTicketFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all SupportTickets
   * const supportTickets = await prisma.supportTicket.findMany()
   * 
   * // Get first 10 SupportTickets
   * const supportTickets = await prisma.supportTicket.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const supportTicketWithIdOnly = await prisma.supportTicket.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends SupportTicketFindManyArgs>(args?: Prisma.SelectSubset<T, SupportTicketFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a SupportTicket.
   * @param {SupportTicketCreateArgs} args - Arguments to create a SupportTicket.
   * @example
   * // Create one SupportTicket
   * const SupportTicket = await prisma.supportTicket.create({
   *   data: {
   *     // ... data to create a SupportTicket
   *   }
   * })
   * 
   */
  create<T extends SupportTicketCreateArgs>(args: Prisma.SelectSubset<T, SupportTicketCreateArgs<ExtArgs>>): Prisma.Prisma__SupportTicketClient<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many SupportTickets.
   * @param {SupportTicketCreateManyArgs} args - Arguments to create many SupportTickets.
   * @example
   * // Create many SupportTickets
   * const supportTicket = await prisma.supportTicket.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends SupportTicketCreateManyArgs>(args?: Prisma.SelectSubset<T, SupportTicketCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many SupportTickets and returns the data saved in the database.
   * @param {SupportTicketCreateManyAndReturnArgs} args - Arguments to create many SupportTickets.
   * @example
   * // Create many SupportTickets
   * const supportTicket = await prisma.supportTicket.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many SupportTickets and only return the `id`
   * const supportTicketWithIdOnly = await prisma.supportTicket.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends SupportTicketCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, SupportTicketCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a SupportTicket.
   * @param {SupportTicketDeleteArgs} args - Arguments to delete one SupportTicket.
   * @example
   * // Delete one SupportTicket
   * const SupportTicket = await prisma.supportTicket.delete({
   *   where: {
   *     // ... filter to delete one SupportTicket
   *   }
   * })
   * 
   */
  delete<T extends SupportTicketDeleteArgs>(args: Prisma.SelectSubset<T, SupportTicketDeleteArgs<ExtArgs>>): Prisma.Prisma__SupportTicketClient<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one SupportTicket.
   * @param {SupportTicketUpdateArgs} args - Arguments to update one SupportTicket.
   * @example
   * // Update one SupportTicket
   * const supportTicket = await prisma.supportTicket.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends SupportTicketUpdateArgs>(args: Prisma.SelectSubset<T, SupportTicketUpdateArgs<ExtArgs>>): Prisma.Prisma__SupportTicketClient<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more SupportTickets.
   * @param {SupportTicketDeleteManyArgs} args - Arguments to filter SupportTickets to delete.
   * @example
   * // Delete a few SupportTickets
   * const { count } = await prisma.supportTicket.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends SupportTicketDeleteManyArgs>(args?: Prisma.SelectSubset<T, SupportTicketDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SupportTickets.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportTicketUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many SupportTickets
   * const supportTicket = await prisma.supportTicket.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends SupportTicketUpdateManyArgs>(args: Prisma.SelectSubset<T, SupportTicketUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SupportTickets and returns the data updated in the database.
   * @param {SupportTicketUpdateManyAndReturnArgs} args - Arguments to update many SupportTickets.
   * @example
   * // Update many SupportTickets
   * const supportTicket = await prisma.supportTicket.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more SupportTickets and only return the `id`
   * const supportTicketWithIdOnly = await prisma.supportTicket.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends SupportTicketUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, SupportTicketUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one SupportTicket.
   * @param {SupportTicketUpsertArgs} args - Arguments to update or create a SupportTicket.
   * @example
   * // Update or create a SupportTicket
   * const supportTicket = await prisma.supportTicket.upsert({
   *   create: {
   *     // ... data to create a SupportTicket
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the SupportTicket we want to update
   *   }
   * })
   */
  upsert<T extends SupportTicketUpsertArgs>(args: Prisma.SelectSubset<T, SupportTicketUpsertArgs<ExtArgs>>): Prisma.Prisma__SupportTicketClient<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of SupportTickets.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportTicketCountArgs} args - Arguments to filter SupportTickets to count.
   * @example
   * // Count the number of SupportTickets
   * const count = await prisma.supportTicket.count({
   *   where: {
   *     // ... the filter for the SupportTickets we want to count
   *   }
   * })
  **/
  count<T extends SupportTicketCountArgs>(
    args?: Prisma.Subset<T, SupportTicketCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SupportTicketCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a SupportTicket.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportTicketAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends SupportTicketAggregateArgs>(args: Prisma.Subset<T, SupportTicketAggregateArgs>): Prisma.PrismaPromise<GetSupportTicketAggregateType<T>>

  /**
   * Group by SupportTicket.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportTicketGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends SupportTicketGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: SupportTicketGroupByArgs['orderBy'] }
      : { orderBy?: SupportTicketGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, SupportTicketGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSupportTicketGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the SupportTicket model
 */
readonly fields: SupportTicketFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for SupportTicket.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__SupportTicketClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  assignedToUser<T extends Prisma.SupportTicket$assignedToUserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.SupportTicket$assignedToUserArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  messages<T extends Prisma.SupportTicket$messagesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.SupportTicket$messagesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the SupportTicket model
 */
export interface SupportTicketFieldRefs {
  readonly id: Prisma.FieldRef<"SupportTicket", 'String'>
  readonly ticketId: Prisma.FieldRef<"SupportTicket", 'String'>
  readonly subject: Prisma.FieldRef<"SupportTicket", 'String'>
  readonly description: Prisma.FieldRef<"SupportTicket", 'String'>
  readonly status: Prisma.FieldRef<"SupportTicket", 'TicketStatus'>
  readonly priority: Prisma.FieldRef<"SupportTicket", 'TicketPriority'>
  readonly category: Prisma.FieldRef<"SupportTicket", 'TicketCategory'>
  readonly customerEmail: Prisma.FieldRef<"SupportTicket", 'String'>
  readonly customerName: Prisma.FieldRef<"SupportTicket", 'String'>
  readonly licenseKey: Prisma.FieldRef<"SupportTicket", 'String'>
  readonly assignedTo: Prisma.FieldRef<"SupportTicket", 'String'>
  readonly createdAt: Prisma.FieldRef<"SupportTicket", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"SupportTicket", 'DateTime'>
  readonly resolvedAt: Prisma.FieldRef<"SupportTicket", 'DateTime'>
}
    

// Custom InputTypes
/**
 * SupportTicket findUnique
 */
export type SupportTicketFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketInclude<ExtArgs> | null
  /**
   * Filter, which SupportTicket to fetch.
   */
  where: Prisma.SupportTicketWhereUniqueInput
}

/**
 * SupportTicket findUniqueOrThrow
 */
export type SupportTicketFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketInclude<ExtArgs> | null
  /**
   * Filter, which SupportTicket to fetch.
   */
  where: Prisma.SupportTicketWhereUniqueInput
}

/**
 * SupportTicket findFirst
 */
export type SupportTicketFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketInclude<ExtArgs> | null
  /**
   * Filter, which SupportTicket to fetch.
   */
  where?: Prisma.SupportTicketWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of SupportTickets to fetch.
   */
  orderBy?: Prisma.SupportTicketOrderByWithRelationInput | Prisma.SupportTicketOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for SupportTickets.
   */
  cursor?: Prisma.SupportTicketWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` SupportTickets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` SupportTickets.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of SupportTickets.
   */
  distinct?: Prisma.SupportTicketScalarFieldEnum | Prisma.SupportTicketScalarFieldEnum[]
}

/**
 * SupportTicket findFirstOrThrow
 */
export type SupportTicketFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketInclude<ExtArgs> | null
  /**
   * Filter, which SupportTicket to fetch.
   */
  where?: Prisma.SupportTicketWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of SupportTickets to fetch.
   */
  orderBy?: Prisma.SupportTicketOrderByWithRelationInput | Prisma.SupportTicketOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for SupportTickets.
   */
  cursor?: Prisma.SupportTicketWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` SupportTickets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` SupportTickets.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of SupportTickets.
   */
  distinct?: Prisma.SupportTicketScalarFieldEnum | Prisma.SupportTicketScalarFieldEnum[]
}

/**
 * SupportTicket findMany
 */
export type SupportTicketFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketInclude<ExtArgs> | null
  /**
   * Filter, which SupportTickets to fetch.
   */
  where?: Prisma.SupportTicketWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of SupportTickets to fetch.
   */
  orderBy?: Prisma.SupportTicketOrderByWithRelationInput | Prisma.SupportTicketOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing SupportTickets.
   */
  cursor?: Prisma.SupportTicketWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` SupportTickets from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` SupportTickets.
   */
  skip?: number
  distinct?: Prisma.SupportTicketScalarFieldEnum | Prisma.SupportTicketScalarFieldEnum[]
}

/**
 * SupportTicket create
 */
export type SupportTicketCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketInclude<ExtArgs> | null
  /**
   * The data needed to create a SupportTicket.
   */
  data: Prisma.XOR<Prisma.SupportTicketCreateInput, Prisma.SupportTicketUncheckedCreateInput>
}

/**
 * SupportTicket createMany
 */
export type SupportTicketCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many SupportTickets.
   */
  data: Prisma.SupportTicketCreateManyInput | Prisma.SupportTicketCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * SupportTicket createManyAndReturn
 */
export type SupportTicketCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * The data used to create many SupportTickets.
   */
  data: Prisma.SupportTicketCreateManyInput | Prisma.SupportTicketCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * SupportTicket update
 */
export type SupportTicketUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketInclude<ExtArgs> | null
  /**
   * The data needed to update a SupportTicket.
   */
  data: Prisma.XOR<Prisma.SupportTicketUpdateInput, Prisma.SupportTicketUncheckedUpdateInput>
  /**
   * Choose, which SupportTicket to update.
   */
  where: Prisma.SupportTicketWhereUniqueInput
}

/**
 * SupportTicket updateMany
 */
export type SupportTicketUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update SupportTickets.
   */
  data: Prisma.XOR<Prisma.SupportTicketUpdateManyMutationInput, Prisma.SupportTicketUncheckedUpdateManyInput>
  /**
   * Filter which SupportTickets to update
   */
  where?: Prisma.SupportTicketWhereInput
  /**
   * Limit how many SupportTickets to update.
   */
  limit?: number
}

/**
 * SupportTicket updateManyAndReturn
 */
export type SupportTicketUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * The data used to update SupportTickets.
   */
  data: Prisma.XOR<Prisma.SupportTicketUpdateManyMutationInput, Prisma.SupportTicketUncheckedUpdateManyInput>
  /**
   * Filter which SupportTickets to update
   */
  where?: Prisma.SupportTicketWhereInput
  /**
   * Limit how many SupportTickets to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * SupportTicket upsert
 */
export type SupportTicketUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketInclude<ExtArgs> | null
  /**
   * The filter to search for the SupportTicket to update in case it exists.
   */
  where: Prisma.SupportTicketWhereUniqueInput
  /**
   * In case the SupportTicket found by the `where` argument doesn't exist, create a new SupportTicket with this data.
   */
  create: Prisma.XOR<Prisma.SupportTicketCreateInput, Prisma.SupportTicketUncheckedCreateInput>
  /**
   * In case the SupportTicket was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.SupportTicketUpdateInput, Prisma.SupportTicketUncheckedUpdateInput>
}

/**
 * SupportTicket delete
 */
export type SupportTicketDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketInclude<ExtArgs> | null
  /**
   * Filter which SupportTicket to delete.
   */
  where: Prisma.SupportTicketWhereUniqueInput
}

/**
 * SupportTicket deleteMany
 */
export type SupportTicketDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which SupportTickets to delete
   */
  where?: Prisma.SupportTicketWhereInput
  /**
   * Limit how many SupportTickets to delete.
   */
  limit?: number
}

/**
 * SupportTicket.assignedToUser
 */
export type SupportTicket$assignedToUserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  where?: Prisma.UserWhereInput
}

/**
 * SupportTicket.messages
 */
export type SupportTicket$messagesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
  where?: Prisma.SupportMessageWhereInput
  orderBy?: Prisma.SupportMessageOrderByWithRelationInput | Prisma.SupportMessageOrderByWithRelationInput[]
  cursor?: Prisma.SupportMessageWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SupportMessageScalarFieldEnum | Prisma.SupportMessageScalarFieldEnum[]
}

/**
 * SupportTicket without action
 */
export type SupportTicketDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportTicket
   */
  select?: Prisma.SupportTicketSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportTicket
   */
  omit?: Prisma.SupportTicketOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportTicketInclude<ExtArgs> | null
}
