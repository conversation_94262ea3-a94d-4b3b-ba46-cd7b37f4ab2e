
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `PaymentIntent` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model PaymentIntent
 * 
 */
export type PaymentIntentModel = runtime.Types.Result.DefaultSelection<Prisma.$PaymentIntentPayload>

export type AggregatePaymentIntent = {
  _count: PaymentIntentCountAggregateOutputType | null
  _avg: PaymentIntentAvgAggregateOutputType | null
  _sum: PaymentIntentSumAggregateOutputType | null
  _min: PaymentIntentMinAggregateOutputType | null
  _max: PaymentIntentMaxAggregateOutputType | null
}

export type PaymentIntentAvgAggregateOutputType = {
  amount: number | null
}

export type PaymentIntentSumAggregateOutputType = {
  amount: number | null
}

export type PaymentIntentMinAggregateOutputType = {
  id: string | null
  stripePaymentIntentId: string | null
  stripeCheckoutSessionId: string | null
  amount: number | null
  currency: string | null
  status: $Enums.PaymentStatus | null
  paymentType: $Enums.PaymentType | null
  customerEmail: string | null
  customerName: string | null
  createdAt: Date | null
  updatedAt: Date | null
  processedAt: Date | null
}

export type PaymentIntentMaxAggregateOutputType = {
  id: string | null
  stripePaymentIntentId: string | null
  stripeCheckoutSessionId: string | null
  amount: number | null
  currency: string | null
  status: $Enums.PaymentStatus | null
  paymentType: $Enums.PaymentType | null
  customerEmail: string | null
  customerName: string | null
  createdAt: Date | null
  updatedAt: Date | null
  processedAt: Date | null
}

export type PaymentIntentCountAggregateOutputType = {
  id: number
  stripePaymentIntentId: number
  stripeCheckoutSessionId: number
  amount: number
  currency: number
  status: number
  paymentType: number
  customerEmail: number
  customerName: number
  createdAt: number
  updatedAt: number
  processedAt: number
  _all: number
}


export type PaymentIntentAvgAggregateInputType = {
  amount?: true
}

export type PaymentIntentSumAggregateInputType = {
  amount?: true
}

export type PaymentIntentMinAggregateInputType = {
  id?: true
  stripePaymentIntentId?: true
  stripeCheckoutSessionId?: true
  amount?: true
  currency?: true
  status?: true
  paymentType?: true
  customerEmail?: true
  customerName?: true
  createdAt?: true
  updatedAt?: true
  processedAt?: true
}

export type PaymentIntentMaxAggregateInputType = {
  id?: true
  stripePaymentIntentId?: true
  stripeCheckoutSessionId?: true
  amount?: true
  currency?: true
  status?: true
  paymentType?: true
  customerEmail?: true
  customerName?: true
  createdAt?: true
  updatedAt?: true
  processedAt?: true
}

export type PaymentIntentCountAggregateInputType = {
  id?: true
  stripePaymentIntentId?: true
  stripeCheckoutSessionId?: true
  amount?: true
  currency?: true
  status?: true
  paymentType?: true
  customerEmail?: true
  customerName?: true
  createdAt?: true
  updatedAt?: true
  processedAt?: true
  _all?: true
}

export type PaymentIntentAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which PaymentIntent to aggregate.
   */
  where?: Prisma.PaymentIntentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PaymentIntents to fetch.
   */
  orderBy?: Prisma.PaymentIntentOrderByWithRelationInput | Prisma.PaymentIntentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.PaymentIntentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PaymentIntents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PaymentIntents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned PaymentIntents
  **/
  _count?: true | PaymentIntentCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: PaymentIntentAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: PaymentIntentSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: PaymentIntentMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: PaymentIntentMaxAggregateInputType
}

export type GetPaymentIntentAggregateType<T extends PaymentIntentAggregateArgs> = {
      [P in keyof T & keyof AggregatePaymentIntent]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregatePaymentIntent[P]>
    : Prisma.GetScalarType<T[P], AggregatePaymentIntent[P]>
}




export type PaymentIntentGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PaymentIntentWhereInput
  orderBy?: Prisma.PaymentIntentOrderByWithAggregationInput | Prisma.PaymentIntentOrderByWithAggregationInput[]
  by: Prisma.PaymentIntentScalarFieldEnum[] | Prisma.PaymentIntentScalarFieldEnum
  having?: Prisma.PaymentIntentScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: PaymentIntentCountAggregateInputType | true
  _avg?: PaymentIntentAvgAggregateInputType
  _sum?: PaymentIntentSumAggregateInputType
  _min?: PaymentIntentMinAggregateInputType
  _max?: PaymentIntentMaxAggregateInputType
}

export type PaymentIntentGroupByOutputType = {
  id: string
  stripePaymentIntentId: string
  stripeCheckoutSessionId: string | null
  amount: number
  currency: string
  status: $Enums.PaymentStatus
  paymentType: $Enums.PaymentType
  customerEmail: string
  customerName: string | null
  createdAt: Date
  updatedAt: Date
  processedAt: Date | null
  _count: PaymentIntentCountAggregateOutputType | null
  _avg: PaymentIntentAvgAggregateOutputType | null
  _sum: PaymentIntentSumAggregateOutputType | null
  _min: PaymentIntentMinAggregateOutputType | null
  _max: PaymentIntentMaxAggregateOutputType | null
}

type GetPaymentIntentGroupByPayload<T extends PaymentIntentGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<PaymentIntentGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof PaymentIntentGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], PaymentIntentGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], PaymentIntentGroupByOutputType[P]>
      }
    >
  >



export type PaymentIntentWhereInput = {
  AND?: Prisma.PaymentIntentWhereInput | Prisma.PaymentIntentWhereInput[]
  OR?: Prisma.PaymentIntentWhereInput[]
  NOT?: Prisma.PaymentIntentWhereInput | Prisma.PaymentIntentWhereInput[]
  id?: Prisma.StringFilter<"PaymentIntent"> | string
  stripePaymentIntentId?: Prisma.StringFilter<"PaymentIntent"> | string
  stripeCheckoutSessionId?: Prisma.StringNullableFilter<"PaymentIntent"> | string | null
  amount?: Prisma.IntFilter<"PaymentIntent"> | number
  currency?: Prisma.StringFilter<"PaymentIntent"> | string
  status?: Prisma.EnumPaymentStatusFilter<"PaymentIntent"> | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFilter<"PaymentIntent"> | $Enums.PaymentType
  customerEmail?: Prisma.StringFilter<"PaymentIntent"> | string
  customerName?: Prisma.StringNullableFilter<"PaymentIntent"> | string | null
  createdAt?: Prisma.DateTimeFilter<"PaymentIntent"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"PaymentIntent"> | Date | string
  processedAt?: Prisma.DateTimeNullableFilter<"PaymentIntent"> | Date | string | null
  licenses?: Prisma.LicenseListRelationFilter
  deviceExpansions?: Prisma.DeviceExpansionListRelationFilter
  webhookEvents?: Prisma.WebhookEventListRelationFilter
}

export type PaymentIntentOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  stripePaymentIntentId?: Prisma.SortOrder
  stripeCheckoutSessionId?: Prisma.SortOrderInput | Prisma.SortOrder
  amount?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  status?: Prisma.SortOrder
  paymentType?: Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  processedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  licenses?: Prisma.LicenseOrderByRelationAggregateInput
  deviceExpansions?: Prisma.DeviceExpansionOrderByRelationAggregateInput
  webhookEvents?: Prisma.WebhookEventOrderByRelationAggregateInput
}

export type PaymentIntentWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  stripePaymentIntentId?: string
  stripeCheckoutSessionId?: string
  AND?: Prisma.PaymentIntentWhereInput | Prisma.PaymentIntentWhereInput[]
  OR?: Prisma.PaymentIntentWhereInput[]
  NOT?: Prisma.PaymentIntentWhereInput | Prisma.PaymentIntentWhereInput[]
  amount?: Prisma.IntFilter<"PaymentIntent"> | number
  currency?: Prisma.StringFilter<"PaymentIntent"> | string
  status?: Prisma.EnumPaymentStatusFilter<"PaymentIntent"> | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFilter<"PaymentIntent"> | $Enums.PaymentType
  customerEmail?: Prisma.StringFilter<"PaymentIntent"> | string
  customerName?: Prisma.StringNullableFilter<"PaymentIntent"> | string | null
  createdAt?: Prisma.DateTimeFilter<"PaymentIntent"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"PaymentIntent"> | Date | string
  processedAt?: Prisma.DateTimeNullableFilter<"PaymentIntent"> | Date | string | null
  licenses?: Prisma.LicenseListRelationFilter
  deviceExpansions?: Prisma.DeviceExpansionListRelationFilter
  webhookEvents?: Prisma.WebhookEventListRelationFilter
}, "id" | "stripePaymentIntentId" | "stripeCheckoutSessionId">

export type PaymentIntentOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  stripePaymentIntentId?: Prisma.SortOrder
  stripeCheckoutSessionId?: Prisma.SortOrderInput | Prisma.SortOrder
  amount?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  status?: Prisma.SortOrder
  paymentType?: Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  processedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.PaymentIntentCountOrderByAggregateInput
  _avg?: Prisma.PaymentIntentAvgOrderByAggregateInput
  _max?: Prisma.PaymentIntentMaxOrderByAggregateInput
  _min?: Prisma.PaymentIntentMinOrderByAggregateInput
  _sum?: Prisma.PaymentIntentSumOrderByAggregateInput
}

export type PaymentIntentScalarWhereWithAggregatesInput = {
  AND?: Prisma.PaymentIntentScalarWhereWithAggregatesInput | Prisma.PaymentIntentScalarWhereWithAggregatesInput[]
  OR?: Prisma.PaymentIntentScalarWhereWithAggregatesInput[]
  NOT?: Prisma.PaymentIntentScalarWhereWithAggregatesInput | Prisma.PaymentIntentScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"PaymentIntent"> | string
  stripePaymentIntentId?: Prisma.StringWithAggregatesFilter<"PaymentIntent"> | string
  stripeCheckoutSessionId?: Prisma.StringNullableWithAggregatesFilter<"PaymentIntent"> | string | null
  amount?: Prisma.IntWithAggregatesFilter<"PaymentIntent"> | number
  currency?: Prisma.StringWithAggregatesFilter<"PaymentIntent"> | string
  status?: Prisma.EnumPaymentStatusWithAggregatesFilter<"PaymentIntent"> | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeWithAggregatesFilter<"PaymentIntent"> | $Enums.PaymentType
  customerEmail?: Prisma.StringWithAggregatesFilter<"PaymentIntent"> | string
  customerName?: Prisma.StringNullableWithAggregatesFilter<"PaymentIntent"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"PaymentIntent"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"PaymentIntent"> | Date | string
  processedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"PaymentIntent"> | Date | string | null
}

export type PaymentIntentCreateInput = {
  id?: string
  stripePaymentIntentId: string
  stripeCheckoutSessionId?: string | null
  amount: number
  currency?: string
  status?: $Enums.PaymentStatus
  paymentType: $Enums.PaymentType
  customerEmail: string
  customerName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  processedAt?: Date | string | null
  licenses?: Prisma.LicenseCreateNestedManyWithoutPaymentIntentInput
  deviceExpansions?: Prisma.DeviceExpansionCreateNestedManyWithoutPaymentIntentInput
  webhookEvents?: Prisma.WebhookEventCreateNestedManyWithoutPaymentIntentInput
}

export type PaymentIntentUncheckedCreateInput = {
  id?: string
  stripePaymentIntentId: string
  stripeCheckoutSessionId?: string | null
  amount: number
  currency?: string
  status?: $Enums.PaymentStatus
  paymentType: $Enums.PaymentType
  customerEmail: string
  customerName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  processedAt?: Date | string | null
  licenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutPaymentIntentInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedCreateNestedManyWithoutPaymentIntentInput
  webhookEvents?: Prisma.WebhookEventUncheckedCreateNestedManyWithoutPaymentIntentInput
}

export type PaymentIntentUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripePaymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCheckoutSessionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFieldUpdateOperationsInput | $Enums.PaymentType
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  licenses?: Prisma.LicenseUpdateManyWithoutPaymentIntentNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUpdateManyWithoutPaymentIntentNestedInput
  webhookEvents?: Prisma.WebhookEventUpdateManyWithoutPaymentIntentNestedInput
}

export type PaymentIntentUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripePaymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCheckoutSessionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFieldUpdateOperationsInput | $Enums.PaymentType
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  licenses?: Prisma.LicenseUncheckedUpdateManyWithoutPaymentIntentNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentNestedInput
  webhookEvents?: Prisma.WebhookEventUncheckedUpdateManyWithoutPaymentIntentNestedInput
}

export type PaymentIntentCreateManyInput = {
  id?: string
  stripePaymentIntentId: string
  stripeCheckoutSessionId?: string | null
  amount: number
  currency?: string
  status?: $Enums.PaymentStatus
  paymentType: $Enums.PaymentType
  customerEmail: string
  customerName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  processedAt?: Date | string | null
}

export type PaymentIntentUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripePaymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCheckoutSessionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFieldUpdateOperationsInput | $Enums.PaymentType
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type PaymentIntentUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripePaymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCheckoutSessionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFieldUpdateOperationsInput | $Enums.PaymentType
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type PaymentIntentCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stripePaymentIntentId?: Prisma.SortOrder
  stripeCheckoutSessionId?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  status?: Prisma.SortOrder
  paymentType?: Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  processedAt?: Prisma.SortOrder
}

export type PaymentIntentAvgOrderByAggregateInput = {
  amount?: Prisma.SortOrder
}

export type PaymentIntentMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stripePaymentIntentId?: Prisma.SortOrder
  stripeCheckoutSessionId?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  status?: Prisma.SortOrder
  paymentType?: Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  processedAt?: Prisma.SortOrder
}

export type PaymentIntentMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stripePaymentIntentId?: Prisma.SortOrder
  stripeCheckoutSessionId?: Prisma.SortOrder
  amount?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  status?: Prisma.SortOrder
  paymentType?: Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  processedAt?: Prisma.SortOrder
}

export type PaymentIntentSumOrderByAggregateInput = {
  amount?: Prisma.SortOrder
}

export type PaymentIntentNullableScalarRelationFilter = {
  is?: Prisma.PaymentIntentWhereInput | null
  isNot?: Prisma.PaymentIntentWhereInput | null
}

export type PaymentIntentScalarRelationFilter = {
  is?: Prisma.PaymentIntentWhereInput
  isNot?: Prisma.PaymentIntentWhereInput
}

export type IntFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type EnumPaymentStatusFieldUpdateOperationsInput = {
  set?: $Enums.PaymentStatus
}

export type EnumPaymentTypeFieldUpdateOperationsInput = {
  set?: $Enums.PaymentType
}

export type PaymentIntentCreateNestedOneWithoutWebhookEventsInput = {
  create?: Prisma.XOR<Prisma.PaymentIntentCreateWithoutWebhookEventsInput, Prisma.PaymentIntentUncheckedCreateWithoutWebhookEventsInput>
  connectOrCreate?: Prisma.PaymentIntentCreateOrConnectWithoutWebhookEventsInput
  connect?: Prisma.PaymentIntentWhereUniqueInput
}

export type PaymentIntentUpdateOneWithoutWebhookEventsNestedInput = {
  create?: Prisma.XOR<Prisma.PaymentIntentCreateWithoutWebhookEventsInput, Prisma.PaymentIntentUncheckedCreateWithoutWebhookEventsInput>
  connectOrCreate?: Prisma.PaymentIntentCreateOrConnectWithoutWebhookEventsInput
  upsert?: Prisma.PaymentIntentUpsertWithoutWebhookEventsInput
  disconnect?: Prisma.PaymentIntentWhereInput | boolean
  delete?: Prisma.PaymentIntentWhereInput | boolean
  connect?: Prisma.PaymentIntentWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.PaymentIntentUpdateToOneWithWhereWithoutWebhookEventsInput, Prisma.PaymentIntentUpdateWithoutWebhookEventsInput>, Prisma.PaymentIntentUncheckedUpdateWithoutWebhookEventsInput>
}

export type PaymentIntentCreateNestedOneWithoutLicensesInput = {
  create?: Prisma.XOR<Prisma.PaymentIntentCreateWithoutLicensesInput, Prisma.PaymentIntentUncheckedCreateWithoutLicensesInput>
  connectOrCreate?: Prisma.PaymentIntentCreateOrConnectWithoutLicensesInput
  connect?: Prisma.PaymentIntentWhereUniqueInput
}

export type PaymentIntentUpdateOneWithoutLicensesNestedInput = {
  create?: Prisma.XOR<Prisma.PaymentIntentCreateWithoutLicensesInput, Prisma.PaymentIntentUncheckedCreateWithoutLicensesInput>
  connectOrCreate?: Prisma.PaymentIntentCreateOrConnectWithoutLicensesInput
  upsert?: Prisma.PaymentIntentUpsertWithoutLicensesInput
  disconnect?: Prisma.PaymentIntentWhereInput | boolean
  delete?: Prisma.PaymentIntentWhereInput | boolean
  connect?: Prisma.PaymentIntentWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.PaymentIntentUpdateToOneWithWhereWithoutLicensesInput, Prisma.PaymentIntentUpdateWithoutLicensesInput>, Prisma.PaymentIntentUncheckedUpdateWithoutLicensesInput>
}

export type PaymentIntentCreateNestedOneWithoutDeviceExpansionsInput = {
  create?: Prisma.XOR<Prisma.PaymentIntentCreateWithoutDeviceExpansionsInput, Prisma.PaymentIntentUncheckedCreateWithoutDeviceExpansionsInput>
  connectOrCreate?: Prisma.PaymentIntentCreateOrConnectWithoutDeviceExpansionsInput
  connect?: Prisma.PaymentIntentWhereUniqueInput
}

export type PaymentIntentUpdateOneRequiredWithoutDeviceExpansionsNestedInput = {
  create?: Prisma.XOR<Prisma.PaymentIntentCreateWithoutDeviceExpansionsInput, Prisma.PaymentIntentUncheckedCreateWithoutDeviceExpansionsInput>
  connectOrCreate?: Prisma.PaymentIntentCreateOrConnectWithoutDeviceExpansionsInput
  upsert?: Prisma.PaymentIntentUpsertWithoutDeviceExpansionsInput
  connect?: Prisma.PaymentIntentWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.PaymentIntentUpdateToOneWithWhereWithoutDeviceExpansionsInput, Prisma.PaymentIntentUpdateWithoutDeviceExpansionsInput>, Prisma.PaymentIntentUncheckedUpdateWithoutDeviceExpansionsInput>
}

export type PaymentIntentCreateWithoutWebhookEventsInput = {
  id?: string
  stripePaymentIntentId: string
  stripeCheckoutSessionId?: string | null
  amount: number
  currency?: string
  status?: $Enums.PaymentStatus
  paymentType: $Enums.PaymentType
  customerEmail: string
  customerName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  processedAt?: Date | string | null
  licenses?: Prisma.LicenseCreateNestedManyWithoutPaymentIntentInput
  deviceExpansions?: Prisma.DeviceExpansionCreateNestedManyWithoutPaymentIntentInput
}

export type PaymentIntentUncheckedCreateWithoutWebhookEventsInput = {
  id?: string
  stripePaymentIntentId: string
  stripeCheckoutSessionId?: string | null
  amount: number
  currency?: string
  status?: $Enums.PaymentStatus
  paymentType: $Enums.PaymentType
  customerEmail: string
  customerName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  processedAt?: Date | string | null
  licenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutPaymentIntentInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedCreateNestedManyWithoutPaymentIntentInput
}

export type PaymentIntentCreateOrConnectWithoutWebhookEventsInput = {
  where: Prisma.PaymentIntentWhereUniqueInput
  create: Prisma.XOR<Prisma.PaymentIntentCreateWithoutWebhookEventsInput, Prisma.PaymentIntentUncheckedCreateWithoutWebhookEventsInput>
}

export type PaymentIntentUpsertWithoutWebhookEventsInput = {
  update: Prisma.XOR<Prisma.PaymentIntentUpdateWithoutWebhookEventsInput, Prisma.PaymentIntentUncheckedUpdateWithoutWebhookEventsInput>
  create: Prisma.XOR<Prisma.PaymentIntentCreateWithoutWebhookEventsInput, Prisma.PaymentIntentUncheckedCreateWithoutWebhookEventsInput>
  where?: Prisma.PaymentIntentWhereInput
}

export type PaymentIntentUpdateToOneWithWhereWithoutWebhookEventsInput = {
  where?: Prisma.PaymentIntentWhereInput
  data: Prisma.XOR<Prisma.PaymentIntentUpdateWithoutWebhookEventsInput, Prisma.PaymentIntentUncheckedUpdateWithoutWebhookEventsInput>
}

export type PaymentIntentUpdateWithoutWebhookEventsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripePaymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCheckoutSessionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFieldUpdateOperationsInput | $Enums.PaymentType
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  licenses?: Prisma.LicenseUpdateManyWithoutPaymentIntentNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUpdateManyWithoutPaymentIntentNestedInput
}

export type PaymentIntentUncheckedUpdateWithoutWebhookEventsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripePaymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCheckoutSessionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFieldUpdateOperationsInput | $Enums.PaymentType
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  licenses?: Prisma.LicenseUncheckedUpdateManyWithoutPaymentIntentNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentNestedInput
}

export type PaymentIntentCreateWithoutLicensesInput = {
  id?: string
  stripePaymentIntentId: string
  stripeCheckoutSessionId?: string | null
  amount: number
  currency?: string
  status?: $Enums.PaymentStatus
  paymentType: $Enums.PaymentType
  customerEmail: string
  customerName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  processedAt?: Date | string | null
  deviceExpansions?: Prisma.DeviceExpansionCreateNestedManyWithoutPaymentIntentInput
  webhookEvents?: Prisma.WebhookEventCreateNestedManyWithoutPaymentIntentInput
}

export type PaymentIntentUncheckedCreateWithoutLicensesInput = {
  id?: string
  stripePaymentIntentId: string
  stripeCheckoutSessionId?: string | null
  amount: number
  currency?: string
  status?: $Enums.PaymentStatus
  paymentType: $Enums.PaymentType
  customerEmail: string
  customerName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  processedAt?: Date | string | null
  deviceExpansions?: Prisma.DeviceExpansionUncheckedCreateNestedManyWithoutPaymentIntentInput
  webhookEvents?: Prisma.WebhookEventUncheckedCreateNestedManyWithoutPaymentIntentInput
}

export type PaymentIntentCreateOrConnectWithoutLicensesInput = {
  where: Prisma.PaymentIntentWhereUniqueInput
  create: Prisma.XOR<Prisma.PaymentIntentCreateWithoutLicensesInput, Prisma.PaymentIntentUncheckedCreateWithoutLicensesInput>
}

export type PaymentIntentUpsertWithoutLicensesInput = {
  update: Prisma.XOR<Prisma.PaymentIntentUpdateWithoutLicensesInput, Prisma.PaymentIntentUncheckedUpdateWithoutLicensesInput>
  create: Prisma.XOR<Prisma.PaymentIntentCreateWithoutLicensesInput, Prisma.PaymentIntentUncheckedCreateWithoutLicensesInput>
  where?: Prisma.PaymentIntentWhereInput
}

export type PaymentIntentUpdateToOneWithWhereWithoutLicensesInput = {
  where?: Prisma.PaymentIntentWhereInput
  data: Prisma.XOR<Prisma.PaymentIntentUpdateWithoutLicensesInput, Prisma.PaymentIntentUncheckedUpdateWithoutLicensesInput>
}

export type PaymentIntentUpdateWithoutLicensesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripePaymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCheckoutSessionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFieldUpdateOperationsInput | $Enums.PaymentType
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deviceExpansions?: Prisma.DeviceExpansionUpdateManyWithoutPaymentIntentNestedInput
  webhookEvents?: Prisma.WebhookEventUpdateManyWithoutPaymentIntentNestedInput
}

export type PaymentIntentUncheckedUpdateWithoutLicensesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripePaymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCheckoutSessionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFieldUpdateOperationsInput | $Enums.PaymentType
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  deviceExpansions?: Prisma.DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentNestedInput
  webhookEvents?: Prisma.WebhookEventUncheckedUpdateManyWithoutPaymentIntentNestedInput
}

export type PaymentIntentCreateWithoutDeviceExpansionsInput = {
  id?: string
  stripePaymentIntentId: string
  stripeCheckoutSessionId?: string | null
  amount: number
  currency?: string
  status?: $Enums.PaymentStatus
  paymentType: $Enums.PaymentType
  customerEmail: string
  customerName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  processedAt?: Date | string | null
  licenses?: Prisma.LicenseCreateNestedManyWithoutPaymentIntentInput
  webhookEvents?: Prisma.WebhookEventCreateNestedManyWithoutPaymentIntentInput
}

export type PaymentIntentUncheckedCreateWithoutDeviceExpansionsInput = {
  id?: string
  stripePaymentIntentId: string
  stripeCheckoutSessionId?: string | null
  amount: number
  currency?: string
  status?: $Enums.PaymentStatus
  paymentType: $Enums.PaymentType
  customerEmail: string
  customerName?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  processedAt?: Date | string | null
  licenses?: Prisma.LicenseUncheckedCreateNestedManyWithoutPaymentIntentInput
  webhookEvents?: Prisma.WebhookEventUncheckedCreateNestedManyWithoutPaymentIntentInput
}

export type PaymentIntentCreateOrConnectWithoutDeviceExpansionsInput = {
  where: Prisma.PaymentIntentWhereUniqueInput
  create: Prisma.XOR<Prisma.PaymentIntentCreateWithoutDeviceExpansionsInput, Prisma.PaymentIntentUncheckedCreateWithoutDeviceExpansionsInput>
}

export type PaymentIntentUpsertWithoutDeviceExpansionsInput = {
  update: Prisma.XOR<Prisma.PaymentIntentUpdateWithoutDeviceExpansionsInput, Prisma.PaymentIntentUncheckedUpdateWithoutDeviceExpansionsInput>
  create: Prisma.XOR<Prisma.PaymentIntentCreateWithoutDeviceExpansionsInput, Prisma.PaymentIntentUncheckedCreateWithoutDeviceExpansionsInput>
  where?: Prisma.PaymentIntentWhereInput
}

export type PaymentIntentUpdateToOneWithWhereWithoutDeviceExpansionsInput = {
  where?: Prisma.PaymentIntentWhereInput
  data: Prisma.XOR<Prisma.PaymentIntentUpdateWithoutDeviceExpansionsInput, Prisma.PaymentIntentUncheckedUpdateWithoutDeviceExpansionsInput>
}

export type PaymentIntentUpdateWithoutDeviceExpansionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripePaymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCheckoutSessionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFieldUpdateOperationsInput | $Enums.PaymentType
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  licenses?: Prisma.LicenseUpdateManyWithoutPaymentIntentNestedInput
  webhookEvents?: Prisma.WebhookEventUpdateManyWithoutPaymentIntentNestedInput
}

export type PaymentIntentUncheckedUpdateWithoutDeviceExpansionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripePaymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCheckoutSessionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  amount?: Prisma.IntFieldUpdateOperationsInput | number
  currency?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumPaymentStatusFieldUpdateOperationsInput | $Enums.PaymentStatus
  paymentType?: Prisma.EnumPaymentTypeFieldUpdateOperationsInput | $Enums.PaymentType
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  licenses?: Prisma.LicenseUncheckedUpdateManyWithoutPaymentIntentNestedInput
  webhookEvents?: Prisma.WebhookEventUncheckedUpdateManyWithoutPaymentIntentNestedInput
}


/**
 * Count Type PaymentIntentCountOutputType
 */

export type PaymentIntentCountOutputType = {
  licenses: number
  deviceExpansions: number
  webhookEvents: number
}

export type PaymentIntentCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  licenses?: boolean | PaymentIntentCountOutputTypeCountLicensesArgs
  deviceExpansions?: boolean | PaymentIntentCountOutputTypeCountDeviceExpansionsArgs
  webhookEvents?: boolean | PaymentIntentCountOutputTypeCountWebhookEventsArgs
}

/**
 * PaymentIntentCountOutputType without action
 */
export type PaymentIntentCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntentCountOutputType
   */
  select?: Prisma.PaymentIntentCountOutputTypeSelect<ExtArgs> | null
}

/**
 * PaymentIntentCountOutputType without action
 */
export type PaymentIntentCountOutputTypeCountLicensesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.LicenseWhereInput
}

/**
 * PaymentIntentCountOutputType without action
 */
export type PaymentIntentCountOutputTypeCountDeviceExpansionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.DeviceExpansionWhereInput
}

/**
 * PaymentIntentCountOutputType without action
 */
export type PaymentIntentCountOutputTypeCountWebhookEventsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.WebhookEventWhereInput
}


export type PaymentIntentSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  stripePaymentIntentId?: boolean
  stripeCheckoutSessionId?: boolean
  amount?: boolean
  currency?: boolean
  status?: boolean
  paymentType?: boolean
  customerEmail?: boolean
  customerName?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  processedAt?: boolean
  licenses?: boolean | Prisma.PaymentIntent$licensesArgs<ExtArgs>
  deviceExpansions?: boolean | Prisma.PaymentIntent$deviceExpansionsArgs<ExtArgs>
  webhookEvents?: boolean | Prisma.PaymentIntent$webhookEventsArgs<ExtArgs>
  _count?: boolean | Prisma.PaymentIntentCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["paymentIntent"]>

export type PaymentIntentSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  stripePaymentIntentId?: boolean
  stripeCheckoutSessionId?: boolean
  amount?: boolean
  currency?: boolean
  status?: boolean
  paymentType?: boolean
  customerEmail?: boolean
  customerName?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  processedAt?: boolean
}, ExtArgs["result"]["paymentIntent"]>

export type PaymentIntentSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  stripePaymentIntentId?: boolean
  stripeCheckoutSessionId?: boolean
  amount?: boolean
  currency?: boolean
  status?: boolean
  paymentType?: boolean
  customerEmail?: boolean
  customerName?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  processedAt?: boolean
}, ExtArgs["result"]["paymentIntent"]>

export type PaymentIntentSelectScalar = {
  id?: boolean
  stripePaymentIntentId?: boolean
  stripeCheckoutSessionId?: boolean
  amount?: boolean
  currency?: boolean
  status?: boolean
  paymentType?: boolean
  customerEmail?: boolean
  customerName?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  processedAt?: boolean
}

export type PaymentIntentOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "stripePaymentIntentId" | "stripeCheckoutSessionId" | "amount" | "currency" | "status" | "paymentType" | "customerEmail" | "customerName" | "createdAt" | "updatedAt" | "processedAt", ExtArgs["result"]["paymentIntent"]>
export type PaymentIntentInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  licenses?: boolean | Prisma.PaymentIntent$licensesArgs<ExtArgs>
  deviceExpansions?: boolean | Prisma.PaymentIntent$deviceExpansionsArgs<ExtArgs>
  webhookEvents?: boolean | Prisma.PaymentIntent$webhookEventsArgs<ExtArgs>
  _count?: boolean | Prisma.PaymentIntentCountOutputTypeDefaultArgs<ExtArgs>
}
export type PaymentIntentIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}
export type PaymentIntentIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}

export type $PaymentIntentPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "PaymentIntent"
  objects: {
    licenses: Prisma.$LicensePayload<ExtArgs>[]
    deviceExpansions: Prisma.$DeviceExpansionPayload<ExtArgs>[]
    webhookEvents: Prisma.$WebhookEventPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    stripePaymentIntentId: string
    stripeCheckoutSessionId: string | null
    amount: number
    currency: string
    status: $Enums.PaymentStatus
    paymentType: $Enums.PaymentType
    customerEmail: string
    customerName: string | null
    createdAt: Date
    updatedAt: Date
    processedAt: Date | null
  }, ExtArgs["result"]["paymentIntent"]>
  composites: {}
}

export type PaymentIntentGetPayload<S extends boolean | null | undefined | PaymentIntentDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload, S>

export type PaymentIntentCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<PaymentIntentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: PaymentIntentCountAggregateInputType | true
  }

export interface PaymentIntentDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['PaymentIntent'], meta: { name: 'PaymentIntent' } }
  /**
   * Find zero or one PaymentIntent that matches the filter.
   * @param {PaymentIntentFindUniqueArgs} args - Arguments to find a PaymentIntent
   * @example
   * // Get one PaymentIntent
   * const paymentIntent = await prisma.paymentIntent.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends PaymentIntentFindUniqueArgs>(args: Prisma.SelectSubset<T, PaymentIntentFindUniqueArgs<ExtArgs>>): Prisma.Prisma__PaymentIntentClient<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one PaymentIntent that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {PaymentIntentFindUniqueOrThrowArgs} args - Arguments to find a PaymentIntent
   * @example
   * // Get one PaymentIntent
   * const paymentIntent = await prisma.paymentIntent.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends PaymentIntentFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, PaymentIntentFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__PaymentIntentClient<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first PaymentIntent that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PaymentIntentFindFirstArgs} args - Arguments to find a PaymentIntent
   * @example
   * // Get one PaymentIntent
   * const paymentIntent = await prisma.paymentIntent.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends PaymentIntentFindFirstArgs>(args?: Prisma.SelectSubset<T, PaymentIntentFindFirstArgs<ExtArgs>>): Prisma.Prisma__PaymentIntentClient<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first PaymentIntent that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PaymentIntentFindFirstOrThrowArgs} args - Arguments to find a PaymentIntent
   * @example
   * // Get one PaymentIntent
   * const paymentIntent = await prisma.paymentIntent.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends PaymentIntentFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, PaymentIntentFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__PaymentIntentClient<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more PaymentIntents that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PaymentIntentFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all PaymentIntents
   * const paymentIntents = await prisma.paymentIntent.findMany()
   * 
   * // Get first 10 PaymentIntents
   * const paymentIntents = await prisma.paymentIntent.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const paymentIntentWithIdOnly = await prisma.paymentIntent.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends PaymentIntentFindManyArgs>(args?: Prisma.SelectSubset<T, PaymentIntentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a PaymentIntent.
   * @param {PaymentIntentCreateArgs} args - Arguments to create a PaymentIntent.
   * @example
   * // Create one PaymentIntent
   * const PaymentIntent = await prisma.paymentIntent.create({
   *   data: {
   *     // ... data to create a PaymentIntent
   *   }
   * })
   * 
   */
  create<T extends PaymentIntentCreateArgs>(args: Prisma.SelectSubset<T, PaymentIntentCreateArgs<ExtArgs>>): Prisma.Prisma__PaymentIntentClient<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many PaymentIntents.
   * @param {PaymentIntentCreateManyArgs} args - Arguments to create many PaymentIntents.
   * @example
   * // Create many PaymentIntents
   * const paymentIntent = await prisma.paymentIntent.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends PaymentIntentCreateManyArgs>(args?: Prisma.SelectSubset<T, PaymentIntentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many PaymentIntents and returns the data saved in the database.
   * @param {PaymentIntentCreateManyAndReturnArgs} args - Arguments to create many PaymentIntents.
   * @example
   * // Create many PaymentIntents
   * const paymentIntent = await prisma.paymentIntent.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many PaymentIntents and only return the `id`
   * const paymentIntentWithIdOnly = await prisma.paymentIntent.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends PaymentIntentCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, PaymentIntentCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a PaymentIntent.
   * @param {PaymentIntentDeleteArgs} args - Arguments to delete one PaymentIntent.
   * @example
   * // Delete one PaymentIntent
   * const PaymentIntent = await prisma.paymentIntent.delete({
   *   where: {
   *     // ... filter to delete one PaymentIntent
   *   }
   * })
   * 
   */
  delete<T extends PaymentIntentDeleteArgs>(args: Prisma.SelectSubset<T, PaymentIntentDeleteArgs<ExtArgs>>): Prisma.Prisma__PaymentIntentClient<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one PaymentIntent.
   * @param {PaymentIntentUpdateArgs} args - Arguments to update one PaymentIntent.
   * @example
   * // Update one PaymentIntent
   * const paymentIntent = await prisma.paymentIntent.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends PaymentIntentUpdateArgs>(args: Prisma.SelectSubset<T, PaymentIntentUpdateArgs<ExtArgs>>): Prisma.Prisma__PaymentIntentClient<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more PaymentIntents.
   * @param {PaymentIntentDeleteManyArgs} args - Arguments to filter PaymentIntents to delete.
   * @example
   * // Delete a few PaymentIntents
   * const { count } = await prisma.paymentIntent.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends PaymentIntentDeleteManyArgs>(args?: Prisma.SelectSubset<T, PaymentIntentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more PaymentIntents.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PaymentIntentUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many PaymentIntents
   * const paymentIntent = await prisma.paymentIntent.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends PaymentIntentUpdateManyArgs>(args: Prisma.SelectSubset<T, PaymentIntentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more PaymentIntents and returns the data updated in the database.
   * @param {PaymentIntentUpdateManyAndReturnArgs} args - Arguments to update many PaymentIntents.
   * @example
   * // Update many PaymentIntents
   * const paymentIntent = await prisma.paymentIntent.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more PaymentIntents and only return the `id`
   * const paymentIntentWithIdOnly = await prisma.paymentIntent.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends PaymentIntentUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, PaymentIntentUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one PaymentIntent.
   * @param {PaymentIntentUpsertArgs} args - Arguments to update or create a PaymentIntent.
   * @example
   * // Update or create a PaymentIntent
   * const paymentIntent = await prisma.paymentIntent.upsert({
   *   create: {
   *     // ... data to create a PaymentIntent
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the PaymentIntent we want to update
   *   }
   * })
   */
  upsert<T extends PaymentIntentUpsertArgs>(args: Prisma.SelectSubset<T, PaymentIntentUpsertArgs<ExtArgs>>): Prisma.Prisma__PaymentIntentClient<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of PaymentIntents.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PaymentIntentCountArgs} args - Arguments to filter PaymentIntents to count.
   * @example
   * // Count the number of PaymentIntents
   * const count = await prisma.paymentIntent.count({
   *   where: {
   *     // ... the filter for the PaymentIntents we want to count
   *   }
   * })
  **/
  count<T extends PaymentIntentCountArgs>(
    args?: Prisma.Subset<T, PaymentIntentCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], PaymentIntentCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a PaymentIntent.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PaymentIntentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends PaymentIntentAggregateArgs>(args: Prisma.Subset<T, PaymentIntentAggregateArgs>): Prisma.PrismaPromise<GetPaymentIntentAggregateType<T>>

  /**
   * Group by PaymentIntent.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PaymentIntentGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends PaymentIntentGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: PaymentIntentGroupByArgs['orderBy'] }
      : { orderBy?: PaymentIntentGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, PaymentIntentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPaymentIntentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the PaymentIntent model
 */
readonly fields: PaymentIntentFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for PaymentIntent.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__PaymentIntentClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  licenses<T extends Prisma.PaymentIntent$licensesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.PaymentIntent$licensesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  deviceExpansions<T extends Prisma.PaymentIntent$deviceExpansionsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.PaymentIntent$deviceExpansionsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  webhookEvents<T extends Prisma.PaymentIntent$webhookEventsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.PaymentIntent$webhookEventsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the PaymentIntent model
 */
export interface PaymentIntentFieldRefs {
  readonly id: Prisma.FieldRef<"PaymentIntent", 'String'>
  readonly stripePaymentIntentId: Prisma.FieldRef<"PaymentIntent", 'String'>
  readonly stripeCheckoutSessionId: Prisma.FieldRef<"PaymentIntent", 'String'>
  readonly amount: Prisma.FieldRef<"PaymentIntent", 'Int'>
  readonly currency: Prisma.FieldRef<"PaymentIntent", 'String'>
  readonly status: Prisma.FieldRef<"PaymentIntent", 'PaymentStatus'>
  readonly paymentType: Prisma.FieldRef<"PaymentIntent", 'PaymentType'>
  readonly customerEmail: Prisma.FieldRef<"PaymentIntent", 'String'>
  readonly customerName: Prisma.FieldRef<"PaymentIntent", 'String'>
  readonly createdAt: Prisma.FieldRef<"PaymentIntent", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"PaymentIntent", 'DateTime'>
  readonly processedAt: Prisma.FieldRef<"PaymentIntent", 'DateTime'>
}
    

// Custom InputTypes
/**
 * PaymentIntent findUnique
 */
export type PaymentIntentFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
  /**
   * Filter, which PaymentIntent to fetch.
   */
  where: Prisma.PaymentIntentWhereUniqueInput
}

/**
 * PaymentIntent findUniqueOrThrow
 */
export type PaymentIntentFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
  /**
   * Filter, which PaymentIntent to fetch.
   */
  where: Prisma.PaymentIntentWhereUniqueInput
}

/**
 * PaymentIntent findFirst
 */
export type PaymentIntentFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
  /**
   * Filter, which PaymentIntent to fetch.
   */
  where?: Prisma.PaymentIntentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PaymentIntents to fetch.
   */
  orderBy?: Prisma.PaymentIntentOrderByWithRelationInput | Prisma.PaymentIntentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for PaymentIntents.
   */
  cursor?: Prisma.PaymentIntentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PaymentIntents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PaymentIntents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of PaymentIntents.
   */
  distinct?: Prisma.PaymentIntentScalarFieldEnum | Prisma.PaymentIntentScalarFieldEnum[]
}

/**
 * PaymentIntent findFirstOrThrow
 */
export type PaymentIntentFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
  /**
   * Filter, which PaymentIntent to fetch.
   */
  where?: Prisma.PaymentIntentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PaymentIntents to fetch.
   */
  orderBy?: Prisma.PaymentIntentOrderByWithRelationInput | Prisma.PaymentIntentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for PaymentIntents.
   */
  cursor?: Prisma.PaymentIntentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PaymentIntents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PaymentIntents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of PaymentIntents.
   */
  distinct?: Prisma.PaymentIntentScalarFieldEnum | Prisma.PaymentIntentScalarFieldEnum[]
}

/**
 * PaymentIntent findMany
 */
export type PaymentIntentFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
  /**
   * Filter, which PaymentIntents to fetch.
   */
  where?: Prisma.PaymentIntentWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of PaymentIntents to fetch.
   */
  orderBy?: Prisma.PaymentIntentOrderByWithRelationInput | Prisma.PaymentIntentOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing PaymentIntents.
   */
  cursor?: Prisma.PaymentIntentWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` PaymentIntents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` PaymentIntents.
   */
  skip?: number
  distinct?: Prisma.PaymentIntentScalarFieldEnum | Prisma.PaymentIntentScalarFieldEnum[]
}

/**
 * PaymentIntent create
 */
export type PaymentIntentCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
  /**
   * The data needed to create a PaymentIntent.
   */
  data: Prisma.XOR<Prisma.PaymentIntentCreateInput, Prisma.PaymentIntentUncheckedCreateInput>
}

/**
 * PaymentIntent createMany
 */
export type PaymentIntentCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many PaymentIntents.
   */
  data: Prisma.PaymentIntentCreateManyInput | Prisma.PaymentIntentCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * PaymentIntent createManyAndReturn
 */
export type PaymentIntentCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * The data used to create many PaymentIntents.
   */
  data: Prisma.PaymentIntentCreateManyInput | Prisma.PaymentIntentCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * PaymentIntent update
 */
export type PaymentIntentUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
  /**
   * The data needed to update a PaymentIntent.
   */
  data: Prisma.XOR<Prisma.PaymentIntentUpdateInput, Prisma.PaymentIntentUncheckedUpdateInput>
  /**
   * Choose, which PaymentIntent to update.
   */
  where: Prisma.PaymentIntentWhereUniqueInput
}

/**
 * PaymentIntent updateMany
 */
export type PaymentIntentUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update PaymentIntents.
   */
  data: Prisma.XOR<Prisma.PaymentIntentUpdateManyMutationInput, Prisma.PaymentIntentUncheckedUpdateManyInput>
  /**
   * Filter which PaymentIntents to update
   */
  where?: Prisma.PaymentIntentWhereInput
  /**
   * Limit how many PaymentIntents to update.
   */
  limit?: number
}

/**
 * PaymentIntent updateManyAndReturn
 */
export type PaymentIntentUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * The data used to update PaymentIntents.
   */
  data: Prisma.XOR<Prisma.PaymentIntentUpdateManyMutationInput, Prisma.PaymentIntentUncheckedUpdateManyInput>
  /**
   * Filter which PaymentIntents to update
   */
  where?: Prisma.PaymentIntentWhereInput
  /**
   * Limit how many PaymentIntents to update.
   */
  limit?: number
}

/**
 * PaymentIntent upsert
 */
export type PaymentIntentUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
  /**
   * The filter to search for the PaymentIntent to update in case it exists.
   */
  where: Prisma.PaymentIntentWhereUniqueInput
  /**
   * In case the PaymentIntent found by the `where` argument doesn't exist, create a new PaymentIntent with this data.
   */
  create: Prisma.XOR<Prisma.PaymentIntentCreateInput, Prisma.PaymentIntentUncheckedCreateInput>
  /**
   * In case the PaymentIntent was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.PaymentIntentUpdateInput, Prisma.PaymentIntentUncheckedUpdateInput>
}

/**
 * PaymentIntent delete
 */
export type PaymentIntentDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
  /**
   * Filter which PaymentIntent to delete.
   */
  where: Prisma.PaymentIntentWhereUniqueInput
}

/**
 * PaymentIntent deleteMany
 */
export type PaymentIntentDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which PaymentIntents to delete
   */
  where?: Prisma.PaymentIntentWhereInput
  /**
   * Limit how many PaymentIntents to delete.
   */
  limit?: number
}

/**
 * PaymentIntent.licenses
 */
export type PaymentIntent$licensesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
  where?: Prisma.LicenseWhereInput
  orderBy?: Prisma.LicenseOrderByWithRelationInput | Prisma.LicenseOrderByWithRelationInput[]
  cursor?: Prisma.LicenseWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.LicenseScalarFieldEnum | Prisma.LicenseScalarFieldEnum[]
}

/**
 * PaymentIntent.deviceExpansions
 */
export type PaymentIntent$deviceExpansionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DeviceExpansion
   */
  select?: Prisma.DeviceExpansionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DeviceExpansion
   */
  omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceExpansionInclude<ExtArgs> | null
  where?: Prisma.DeviceExpansionWhereInput
  orderBy?: Prisma.DeviceExpansionOrderByWithRelationInput | Prisma.DeviceExpansionOrderByWithRelationInput[]
  cursor?: Prisma.DeviceExpansionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.DeviceExpansionScalarFieldEnum | Prisma.DeviceExpansionScalarFieldEnum[]
}

/**
 * PaymentIntent.webhookEvents
 */
export type PaymentIntent$webhookEventsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventInclude<ExtArgs> | null
  where?: Prisma.WebhookEventWhereInput
  orderBy?: Prisma.WebhookEventOrderByWithRelationInput | Prisma.WebhookEventOrderByWithRelationInput[]
  cursor?: Prisma.WebhookEventWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.WebhookEventScalarFieldEnum | Prisma.WebhookEventScalarFieldEnum[]
}

/**
 * PaymentIntent without action
 */
export type PaymentIntentDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
}
