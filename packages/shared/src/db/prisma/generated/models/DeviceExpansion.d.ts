/**
 * This file exports the `DeviceExpansion` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library";
import type * as $Enums from "../enums";
import type * as Prisma from "../internal/prismaNamespace";
/**
 * Model DeviceExpansion
 *
 */
export type DeviceExpansionModel = runtime.Types.Result.DefaultSelection<Prisma.$DeviceExpansionPayload>;
export type AggregateDeviceExpansion = {
    _count: DeviceExpansionCountAggregateOutputType | null;
    _avg: DeviceExpansionAvgAggregateOutputType | null;
    _sum: DeviceExpansionSumAggregateOutputType | null;
    _min: DeviceExpansionMinAggregateOutputType | null;
    _max: DeviceExpansionMaxAggregateOutputType | null;
};
export type DeviceExpansionAvgAggregateOutputType = {
    additionalDevices: number | null;
    amount: number | null;
};
export type DeviceExpansionSumAggregateOutputType = {
    additionalDevices: number | null;
    amount: number | null;
};
export type DeviceExpansionMinAggregateOutputType = {
    id: string | null;
    licenseId: string | null;
    paymentIntentId: string | null;
    additionalDevices: number | null;
    amount: number | null;
    status: $Enums.DeviceExpansionStatus | null;
    createdAt: Date | null;
    processedAt: Date | null;
};
export type DeviceExpansionMaxAggregateOutputType = {
    id: string | null;
    licenseId: string | null;
    paymentIntentId: string | null;
    additionalDevices: number | null;
    amount: number | null;
    status: $Enums.DeviceExpansionStatus | null;
    createdAt: Date | null;
    processedAt: Date | null;
};
export type DeviceExpansionCountAggregateOutputType = {
    id: number;
    licenseId: number;
    paymentIntentId: number;
    additionalDevices: number;
    amount: number;
    status: number;
    createdAt: number;
    processedAt: number;
    _all: number;
};
export type DeviceExpansionAvgAggregateInputType = {
    additionalDevices?: true;
    amount?: true;
};
export type DeviceExpansionSumAggregateInputType = {
    additionalDevices?: true;
    amount?: true;
};
export type DeviceExpansionMinAggregateInputType = {
    id?: true;
    licenseId?: true;
    paymentIntentId?: true;
    additionalDevices?: true;
    amount?: true;
    status?: true;
    createdAt?: true;
    processedAt?: true;
};
export type DeviceExpansionMaxAggregateInputType = {
    id?: true;
    licenseId?: true;
    paymentIntentId?: true;
    additionalDevices?: true;
    amount?: true;
    status?: true;
    createdAt?: true;
    processedAt?: true;
};
export type DeviceExpansionCountAggregateInputType = {
    id?: true;
    licenseId?: true;
    paymentIntentId?: true;
    additionalDevices?: true;
    amount?: true;
    status?: true;
    createdAt?: true;
    processedAt?: true;
    _all?: true;
};
export type DeviceExpansionAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Filter which DeviceExpansion to aggregate.
     */
    where?: Prisma.DeviceExpansionWhereInput;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     *
     * Determine the order of DeviceExpansions to fetch.
     */
    orderBy?: Prisma.DeviceExpansionOrderByWithRelationInput | Prisma.DeviceExpansionOrderByWithRelationInput[];
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     *
     * Sets the start position
     */
    cursor?: Prisma.DeviceExpansionWhereUniqueInput;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     *
     * Take `±n` DeviceExpansions from the position of the cursor.
     */
    take?: number;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     *
     * Skip the first `n` DeviceExpansions.
     */
    skip?: number;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     *
     * Count returned DeviceExpansions
    **/
    _count?: true | DeviceExpansionCountAggregateInputType;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     *
     * Select which fields to average
    **/
    _avg?: DeviceExpansionAvgAggregateInputType;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     *
     * Select which fields to sum
    **/
    _sum?: DeviceExpansionSumAggregateInputType;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     *
     * Select which fields to find the minimum value
    **/
    _min?: DeviceExpansionMinAggregateInputType;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     *
     * Select which fields to find the maximum value
    **/
    _max?: DeviceExpansionMaxAggregateInputType;
};
export type GetDeviceExpansionAggregateType<T extends DeviceExpansionAggregateArgs> = {
    [P in keyof T & keyof AggregateDeviceExpansion]: P extends '_count' | 'count' ? T[P] extends true ? number : Prisma.GetScalarType<T[P], AggregateDeviceExpansion[P]> : Prisma.GetScalarType<T[P], AggregateDeviceExpansion[P]>;
};
export type DeviceExpansionGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    where?: Prisma.DeviceExpansionWhereInput;
    orderBy?: Prisma.DeviceExpansionOrderByWithAggregationInput | Prisma.DeviceExpansionOrderByWithAggregationInput[];
    by: Prisma.DeviceExpansionScalarFieldEnum[] | Prisma.DeviceExpansionScalarFieldEnum;
    having?: Prisma.DeviceExpansionScalarWhereWithAggregatesInput;
    take?: number;
    skip?: number;
    _count?: DeviceExpansionCountAggregateInputType | true;
    _avg?: DeviceExpansionAvgAggregateInputType;
    _sum?: DeviceExpansionSumAggregateInputType;
    _min?: DeviceExpansionMinAggregateInputType;
    _max?: DeviceExpansionMaxAggregateInputType;
};
export type DeviceExpansionGroupByOutputType = {
    id: string;
    licenseId: string;
    paymentIntentId: string;
    additionalDevices: number;
    amount: number;
    status: $Enums.DeviceExpansionStatus;
    createdAt: Date;
    processedAt: Date | null;
    _count: DeviceExpansionCountAggregateOutputType | null;
    _avg: DeviceExpansionAvgAggregateOutputType | null;
    _sum: DeviceExpansionSumAggregateOutputType | null;
    _min: DeviceExpansionMinAggregateOutputType | null;
    _max: DeviceExpansionMaxAggregateOutputType | null;
};
type GetDeviceExpansionGroupByPayload<T extends DeviceExpansionGroupByArgs> = Prisma.PrismaPromise<Array<Prisma.PickEnumerable<DeviceExpansionGroupByOutputType, T['by']> & {
    [P in ((keyof T) & (keyof DeviceExpansionGroupByOutputType))]: P extends '_count' ? T[P] extends boolean ? number : Prisma.GetScalarType<T[P], DeviceExpansionGroupByOutputType[P]> : Prisma.GetScalarType<T[P], DeviceExpansionGroupByOutputType[P]>;
}>>;
export type DeviceExpansionWhereInput = {
    AND?: Prisma.DeviceExpansionWhereInput | Prisma.DeviceExpansionWhereInput[];
    OR?: Prisma.DeviceExpansionWhereInput[];
    NOT?: Prisma.DeviceExpansionWhereInput | Prisma.DeviceExpansionWhereInput[];
    id?: Prisma.StringFilter<"DeviceExpansion"> | string;
    licenseId?: Prisma.StringFilter<"DeviceExpansion"> | string;
    paymentIntentId?: Prisma.StringFilter<"DeviceExpansion"> | string;
    additionalDevices?: Prisma.IntFilter<"DeviceExpansion"> | number;
    amount?: Prisma.IntFilter<"DeviceExpansion"> | number;
    status?: Prisma.EnumDeviceExpansionStatusFilter<"DeviceExpansion"> | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFilter<"DeviceExpansion"> | Date | string;
    processedAt?: Prisma.DateTimeNullableFilter<"DeviceExpansion"> | Date | string | null;
    license?: Prisma.XOR<Prisma.LicenseScalarRelationFilter, Prisma.LicenseWhereInput>;
    paymentIntent?: Prisma.XOR<Prisma.PaymentIntentScalarRelationFilter, Prisma.PaymentIntentWhereInput>;
};
export type DeviceExpansionOrderByWithRelationInput = {
    id?: Prisma.SortOrder;
    licenseId?: Prisma.SortOrder;
    paymentIntentId?: Prisma.SortOrder;
    additionalDevices?: Prisma.SortOrder;
    amount?: Prisma.SortOrder;
    status?: Prisma.SortOrder;
    createdAt?: Prisma.SortOrder;
    processedAt?: Prisma.SortOrderInput | Prisma.SortOrder;
    license?: Prisma.LicenseOrderByWithRelationInput;
    paymentIntent?: Prisma.PaymentIntentOrderByWithRelationInput;
};
export type DeviceExpansionWhereUniqueInput = Prisma.AtLeast<{
    id?: string;
    AND?: Prisma.DeviceExpansionWhereInput | Prisma.DeviceExpansionWhereInput[];
    OR?: Prisma.DeviceExpansionWhereInput[];
    NOT?: Prisma.DeviceExpansionWhereInput | Prisma.DeviceExpansionWhereInput[];
    licenseId?: Prisma.StringFilter<"DeviceExpansion"> | string;
    paymentIntentId?: Prisma.StringFilter<"DeviceExpansion"> | string;
    additionalDevices?: Prisma.IntFilter<"DeviceExpansion"> | number;
    amount?: Prisma.IntFilter<"DeviceExpansion"> | number;
    status?: Prisma.EnumDeviceExpansionStatusFilter<"DeviceExpansion"> | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFilter<"DeviceExpansion"> | Date | string;
    processedAt?: Prisma.DateTimeNullableFilter<"DeviceExpansion"> | Date | string | null;
    license?: Prisma.XOR<Prisma.LicenseScalarRelationFilter, Prisma.LicenseWhereInput>;
    paymentIntent?: Prisma.XOR<Prisma.PaymentIntentScalarRelationFilter, Prisma.PaymentIntentWhereInput>;
}, "id">;
export type DeviceExpansionOrderByWithAggregationInput = {
    id?: Prisma.SortOrder;
    licenseId?: Prisma.SortOrder;
    paymentIntentId?: Prisma.SortOrder;
    additionalDevices?: Prisma.SortOrder;
    amount?: Prisma.SortOrder;
    status?: Prisma.SortOrder;
    createdAt?: Prisma.SortOrder;
    processedAt?: Prisma.SortOrderInput | Prisma.SortOrder;
    _count?: Prisma.DeviceExpansionCountOrderByAggregateInput;
    _avg?: Prisma.DeviceExpansionAvgOrderByAggregateInput;
    _max?: Prisma.DeviceExpansionMaxOrderByAggregateInput;
    _min?: Prisma.DeviceExpansionMinOrderByAggregateInput;
    _sum?: Prisma.DeviceExpansionSumOrderByAggregateInput;
};
export type DeviceExpansionScalarWhereWithAggregatesInput = {
    AND?: Prisma.DeviceExpansionScalarWhereWithAggregatesInput | Prisma.DeviceExpansionScalarWhereWithAggregatesInput[];
    OR?: Prisma.DeviceExpansionScalarWhereWithAggregatesInput[];
    NOT?: Prisma.DeviceExpansionScalarWhereWithAggregatesInput | Prisma.DeviceExpansionScalarWhereWithAggregatesInput[];
    id?: Prisma.StringWithAggregatesFilter<"DeviceExpansion"> | string;
    licenseId?: Prisma.StringWithAggregatesFilter<"DeviceExpansion"> | string;
    paymentIntentId?: Prisma.StringWithAggregatesFilter<"DeviceExpansion"> | string;
    additionalDevices?: Prisma.IntWithAggregatesFilter<"DeviceExpansion"> | number;
    amount?: Prisma.IntWithAggregatesFilter<"DeviceExpansion"> | number;
    status?: Prisma.EnumDeviceExpansionStatusWithAggregatesFilter<"DeviceExpansion"> | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeWithAggregatesFilter<"DeviceExpansion"> | Date | string;
    processedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"DeviceExpansion"> | Date | string | null;
};
export type DeviceExpansionCreateInput = {
    id?: string;
    additionalDevices: number;
    amount: number;
    status?: $Enums.DeviceExpansionStatus;
    createdAt?: Date | string;
    processedAt?: Date | string | null;
    license: Prisma.LicenseCreateNestedOneWithoutDeviceExpansionsInput;
    paymentIntent: Prisma.PaymentIntentCreateNestedOneWithoutDeviceExpansionsInput;
};
export type DeviceExpansionUncheckedCreateInput = {
    id?: string;
    licenseId: string;
    paymentIntentId: string;
    additionalDevices: number;
    amount: number;
    status?: $Enums.DeviceExpansionStatus;
    createdAt?: Date | string;
    processedAt?: Date | string | null;
};
export type DeviceExpansionUpdateInput = {
    id?: Prisma.StringFieldUpdateOperationsInput | string;
    additionalDevices?: Prisma.IntFieldUpdateOperationsInput | number;
    amount?: Prisma.IntFieldUpdateOperationsInput | number;
    status?: Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
    processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null;
    license?: Prisma.LicenseUpdateOneRequiredWithoutDeviceExpansionsNestedInput;
    paymentIntent?: Prisma.PaymentIntentUpdateOneRequiredWithoutDeviceExpansionsNestedInput;
};
export type DeviceExpansionUncheckedUpdateInput = {
    id?: Prisma.StringFieldUpdateOperationsInput | string;
    licenseId?: Prisma.StringFieldUpdateOperationsInput | string;
    paymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string;
    additionalDevices?: Prisma.IntFieldUpdateOperationsInput | number;
    amount?: Prisma.IntFieldUpdateOperationsInput | number;
    status?: Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
    processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null;
};
export type DeviceExpansionCreateManyInput = {
    id?: string;
    licenseId: string;
    paymentIntentId: string;
    additionalDevices: number;
    amount: number;
    status?: $Enums.DeviceExpansionStatus;
    createdAt?: Date | string;
    processedAt?: Date | string | null;
};
export type DeviceExpansionUpdateManyMutationInput = {
    id?: Prisma.StringFieldUpdateOperationsInput | string;
    additionalDevices?: Prisma.IntFieldUpdateOperationsInput | number;
    amount?: Prisma.IntFieldUpdateOperationsInput | number;
    status?: Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
    processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null;
};
export type DeviceExpansionUncheckedUpdateManyInput = {
    id?: Prisma.StringFieldUpdateOperationsInput | string;
    licenseId?: Prisma.StringFieldUpdateOperationsInput | string;
    paymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string;
    additionalDevices?: Prisma.IntFieldUpdateOperationsInput | number;
    amount?: Prisma.IntFieldUpdateOperationsInput | number;
    status?: Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
    processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null;
};
export type DeviceExpansionListRelationFilter = {
    every?: Prisma.DeviceExpansionWhereInput;
    some?: Prisma.DeviceExpansionWhereInput;
    none?: Prisma.DeviceExpansionWhereInput;
};
export type DeviceExpansionOrderByRelationAggregateInput = {
    _count?: Prisma.SortOrder;
};
export type DeviceExpansionCountOrderByAggregateInput = {
    id?: Prisma.SortOrder;
    licenseId?: Prisma.SortOrder;
    paymentIntentId?: Prisma.SortOrder;
    additionalDevices?: Prisma.SortOrder;
    amount?: Prisma.SortOrder;
    status?: Prisma.SortOrder;
    createdAt?: Prisma.SortOrder;
    processedAt?: Prisma.SortOrder;
};
export type DeviceExpansionAvgOrderByAggregateInput = {
    additionalDevices?: Prisma.SortOrder;
    amount?: Prisma.SortOrder;
};
export type DeviceExpansionMaxOrderByAggregateInput = {
    id?: Prisma.SortOrder;
    licenseId?: Prisma.SortOrder;
    paymentIntentId?: Prisma.SortOrder;
    additionalDevices?: Prisma.SortOrder;
    amount?: Prisma.SortOrder;
    status?: Prisma.SortOrder;
    createdAt?: Prisma.SortOrder;
    processedAt?: Prisma.SortOrder;
};
export type DeviceExpansionMinOrderByAggregateInput = {
    id?: Prisma.SortOrder;
    licenseId?: Prisma.SortOrder;
    paymentIntentId?: Prisma.SortOrder;
    additionalDevices?: Prisma.SortOrder;
    amount?: Prisma.SortOrder;
    status?: Prisma.SortOrder;
    createdAt?: Prisma.SortOrder;
    processedAt?: Prisma.SortOrder;
};
export type DeviceExpansionSumOrderByAggregateInput = {
    additionalDevices?: Prisma.SortOrder;
    amount?: Prisma.SortOrder;
};
export type DeviceExpansionCreateNestedManyWithoutPaymentIntentInput = {
    create?: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutPaymentIntentInput, Prisma.DeviceExpansionUncheckedCreateWithoutPaymentIntentInput> | Prisma.DeviceExpansionCreateWithoutPaymentIntentInput[] | Prisma.DeviceExpansionUncheckedCreateWithoutPaymentIntentInput[];
    connectOrCreate?: Prisma.DeviceExpansionCreateOrConnectWithoutPaymentIntentInput | Prisma.DeviceExpansionCreateOrConnectWithoutPaymentIntentInput[];
    createMany?: Prisma.DeviceExpansionCreateManyPaymentIntentInputEnvelope;
    connect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
};
export type DeviceExpansionUncheckedCreateNestedManyWithoutPaymentIntentInput = {
    create?: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutPaymentIntentInput, Prisma.DeviceExpansionUncheckedCreateWithoutPaymentIntentInput> | Prisma.DeviceExpansionCreateWithoutPaymentIntentInput[] | Prisma.DeviceExpansionUncheckedCreateWithoutPaymentIntentInput[];
    connectOrCreate?: Prisma.DeviceExpansionCreateOrConnectWithoutPaymentIntentInput | Prisma.DeviceExpansionCreateOrConnectWithoutPaymentIntentInput[];
    createMany?: Prisma.DeviceExpansionCreateManyPaymentIntentInputEnvelope;
    connect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
};
export type DeviceExpansionUpdateManyWithoutPaymentIntentNestedInput = {
    create?: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutPaymentIntentInput, Prisma.DeviceExpansionUncheckedCreateWithoutPaymentIntentInput> | Prisma.DeviceExpansionCreateWithoutPaymentIntentInput[] | Prisma.DeviceExpansionUncheckedCreateWithoutPaymentIntentInput[];
    connectOrCreate?: Prisma.DeviceExpansionCreateOrConnectWithoutPaymentIntentInput | Prisma.DeviceExpansionCreateOrConnectWithoutPaymentIntentInput[];
    upsert?: Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInput | Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInput[];
    createMany?: Prisma.DeviceExpansionCreateManyPaymentIntentInputEnvelope;
    set?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    disconnect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    delete?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    connect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    update?: Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInput | Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInput[];
    updateMany?: Prisma.DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInput | Prisma.DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInput[];
    deleteMany?: Prisma.DeviceExpansionScalarWhereInput | Prisma.DeviceExpansionScalarWhereInput[];
};
export type DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentNestedInput = {
    create?: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutPaymentIntentInput, Prisma.DeviceExpansionUncheckedCreateWithoutPaymentIntentInput> | Prisma.DeviceExpansionCreateWithoutPaymentIntentInput[] | Prisma.DeviceExpansionUncheckedCreateWithoutPaymentIntentInput[];
    connectOrCreate?: Prisma.DeviceExpansionCreateOrConnectWithoutPaymentIntentInput | Prisma.DeviceExpansionCreateOrConnectWithoutPaymentIntentInput[];
    upsert?: Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInput | Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInput[];
    createMany?: Prisma.DeviceExpansionCreateManyPaymentIntentInputEnvelope;
    set?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    disconnect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    delete?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    connect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    update?: Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInput | Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInput[];
    updateMany?: Prisma.DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInput | Prisma.DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInput[];
    deleteMany?: Prisma.DeviceExpansionScalarWhereInput | Prisma.DeviceExpansionScalarWhereInput[];
};
export type DeviceExpansionCreateNestedManyWithoutLicenseInput = {
    create?: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutLicenseInput, Prisma.DeviceExpansionUncheckedCreateWithoutLicenseInput> | Prisma.DeviceExpansionCreateWithoutLicenseInput[] | Prisma.DeviceExpansionUncheckedCreateWithoutLicenseInput[];
    connectOrCreate?: Prisma.DeviceExpansionCreateOrConnectWithoutLicenseInput | Prisma.DeviceExpansionCreateOrConnectWithoutLicenseInput[];
    createMany?: Prisma.DeviceExpansionCreateManyLicenseInputEnvelope;
    connect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
};
export type DeviceExpansionUncheckedCreateNestedManyWithoutLicenseInput = {
    create?: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutLicenseInput, Prisma.DeviceExpansionUncheckedCreateWithoutLicenseInput> | Prisma.DeviceExpansionCreateWithoutLicenseInput[] | Prisma.DeviceExpansionUncheckedCreateWithoutLicenseInput[];
    connectOrCreate?: Prisma.DeviceExpansionCreateOrConnectWithoutLicenseInput | Prisma.DeviceExpansionCreateOrConnectWithoutLicenseInput[];
    createMany?: Prisma.DeviceExpansionCreateManyLicenseInputEnvelope;
    connect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
};
export type DeviceExpansionUpdateManyWithoutLicenseNestedInput = {
    create?: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutLicenseInput, Prisma.DeviceExpansionUncheckedCreateWithoutLicenseInput> | Prisma.DeviceExpansionCreateWithoutLicenseInput[] | Prisma.DeviceExpansionUncheckedCreateWithoutLicenseInput[];
    connectOrCreate?: Prisma.DeviceExpansionCreateOrConnectWithoutLicenseInput | Prisma.DeviceExpansionCreateOrConnectWithoutLicenseInput[];
    upsert?: Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInput | Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInput[];
    createMany?: Prisma.DeviceExpansionCreateManyLicenseInputEnvelope;
    set?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    disconnect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    delete?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    connect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    update?: Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInput | Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInput[];
    updateMany?: Prisma.DeviceExpansionUpdateManyWithWhereWithoutLicenseInput | Prisma.DeviceExpansionUpdateManyWithWhereWithoutLicenseInput[];
    deleteMany?: Prisma.DeviceExpansionScalarWhereInput | Prisma.DeviceExpansionScalarWhereInput[];
};
export type DeviceExpansionUncheckedUpdateManyWithoutLicenseNestedInput = {
    create?: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutLicenseInput, Prisma.DeviceExpansionUncheckedCreateWithoutLicenseInput> | Prisma.DeviceExpansionCreateWithoutLicenseInput[] | Prisma.DeviceExpansionUncheckedCreateWithoutLicenseInput[];
    connectOrCreate?: Prisma.DeviceExpansionCreateOrConnectWithoutLicenseInput | Prisma.DeviceExpansionCreateOrConnectWithoutLicenseInput[];
    upsert?: Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInput | Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInput[];
    createMany?: Prisma.DeviceExpansionCreateManyLicenseInputEnvelope;
    set?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    disconnect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    delete?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    connect?: Prisma.DeviceExpansionWhereUniqueInput | Prisma.DeviceExpansionWhereUniqueInput[];
    update?: Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInput | Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInput[];
    updateMany?: Prisma.DeviceExpansionUpdateManyWithWhereWithoutLicenseInput | Prisma.DeviceExpansionUpdateManyWithWhereWithoutLicenseInput[];
    deleteMany?: Prisma.DeviceExpansionScalarWhereInput | Prisma.DeviceExpansionScalarWhereInput[];
};
export type EnumDeviceExpansionStatusFieldUpdateOperationsInput = {
    set?: $Enums.DeviceExpansionStatus;
};
export type DeviceExpansionCreateWithoutPaymentIntentInput = {
    id?: string;
    additionalDevices: number;
    amount: number;
    status?: $Enums.DeviceExpansionStatus;
    createdAt?: Date | string;
    processedAt?: Date | string | null;
    license: Prisma.LicenseCreateNestedOneWithoutDeviceExpansionsInput;
};
export type DeviceExpansionUncheckedCreateWithoutPaymentIntentInput = {
    id?: string;
    licenseId: string;
    additionalDevices: number;
    amount: number;
    status?: $Enums.DeviceExpansionStatus;
    createdAt?: Date | string;
    processedAt?: Date | string | null;
};
export type DeviceExpansionCreateOrConnectWithoutPaymentIntentInput = {
    where: Prisma.DeviceExpansionWhereUniqueInput;
    create: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutPaymentIntentInput, Prisma.DeviceExpansionUncheckedCreateWithoutPaymentIntentInput>;
};
export type DeviceExpansionCreateManyPaymentIntentInputEnvelope = {
    data: Prisma.DeviceExpansionCreateManyPaymentIntentInput | Prisma.DeviceExpansionCreateManyPaymentIntentInput[];
    skipDuplicates?: boolean;
};
export type DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInput = {
    where: Prisma.DeviceExpansionWhereUniqueInput;
    update: Prisma.XOR<Prisma.DeviceExpansionUpdateWithoutPaymentIntentInput, Prisma.DeviceExpansionUncheckedUpdateWithoutPaymentIntentInput>;
    create: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutPaymentIntentInput, Prisma.DeviceExpansionUncheckedCreateWithoutPaymentIntentInput>;
};
export type DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInput = {
    where: Prisma.DeviceExpansionWhereUniqueInput;
    data: Prisma.XOR<Prisma.DeviceExpansionUpdateWithoutPaymentIntentInput, Prisma.DeviceExpansionUncheckedUpdateWithoutPaymentIntentInput>;
};
export type DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInput = {
    where: Prisma.DeviceExpansionScalarWhereInput;
    data: Prisma.XOR<Prisma.DeviceExpansionUpdateManyMutationInput, Prisma.DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentInput>;
};
export type DeviceExpansionScalarWhereInput = {
    AND?: Prisma.DeviceExpansionScalarWhereInput | Prisma.DeviceExpansionScalarWhereInput[];
    OR?: Prisma.DeviceExpansionScalarWhereInput[];
    NOT?: Prisma.DeviceExpansionScalarWhereInput | Prisma.DeviceExpansionScalarWhereInput[];
    id?: Prisma.StringFilter<"DeviceExpansion"> | string;
    licenseId?: Prisma.StringFilter<"DeviceExpansion"> | string;
    paymentIntentId?: Prisma.StringFilter<"DeviceExpansion"> | string;
    additionalDevices?: Prisma.IntFilter<"DeviceExpansion"> | number;
    amount?: Prisma.IntFilter<"DeviceExpansion"> | number;
    status?: Prisma.EnumDeviceExpansionStatusFilter<"DeviceExpansion"> | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFilter<"DeviceExpansion"> | Date | string;
    processedAt?: Prisma.DateTimeNullableFilter<"DeviceExpansion"> | Date | string | null;
};
export type DeviceExpansionCreateWithoutLicenseInput = {
    id?: string;
    additionalDevices: number;
    amount: number;
    status?: $Enums.DeviceExpansionStatus;
    createdAt?: Date | string;
    processedAt?: Date | string | null;
    paymentIntent: Prisma.PaymentIntentCreateNestedOneWithoutDeviceExpansionsInput;
};
export type DeviceExpansionUncheckedCreateWithoutLicenseInput = {
    id?: string;
    paymentIntentId: string;
    additionalDevices: number;
    amount: number;
    status?: $Enums.DeviceExpansionStatus;
    createdAt?: Date | string;
    processedAt?: Date | string | null;
};
export type DeviceExpansionCreateOrConnectWithoutLicenseInput = {
    where: Prisma.DeviceExpansionWhereUniqueInput;
    create: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutLicenseInput, Prisma.DeviceExpansionUncheckedCreateWithoutLicenseInput>;
};
export type DeviceExpansionCreateManyLicenseInputEnvelope = {
    data: Prisma.DeviceExpansionCreateManyLicenseInput | Prisma.DeviceExpansionCreateManyLicenseInput[];
    skipDuplicates?: boolean;
};
export type DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInput = {
    where: Prisma.DeviceExpansionWhereUniqueInput;
    update: Prisma.XOR<Prisma.DeviceExpansionUpdateWithoutLicenseInput, Prisma.DeviceExpansionUncheckedUpdateWithoutLicenseInput>;
    create: Prisma.XOR<Prisma.DeviceExpansionCreateWithoutLicenseInput, Prisma.DeviceExpansionUncheckedCreateWithoutLicenseInput>;
};
export type DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInput = {
    where: Prisma.DeviceExpansionWhereUniqueInput;
    data: Prisma.XOR<Prisma.DeviceExpansionUpdateWithoutLicenseInput, Prisma.DeviceExpansionUncheckedUpdateWithoutLicenseInput>;
};
export type DeviceExpansionUpdateManyWithWhereWithoutLicenseInput = {
    where: Prisma.DeviceExpansionScalarWhereInput;
    data: Prisma.XOR<Prisma.DeviceExpansionUpdateManyMutationInput, Prisma.DeviceExpansionUncheckedUpdateManyWithoutLicenseInput>;
};
export type DeviceExpansionCreateManyPaymentIntentInput = {
    id?: string;
    licenseId: string;
    additionalDevices: number;
    amount: number;
    status?: $Enums.DeviceExpansionStatus;
    createdAt?: Date | string;
    processedAt?: Date | string | null;
};
export type DeviceExpansionUpdateWithoutPaymentIntentInput = {
    id?: Prisma.StringFieldUpdateOperationsInput | string;
    additionalDevices?: Prisma.IntFieldUpdateOperationsInput | number;
    amount?: Prisma.IntFieldUpdateOperationsInput | number;
    status?: Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
    processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null;
    license?: Prisma.LicenseUpdateOneRequiredWithoutDeviceExpansionsNestedInput;
};
export type DeviceExpansionUncheckedUpdateWithoutPaymentIntentInput = {
    id?: Prisma.StringFieldUpdateOperationsInput | string;
    licenseId?: Prisma.StringFieldUpdateOperationsInput | string;
    additionalDevices?: Prisma.IntFieldUpdateOperationsInput | number;
    amount?: Prisma.IntFieldUpdateOperationsInput | number;
    status?: Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
    processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null;
};
export type DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentInput = {
    id?: Prisma.StringFieldUpdateOperationsInput | string;
    licenseId?: Prisma.StringFieldUpdateOperationsInput | string;
    additionalDevices?: Prisma.IntFieldUpdateOperationsInput | number;
    amount?: Prisma.IntFieldUpdateOperationsInput | number;
    status?: Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
    processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null;
};
export type DeviceExpansionCreateManyLicenseInput = {
    id?: string;
    paymentIntentId: string;
    additionalDevices: number;
    amount: number;
    status?: $Enums.DeviceExpansionStatus;
    createdAt?: Date | string;
    processedAt?: Date | string | null;
};
export type DeviceExpansionUpdateWithoutLicenseInput = {
    id?: Prisma.StringFieldUpdateOperationsInput | string;
    additionalDevices?: Prisma.IntFieldUpdateOperationsInput | number;
    amount?: Prisma.IntFieldUpdateOperationsInput | number;
    status?: Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
    processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null;
    paymentIntent?: Prisma.PaymentIntentUpdateOneRequiredWithoutDeviceExpansionsNestedInput;
};
export type DeviceExpansionUncheckedUpdateWithoutLicenseInput = {
    id?: Prisma.StringFieldUpdateOperationsInput | string;
    paymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string;
    additionalDevices?: Prisma.IntFieldUpdateOperationsInput | number;
    amount?: Prisma.IntFieldUpdateOperationsInput | number;
    status?: Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
    processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null;
};
export type DeviceExpansionUncheckedUpdateManyWithoutLicenseInput = {
    id?: Prisma.StringFieldUpdateOperationsInput | string;
    paymentIntentId?: Prisma.StringFieldUpdateOperationsInput | string;
    additionalDevices?: Prisma.IntFieldUpdateOperationsInput | number;
    amount?: Prisma.IntFieldUpdateOperationsInput | number;
    status?: Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput | $Enums.DeviceExpansionStatus;
    createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string;
    processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null;
};
export type DeviceExpansionSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
    id?: boolean;
    licenseId?: boolean;
    paymentIntentId?: boolean;
    additionalDevices?: boolean;
    amount?: boolean;
    status?: boolean;
    createdAt?: boolean;
    processedAt?: boolean;
    license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>;
    paymentIntent?: boolean | Prisma.PaymentIntentDefaultArgs<ExtArgs>;
}, ExtArgs["result"]["deviceExpansion"]>;
export type DeviceExpansionSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
    id?: boolean;
    licenseId?: boolean;
    paymentIntentId?: boolean;
    additionalDevices?: boolean;
    amount?: boolean;
    status?: boolean;
    createdAt?: boolean;
    processedAt?: boolean;
    license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>;
    paymentIntent?: boolean | Prisma.PaymentIntentDefaultArgs<ExtArgs>;
}, ExtArgs["result"]["deviceExpansion"]>;
export type DeviceExpansionSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
    id?: boolean;
    licenseId?: boolean;
    paymentIntentId?: boolean;
    additionalDevices?: boolean;
    amount?: boolean;
    status?: boolean;
    createdAt?: boolean;
    processedAt?: boolean;
    license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>;
    paymentIntent?: boolean | Prisma.PaymentIntentDefaultArgs<ExtArgs>;
}, ExtArgs["result"]["deviceExpansion"]>;
export type DeviceExpansionSelectScalar = {
    id?: boolean;
    licenseId?: boolean;
    paymentIntentId?: boolean;
    additionalDevices?: boolean;
    amount?: boolean;
    status?: boolean;
    createdAt?: boolean;
    processedAt?: boolean;
};
export type DeviceExpansionOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "licenseId" | "paymentIntentId" | "additionalDevices" | "amount" | "status" | "createdAt" | "processedAt", ExtArgs["result"]["deviceExpansion"]>;
export type DeviceExpansionInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>;
    paymentIntent?: boolean | Prisma.PaymentIntentDefaultArgs<ExtArgs>;
};
export type DeviceExpansionIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>;
    paymentIntent?: boolean | Prisma.PaymentIntentDefaultArgs<ExtArgs>;
};
export type DeviceExpansionIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>;
    paymentIntent?: boolean | Prisma.PaymentIntentDefaultArgs<ExtArgs>;
};
export type $DeviceExpansionPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    name: "DeviceExpansion";
    objects: {
        license: Prisma.$LicensePayload<ExtArgs>;
        paymentIntent: Prisma.$PaymentIntentPayload<ExtArgs>;
    };
    scalars: runtime.Types.Extensions.GetPayloadResult<{
        id: string;
        licenseId: string;
        paymentIntentId: string;
        additionalDevices: number;
        amount: number;
        status: $Enums.DeviceExpansionStatus;
        createdAt: Date;
        processedAt: Date | null;
    }, ExtArgs["result"]["deviceExpansion"]>;
    composites: {};
};
export type DeviceExpansionGetPayload<S extends boolean | null | undefined | DeviceExpansionDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload, S>;
export type DeviceExpansionCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = Omit<DeviceExpansionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: DeviceExpansionCountAggregateInputType | true;
};
export interface DeviceExpansionDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: {
        types: Prisma.TypeMap<ExtArgs>['model']['DeviceExpansion'];
        meta: {
            name: 'DeviceExpansion';
        };
    };
    /**
     * Find zero or one DeviceExpansion that matches the filter.
     * @param {DeviceExpansionFindUniqueArgs} args - Arguments to find a DeviceExpansion
     * @example
     * // Get one DeviceExpansion
     * const deviceExpansion = await prisma.deviceExpansion.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends DeviceExpansionFindUniqueArgs>(args: Prisma.SelectSubset<T, DeviceExpansionFindUniqueArgs<ExtArgs>>): Prisma.Prisma__DeviceExpansionClient<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>;
    /**
     * Find one DeviceExpansion that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {DeviceExpansionFindUniqueOrThrowArgs} args - Arguments to find a DeviceExpansion
     * @example
     * // Get one DeviceExpansion
     * const deviceExpansion = await prisma.deviceExpansion.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends DeviceExpansionFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, DeviceExpansionFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__DeviceExpansionClient<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>;
    /**
     * Find the first DeviceExpansion that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DeviceExpansionFindFirstArgs} args - Arguments to find a DeviceExpansion
     * @example
     * // Get one DeviceExpansion
     * const deviceExpansion = await prisma.deviceExpansion.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends DeviceExpansionFindFirstArgs>(args?: Prisma.SelectSubset<T, DeviceExpansionFindFirstArgs<ExtArgs>>): Prisma.Prisma__DeviceExpansionClient<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>;
    /**
     * Find the first DeviceExpansion that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DeviceExpansionFindFirstOrThrowArgs} args - Arguments to find a DeviceExpansion
     * @example
     * // Get one DeviceExpansion
     * const deviceExpansion = await prisma.deviceExpansion.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends DeviceExpansionFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, DeviceExpansionFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__DeviceExpansionClient<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>;
    /**
     * Find zero or more DeviceExpansions that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DeviceExpansionFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all DeviceExpansions
     * const deviceExpansions = await prisma.deviceExpansion.findMany()
     *
     * // Get first 10 DeviceExpansions
     * const deviceExpansions = await prisma.deviceExpansion.findMany({ take: 10 })
     *
     * // Only select the `id`
     * const deviceExpansionWithIdOnly = await prisma.deviceExpansion.findMany({ select: { id: true } })
     *
     */
    findMany<T extends DeviceExpansionFindManyArgs>(args?: Prisma.SelectSubset<T, DeviceExpansionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>;
    /**
     * Create a DeviceExpansion.
     * @param {DeviceExpansionCreateArgs} args - Arguments to create a DeviceExpansion.
     * @example
     * // Create one DeviceExpansion
     * const DeviceExpansion = await prisma.deviceExpansion.create({
     *   data: {
     *     // ... data to create a DeviceExpansion
     *   }
     * })
     *
     */
    create<T extends DeviceExpansionCreateArgs>(args: Prisma.SelectSubset<T, DeviceExpansionCreateArgs<ExtArgs>>): Prisma.Prisma__DeviceExpansionClient<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>;
    /**
     * Create many DeviceExpansions.
     * @param {DeviceExpansionCreateManyArgs} args - Arguments to create many DeviceExpansions.
     * @example
     * // Create many DeviceExpansions
     * const deviceExpansion = await prisma.deviceExpansion.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *
     */
    createMany<T extends DeviceExpansionCreateManyArgs>(args?: Prisma.SelectSubset<T, DeviceExpansionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>;
    /**
     * Create many DeviceExpansions and returns the data saved in the database.
     * @param {DeviceExpansionCreateManyAndReturnArgs} args - Arguments to create many DeviceExpansions.
     * @example
     * // Create many DeviceExpansions
     * const deviceExpansion = await prisma.deviceExpansion.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *
     * // Create many DeviceExpansions and only return the `id`
     * const deviceExpansionWithIdOnly = await prisma.deviceExpansion.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     *
     */
    createManyAndReturn<T extends DeviceExpansionCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, DeviceExpansionCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>;
    /**
     * Delete a DeviceExpansion.
     * @param {DeviceExpansionDeleteArgs} args - Arguments to delete one DeviceExpansion.
     * @example
     * // Delete one DeviceExpansion
     * const DeviceExpansion = await prisma.deviceExpansion.delete({
     *   where: {
     *     // ... filter to delete one DeviceExpansion
     *   }
     * })
     *
     */
    delete<T extends DeviceExpansionDeleteArgs>(args: Prisma.SelectSubset<T, DeviceExpansionDeleteArgs<ExtArgs>>): Prisma.Prisma__DeviceExpansionClient<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>;
    /**
     * Update one DeviceExpansion.
     * @param {DeviceExpansionUpdateArgs} args - Arguments to update one DeviceExpansion.
     * @example
     * // Update one DeviceExpansion
     * const deviceExpansion = await prisma.deviceExpansion.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     *
     */
    update<T extends DeviceExpansionUpdateArgs>(args: Prisma.SelectSubset<T, DeviceExpansionUpdateArgs<ExtArgs>>): Prisma.Prisma__DeviceExpansionClient<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>;
    /**
     * Delete zero or more DeviceExpansions.
     * @param {DeviceExpansionDeleteManyArgs} args - Arguments to filter DeviceExpansions to delete.
     * @example
     * // Delete a few DeviceExpansions
     * const { count } = await prisma.deviceExpansion.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     *
     */
    deleteMany<T extends DeviceExpansionDeleteManyArgs>(args?: Prisma.SelectSubset<T, DeviceExpansionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>;
    /**
     * Update zero or more DeviceExpansions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DeviceExpansionUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many DeviceExpansions
     * const deviceExpansion = await prisma.deviceExpansion.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     *
     */
    updateMany<T extends DeviceExpansionUpdateManyArgs>(args: Prisma.SelectSubset<T, DeviceExpansionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>;
    /**
     * Update zero or more DeviceExpansions and returns the data updated in the database.
     * @param {DeviceExpansionUpdateManyAndReturnArgs} args - Arguments to update many DeviceExpansions.
     * @example
     * // Update many DeviceExpansions
     * const deviceExpansion = await prisma.deviceExpansion.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *
     * // Update zero or more DeviceExpansions and only return the `id`
     * const deviceExpansionWithIdOnly = await prisma.deviceExpansion.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     *
     */
    updateManyAndReturn<T extends DeviceExpansionUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, DeviceExpansionUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>;
    /**
     * Create or update one DeviceExpansion.
     * @param {DeviceExpansionUpsertArgs} args - Arguments to update or create a DeviceExpansion.
     * @example
     * // Update or create a DeviceExpansion
     * const deviceExpansion = await prisma.deviceExpansion.upsert({
     *   create: {
     *     // ... data to create a DeviceExpansion
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the DeviceExpansion we want to update
     *   }
     * })
     */
    upsert<T extends DeviceExpansionUpsertArgs>(args: Prisma.SelectSubset<T, DeviceExpansionUpsertArgs<ExtArgs>>): Prisma.Prisma__DeviceExpansionClient<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>;
    /**
     * Count the number of DeviceExpansions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DeviceExpansionCountArgs} args - Arguments to filter DeviceExpansions to count.
     * @example
     * // Count the number of DeviceExpansions
     * const count = await prisma.deviceExpansion.count({
     *   where: {
     *     // ... the filter for the DeviceExpansions we want to count
     *   }
     * })
    **/
    count<T extends DeviceExpansionCountArgs>(args?: Prisma.Subset<T, DeviceExpansionCountArgs>): Prisma.PrismaPromise<T extends runtime.Types.Utils.Record<'select', any> ? T['select'] extends true ? number : Prisma.GetScalarType<T['select'], DeviceExpansionCountAggregateOutputType> : number>;
    /**
     * Allows you to perform aggregations operations on a DeviceExpansion.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DeviceExpansionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends DeviceExpansionAggregateArgs>(args: Prisma.Subset<T, DeviceExpansionAggregateArgs>): Prisma.PrismaPromise<GetDeviceExpansionAggregateType<T>>;
    /**
     * Group by DeviceExpansion.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DeviceExpansionGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     *
    **/
    groupBy<T extends DeviceExpansionGroupByArgs, HasSelectOrTake extends Prisma.Or<Prisma.Extends<'skip', Prisma.Keys<T>>, Prisma.Extends<'take', Prisma.Keys<T>>>, OrderByArg extends Prisma.True extends HasSelectOrTake ? {
        orderBy: DeviceExpansionGroupByArgs['orderBy'];
    } : {
        orderBy?: DeviceExpansionGroupByArgs['orderBy'];
    }, OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>, ByFields extends Prisma.MaybeTupleToUnion<T['by']>, ByValid extends Prisma.Has<ByFields, OrderFields>, HavingFields extends Prisma.GetHavingFields<T['having']>, HavingValid extends Prisma.Has<ByFields, HavingFields>, ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False, InputErrors extends ByEmpty extends Prisma.True ? `Error: "by" must not be empty.` : HavingValid extends Prisma.False ? {
        [P in HavingFields]: P extends ByFields ? never : P extends string ? `Error: Field "${P}" used in "having" needs to be provided in "by".` : [
            Error,
            'Field ',
            P,
            ` in "having" needs to be provided in "by"`
        ];
    }[HavingFields] : 'take' extends Prisma.Keys<T> ? 'orderBy' extends Prisma.Keys<T> ? ByValid extends Prisma.True ? {} : {
        [P in OrderFields]: P extends ByFields ? never : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`;
    }[OrderFields] : 'Error: If you provide "take", you also need to provide "orderBy"' : 'skip' extends Prisma.Keys<T> ? 'orderBy' extends Prisma.Keys<T> ? ByValid extends Prisma.True ? {} : {
        [P in OrderFields]: P extends ByFields ? never : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`;
    }[OrderFields] : 'Error: If you provide "skip", you also need to provide "orderBy"' : ByValid extends Prisma.True ? {} : {
        [P in OrderFields]: P extends ByFields ? never : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`;
    }[OrderFields]>(args: Prisma.SubsetIntersection<T, DeviceExpansionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetDeviceExpansionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>;
    /**
     * Fields of the DeviceExpansion model
     */
    readonly fields: DeviceExpansionFieldRefs;
}
/**
 * The delegate class that acts as a "Promise-like" for DeviceExpansion.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__DeviceExpansionClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise";
    license<T extends Prisma.LicenseDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.LicenseDefaultArgs<ExtArgs>>): Prisma.Prisma__LicenseClient<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>;
    paymentIntent<T extends Prisma.PaymentIntentDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.PaymentIntentDefaultArgs<ExtArgs>>): Prisma.Prisma__PaymentIntentClient<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>;
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>;
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>;
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>;
}
/**
 * Fields of the DeviceExpansion model
 */
export interface DeviceExpansionFieldRefs {
    readonly id: Prisma.FieldRef<"DeviceExpansion", 'String'>;
    readonly licenseId: Prisma.FieldRef<"DeviceExpansion", 'String'>;
    readonly paymentIntentId: Prisma.FieldRef<"DeviceExpansion", 'String'>;
    readonly additionalDevices: Prisma.FieldRef<"DeviceExpansion", 'Int'>;
    readonly amount: Prisma.FieldRef<"DeviceExpansion", 'Int'>;
    readonly status: Prisma.FieldRef<"DeviceExpansion", 'DeviceExpansionStatus'>;
    readonly createdAt: Prisma.FieldRef<"DeviceExpansion", 'DateTime'>;
    readonly processedAt: Prisma.FieldRef<"DeviceExpansion", 'DateTime'>;
}
/**
 * DeviceExpansion findUnique
 */
export type DeviceExpansionFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelect<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionInclude<ExtArgs> | null;
    /**
     * Filter, which DeviceExpansion to fetch.
     */
    where: Prisma.DeviceExpansionWhereUniqueInput;
};
/**
 * DeviceExpansion findUniqueOrThrow
 */
export type DeviceExpansionFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelect<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionInclude<ExtArgs> | null;
    /**
     * Filter, which DeviceExpansion to fetch.
     */
    where: Prisma.DeviceExpansionWhereUniqueInput;
};
/**
 * DeviceExpansion findFirst
 */
export type DeviceExpansionFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelect<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionInclude<ExtArgs> | null;
    /**
     * Filter, which DeviceExpansion to fetch.
     */
    where?: Prisma.DeviceExpansionWhereInput;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     *
     * Determine the order of DeviceExpansions to fetch.
     */
    orderBy?: Prisma.DeviceExpansionOrderByWithRelationInput | Prisma.DeviceExpansionOrderByWithRelationInput[];
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     *
     * Sets the position for searching for DeviceExpansions.
     */
    cursor?: Prisma.DeviceExpansionWhereUniqueInput;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     *
     * Take `±n` DeviceExpansions from the position of the cursor.
     */
    take?: number;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     *
     * Skip the first `n` DeviceExpansions.
     */
    skip?: number;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     *
     * Filter by unique combinations of DeviceExpansions.
     */
    distinct?: Prisma.DeviceExpansionScalarFieldEnum | Prisma.DeviceExpansionScalarFieldEnum[];
};
/**
 * DeviceExpansion findFirstOrThrow
 */
export type DeviceExpansionFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelect<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionInclude<ExtArgs> | null;
    /**
     * Filter, which DeviceExpansion to fetch.
     */
    where?: Prisma.DeviceExpansionWhereInput;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     *
     * Determine the order of DeviceExpansions to fetch.
     */
    orderBy?: Prisma.DeviceExpansionOrderByWithRelationInput | Prisma.DeviceExpansionOrderByWithRelationInput[];
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     *
     * Sets the position for searching for DeviceExpansions.
     */
    cursor?: Prisma.DeviceExpansionWhereUniqueInput;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     *
     * Take `±n` DeviceExpansions from the position of the cursor.
     */
    take?: number;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     *
     * Skip the first `n` DeviceExpansions.
     */
    skip?: number;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     *
     * Filter by unique combinations of DeviceExpansions.
     */
    distinct?: Prisma.DeviceExpansionScalarFieldEnum | Prisma.DeviceExpansionScalarFieldEnum[];
};
/**
 * DeviceExpansion findMany
 */
export type DeviceExpansionFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelect<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionInclude<ExtArgs> | null;
    /**
     * Filter, which DeviceExpansions to fetch.
     */
    where?: Prisma.DeviceExpansionWhereInput;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     *
     * Determine the order of DeviceExpansions to fetch.
     */
    orderBy?: Prisma.DeviceExpansionOrderByWithRelationInput | Prisma.DeviceExpansionOrderByWithRelationInput[];
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     *
     * Sets the position for listing DeviceExpansions.
     */
    cursor?: Prisma.DeviceExpansionWhereUniqueInput;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     *
     * Take `±n` DeviceExpansions from the position of the cursor.
     */
    take?: number;
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     *
     * Skip the first `n` DeviceExpansions.
     */
    skip?: number;
    distinct?: Prisma.DeviceExpansionScalarFieldEnum | Prisma.DeviceExpansionScalarFieldEnum[];
};
/**
 * DeviceExpansion create
 */
export type DeviceExpansionCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelect<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionInclude<ExtArgs> | null;
    /**
     * The data needed to create a DeviceExpansion.
     */
    data: Prisma.XOR<Prisma.DeviceExpansionCreateInput, Prisma.DeviceExpansionUncheckedCreateInput>;
};
/**
 * DeviceExpansion createMany
 */
export type DeviceExpansionCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * The data used to create many DeviceExpansions.
     */
    data: Prisma.DeviceExpansionCreateManyInput | Prisma.DeviceExpansionCreateManyInput[];
    skipDuplicates?: boolean;
};
/**
 * DeviceExpansion createManyAndReturn
 */
export type DeviceExpansionCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelectCreateManyAndReturn<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * The data used to create many DeviceExpansions.
     */
    data: Prisma.DeviceExpansionCreateManyInput | Prisma.DeviceExpansionCreateManyInput[];
    skipDuplicates?: boolean;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionIncludeCreateManyAndReturn<ExtArgs> | null;
};
/**
 * DeviceExpansion update
 */
export type DeviceExpansionUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelect<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionInclude<ExtArgs> | null;
    /**
     * The data needed to update a DeviceExpansion.
     */
    data: Prisma.XOR<Prisma.DeviceExpansionUpdateInput, Prisma.DeviceExpansionUncheckedUpdateInput>;
    /**
     * Choose, which DeviceExpansion to update.
     */
    where: Prisma.DeviceExpansionWhereUniqueInput;
};
/**
 * DeviceExpansion updateMany
 */
export type DeviceExpansionUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * The data used to update DeviceExpansions.
     */
    data: Prisma.XOR<Prisma.DeviceExpansionUpdateManyMutationInput, Prisma.DeviceExpansionUncheckedUpdateManyInput>;
    /**
     * Filter which DeviceExpansions to update
     */
    where?: Prisma.DeviceExpansionWhereInput;
    /**
     * Limit how many DeviceExpansions to update.
     */
    limit?: number;
};
/**
 * DeviceExpansion updateManyAndReturn
 */
export type DeviceExpansionUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelectUpdateManyAndReturn<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * The data used to update DeviceExpansions.
     */
    data: Prisma.XOR<Prisma.DeviceExpansionUpdateManyMutationInput, Prisma.DeviceExpansionUncheckedUpdateManyInput>;
    /**
     * Filter which DeviceExpansions to update
     */
    where?: Prisma.DeviceExpansionWhereInput;
    /**
     * Limit how many DeviceExpansions to update.
     */
    limit?: number;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionIncludeUpdateManyAndReturn<ExtArgs> | null;
};
/**
 * DeviceExpansion upsert
 */
export type DeviceExpansionUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelect<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionInclude<ExtArgs> | null;
    /**
     * The filter to search for the DeviceExpansion to update in case it exists.
     */
    where: Prisma.DeviceExpansionWhereUniqueInput;
    /**
     * In case the DeviceExpansion found by the `where` argument doesn't exist, create a new DeviceExpansion with this data.
     */
    create: Prisma.XOR<Prisma.DeviceExpansionCreateInput, Prisma.DeviceExpansionUncheckedCreateInput>;
    /**
     * In case the DeviceExpansion was found with the provided `where` argument, update it with this data.
     */
    update: Prisma.XOR<Prisma.DeviceExpansionUpdateInput, Prisma.DeviceExpansionUncheckedUpdateInput>;
};
/**
 * DeviceExpansion delete
 */
export type DeviceExpansionDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelect<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionInclude<ExtArgs> | null;
    /**
     * Filter which DeviceExpansion to delete.
     */
    where: Prisma.DeviceExpansionWhereUniqueInput;
};
/**
 * DeviceExpansion deleteMany
 */
export type DeviceExpansionDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Filter which DeviceExpansions to delete
     */
    where?: Prisma.DeviceExpansionWhereInput;
    /**
     * Limit how many DeviceExpansions to delete.
     */
    limit?: number;
};
/**
 * DeviceExpansion without action
 */
export type DeviceExpansionDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DeviceExpansion
     */
    select?: Prisma.DeviceExpansionSelect<ExtArgs> | null;
    /**
     * Omit specific fields from the DeviceExpansion
     */
    omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null;
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: Prisma.DeviceExpansionInclude<ExtArgs> | null;
};
export {};
