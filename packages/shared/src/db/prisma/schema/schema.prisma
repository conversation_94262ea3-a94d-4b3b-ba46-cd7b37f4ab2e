generator client {
  provider     = "prisma-client"
  output       = "../generated"
  moduleFormat = "esm"
}

// generator zod {
//   provider = "prisma-zod-generator"
//   output   = "../generated/zod"
// }

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

// ============================================================================
// PAYMENT & BILLING
// ============================================================================

model PaymentIntent {
  id                      String        @id @default(cuid())
  stripePaymentIntentId   String        @unique
  stripeCheckoutSessionId String?       @unique
  amount                  Int // Amount in cents
  currency                String        @default("usd")
  status                  PaymentStatus @default(PENDING)
  paymentType             PaymentType

  // Customer info
  customerEmail String
  customerName  String?

  // Metadata
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  processedAt DateTime?

  // Relationships
  licenses         License[]
  deviceExpansions DeviceExpansion[]
  webhookEvents    WebhookEvent[]

  @@index([stripePaymentIntentId])
  @@index([customerEmail])
  @@index([status])
  @@index([paymentType])
  @@index([createdAt])
  @@map("payment_intents")
}

model WebhookEvent {
  id            String    @id @default(cuid())
  stripeEventId String    @unique
  eventType     String
  processed     Boolean   @default(false)
  processedAt   DateTime?
  errorMessage  String?
  retryCount    Int       @default(0)
  createdAt     DateTime  @default(now())

  // Relationships
  paymentIntentId String?
  paymentIntent   PaymentIntent? @relation(fields: [paymentIntentId], references: [id])

  @@index([stripeEventId])
  @@index([eventType])
  @@index([processed])
  @@index([createdAt])
  @@map("webhook_events")
}

// ============================================================================
// LICENSE MANAGEMENT
// ============================================================================

model License {
  id          String        @id @default(cuid())
  licenseKey  String        @unique
  licenseType LicenseType
  status      LicenseStatus @default(ACTIVE)

  // Device limits
  maxDevices  Int @default(2)
  usedDevices Int @default(0) // Denormalized for performance

  // Dates
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  expiresAt   DateTime?
  activatedAt DateTime? // When first device was registered

  // Customer information (no account needed - these are app users)
  customerEmail String // Email where license was sent
  customerName  String? // Name from payment, if provided

  // Creation tracking (which admin user created this license)
  createdBy     String?
  createdByUser User?   @relation("LicenseCreatedBy", fields: [createdBy], references: [id])

  // Payment tracking
  paymentIntentId String?
  paymentIntent   PaymentIntent? @relation(fields: [paymentIntentId], references: [id])
  totalPaidAmount Int            @default(0) // Total amount paid for this license in cents

  // Refund tracking
  refundedAt   DateTime?
  refundReason String?
  refundAmount Int? // Amount refunded in cents

  // License delivery tracking
  emailSentAt         DateTime? // When license was emailed to customer
  emailDeliveryStatus String? // 'sent', 'delivered', 'failed', etc.

  // Relationships
  devices          Device[]
  deviceExpansions DeviceExpansion[]
  refundRequests   RefundRequest[]

  @@index([licenseKey])
  @@index([customerEmail])
  @@index([licenseType])
  @@index([status])
  @@index([expiresAt])
  @@index([createdAt])
  @@index([emailDeliveryStatus])
  @@map("licenses")
}

model Device {
  id         String       @id @default(cuid())
  licenseId  String
  deviceHash String // Hashed device ID for security
  salt       String // Salt used for hashing
  status     DeviceStatus @default(ACTIVE)

  // Timestamps
  firstSeen DateTime  @default(now())
  lastSeen  DateTime  @default(now())
  removedAt DateTime?

  // App information
  appVersion String?

  // Device metadata (for better UX)
  deviceName       String?
  deviceType       String?
  deviceModel      String?
  operatingSystem  String?
  architecture     String?
  screenResolution String?
  totalMemory      String?

  // User-provided metadata
  userNickname String?
  location     String?
  notes        String?

  // Relationships
  license License @relation(fields: [licenseId], references: [id], onDelete: Cascade)

  @@unique([licenseId, deviceHash])
  @@index([licenseId])
  @@index([deviceHash])
  @@index([status])
  @@index([lastSeen])
  @@map("devices")
}

model DeviceExpansion {
  id                String                @id @default(cuid())
  licenseId         String
  paymentIntentId   String
  additionalDevices Int
  amount            Int // Amount paid in cents
  status            DeviceExpansionStatus @default(PENDING)

  // Timestamps
  createdAt   DateTime  @default(now())
  processedAt DateTime?

  // Relationships
  license       License       @relation(fields: [licenseId], references: [id], onDelete: Cascade)
  paymentIntent PaymentIntent @relation(fields: [paymentIntentId], references: [id])

  @@index([licenseId])
  @@index([paymentIntentId])
  @@index([status])
  @@index([createdAt])
  @@map("device_expansions")
}

// ============================================================================
// REFUND MANAGEMENT
// ============================================================================

model RefundRequest {
  id          String       @id @default(cuid())
  licenseId   String
  requestedBy String // Email of customer requesting refund
  reason      String
  status      RefundStatus @default(PENDING)

  // Financial details
  requestedAmount Int? // Requested refund amount in cents
  approvedAmount  Int? // Approved refund amount in cents
  stripeRefundIds String[] @default([])

  // Processing details
  adminNotes      String?
  processedBy     String? // Admin user ID who processed this
  processedByUser User?   @relation("RefundProcessedBy", fields: [processedBy], references: [id])

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  processedAt DateTime?

  // Relationships
  license License @relation(fields: [licenseId], references: [id], onDelete: Cascade)

  @@index([licenseId])
  @@index([status])
  @@index([requestedBy]) // To find refunds by customer email
  @@index([createdAt])
  @@index([processedBy])
  @@map("refund_requests")
}

// ============================================================================
// AUDIT & MONITORING
// ============================================================================

model AuditLog {
  id     String      @id @default(cuid())
  action AuditAction

  // Context
  licenseKey String?
  deviceHash String?
  licenseId  String?
  deviceId   String?

  // Actor information (admin users performing actions)
  actorId    String? // Admin user who performed the action
  actor      User?   @relation("AuditLogActor", fields: [actorId], references: [id])
  actorEmail String? // For cases where admin user doesn't exist yet

  // Target information (customer/license affected by action)
  targetId      String? // Another admin user affected by the action (for user management)
  target        User?   @relation("AuditLogTarget", fields: [targetId], references: [id])
  customerEmail String? // Customer email affected by the action

  // Request metadata
  ipAddress String?
  userAgent String?

  // Additional data
  details Json?

  // Timestamps
  createdAt DateTime @default(now())

  @@index([licenseKey])
  @@index([licenseId])
  @@index([actorId])
  @@index([targetId])
  @@index([customerEmail])
  @@index([action])
  @@index([createdAt])
  @@index([ipAddress]) // For security monitoring
  @@map("audit_logs")
}

model RateLimit {
  id          String   @id @default(cuid())
  identifier  String // IP address, user ID, or license key
  action      String // Type of action being rate limited
  count       Int      @default(1)
  windowStart DateTime @default(now())
  expiresAt   DateTime

  @@unique([identifier, action])
  @@index([identifier])
  @@index([expiresAt])
  @@map("rate_limits")
}

// ============================================================================
// SUPPORT SYSTEM
// ============================================================================

model SupportTicket {
  id          String          @id @default(cuid())
  ticketId    String          @unique // Human-readable ticket ID (e.g., "SNAP-2024-001")
  subject     String
  description String
  status      TicketStatus    @default(OPEN)
  priority    TicketPriority  @default(MEDIUM)
  category    TicketCategory?

  // Customer information
  customerEmail String
  customerName  String?
  licenseKey    String? // Optional - if ticket relates to specific license

  // Assignment
  assignedTo     String? // Admin user ID
  assignedToUser User?   @relation("TicketAssignedTo", fields: [assignedTo], references: [id])

  // Timestamps
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  resolvedAt DateTime?

  // Relationships
  messages SupportMessage[]

  @@index([customerEmail])
  @@index([status])
  @@index([assignedTo])
  @@index([createdAt])
  @@index([ticketId])
  @@map("support_tickets")
}

model SupportMessage {
  id       String        @id @default(cuid())
  ticketId String
  ticket   SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)

  message    String
  isInternal Boolean @default(false) // Internal admin notes vs customer-visible

  // Author information
  authorEmail String? // Customer email
  authorId    String? // Admin user ID
  authorUser  User?   @relation("MessageAuthor", fields: [authorId], references: [id])

  createdAt DateTime @default(now())

  @@index([ticketId])
  @@index([createdAt])
  @@map("support_messages")
}

// ============================================================================
// ENUMS
// ============================================================================

enum LicenseType {
  TRIAL
  PRO
  ENTERPRISE // Future-proofing
}

enum LicenseStatus {
  ACTIVE
  EXPIRED
  SUSPENDED
  REFUNDED
  CANCELLED
}

enum DeviceStatus {
  ACTIVE
  INACTIVE
  REMOVED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  SUCCEEDED
  FAILED
  CANCELLED
  REFUNDED
}

enum PaymentType {
  LICENSE_PURCHASE
  DEVICE_EXPANSION
  SUBSCRIPTION // Future-proofing
}

enum DeviceExpansionStatus {
  PENDING
  PROCESSED
  FAILED
}

enum RefundStatus {
  PENDING
  APPROVED
  REJECTED
  PROCESSED
  FAILED
}

enum AuditAction {
  // License actions
  LICENSE_CREATED
  TRIAL_LICENSE_CREATED
  LICENSE_ACTIVATED
  LICENSE_VALIDATED
  LICENSE_EXPIRED
  LICENSE_SUSPENDED
  LICENSE_REACTIVATED
  LICENSE_DELETED
  LICENSE_EXTENDED
  LICENSE_CANCELLED
  LICENSE_UPGRADED

  // Device actions
  DEVICE_REGISTERED
  DEVICE_UPDATED
  DEVICE_REMOVED
  DEVICE_EXPANSION_PURCHASED
  DEVICE_EXPANSION_PROCESSED

  // Payment actions
  PAYMENT_INITIATED
  PAYMENT_SUCCEEDED
  PAYMENT_FAILED
  PAYMENT_REFUNDED

  // Refund actions
  REFUND_REQUESTED
  REFUND_APPROVED
  REFUND_REJECTED
  REFUND_PROCESSED
  REFUND_FAILED
  REFUND_UPDATED

  // User management actions
  USER_CREATED
  USER_UPDATED
  USER_ROLE_CHANGED
  USER_ACTIVATED
  USER_DEACTIVATED
  USER_SUSPENDED
  USER_DELETED
  USER_LOGIN
  USER_LOGOUT
  USER_EMAIL_VERIFIED
  FIRST_USER_ADMIN_GRANTED

  // Invitation actions
  INVITATION_SENT
  INVITATION_ACCEPTED
  INVITATION_EXPIRED
  INVITATION_REVOKED
  INVITATION_RESENT
  INVITATION_UPDATED
  INVITATION_CANCELLED
  USER_CREATED_FROM_INVITATION

  // Security actions
  SUSPICIOUS_ACTIVITY_DETECTED
  RATE_LIMIT_EXCEEDED
  UNAUTHORIZED_ACCESS_ATTEMPT
  MULTIPLE_DEVICE_LIMIT_EXCEEDED

  // System actions
  WEBHOOK_PROCESSED
  WEBHOOK_FAILED
  DATA_EXPORT_REQUESTED
  DATA_DELETED

  // Email & Communication actions
  EMAIL_SENT
  BULK_EMAIL_SENT
  EMAIL_DELIVERY_FAILED
  EMAIL_TEMPLATE_UPDATED

  // Notification actions
  NOTIFICATION_CREATED
  NOTIFICATIONS_MARKED_READ
  NOTIFICATION_SETTINGS_UPDATED

  // Support actions
  SUPPORT_TICKET_CREATED
  SUPPORT_TICKET_UPDATED
  SUPPORT_TICKET_RESOLVED
  SUPPORT_MESSAGE_SENT
}

enum TicketStatus {
  OPEN
  IN_PROGRESS
  WAITING_CUSTOMER
  RESOLVED
  CLOSED
}

enum TicketCategory {
  LICENSE
  BILLING
  TECHNICAL
  GENERAL
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}
