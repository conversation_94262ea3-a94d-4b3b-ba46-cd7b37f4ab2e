/**
 * Shared Package for SnapBack App
 *
 * This package provides API-specific types, validation schemas, and utilities
 * that are shared across the monorepo. Database model types are auto-generated
 * by Prisma and should be imported directly from the server app.
 */

// API utilities and response helpers
export * from "./api";
// Prisma client for database operations
export { PrismaClient } from "./db/prisma/generated/client";
// Prisma enum types and constants
export * from "./db/prisma/generated/enums";
// API validation schemas
export * from "./schemas";
// API types and interfaces
export * from "./types";
