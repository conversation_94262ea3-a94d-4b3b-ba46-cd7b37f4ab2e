import { z } from "zod";

import {
	DeviceExpansionStatus,
	DeviceStatus,
	InvitationStatus,
	LicenseStatus,
	LicenseType,
	PaymentStatus,
	PaymentType,
	RefundStatus,
	TicketCategory,
	TicketPriority,
	TicketStatus,
	UserRole,
} from "./db/prisma/generated/enums";

/**
 * API Validation Schemas
 *
 * This file contains Zod validation schemas for API requests, responses,
 * and business logic validation. Database model schemas are auto-generated
 * by Prisma and available in the server app.
 */

// ============================================================================
// COMMON VALIDATION PATTERNS
// ============================================================================

export const urlSchema = z.url("Valid success URL is required");
export const isoDateSchema = z.iso.datetime();
export const emailSchema = z
	.email("Invalid email address")
	.max(254, "Email must be at most 254 characters")
	.transform((val) => val.toLowerCase().trim());

export const licenseKeySchema = z
	.string()
	.length(24, "License key must be exactly 24 characters")
	.regex(
		/^[A-Z0-9]{24}$/,
		"License key must contain only uppercase letters and numbers",
	);

export const deviceIdSchema = z
	.string()
	.min(32, "Device ID must be at least 32 characters")
	.max(128, "Device ID must be at most 128 characters");

export const deviceMetadataSchema = z.object({
	deviceName: z.string().optional(),
	deviceType: z.string().optional(),
	deviceModel: z.string().optional(),
	operatingSystem: z.string().optional(),
	architecture: z.string().optional(),
	screenResolution: z.string().optional(),
	totalMemory: z.string().optional(),
	userNickname: z.string().optional(),
	location: z.string().optional(),
	notes: z.string().optional(),
});

// ============================================================================
// PAGINATION SCHEMAS
// ============================================================================

/**
 * Base pagination schema with just page and limit
 */
export const basePaginationSchema = z.object({
	page: z.number().int().min(1).default(1),
	limit: z.number().int().min(1).max(100).default(20),
});

/**
 * Standardized pagination schema used across all list endpoints
 *
 * Features:
 * - page: Page number (1-based, default: 1)
 * - limit: Items per page (1-100, default: 20)
 * - search: General search term (optional)
 * - sortBy: Field to sort by (optional)
 * - sortOrder: Sort direction "asc" or "desc" (default: "desc")
 * - filters: Flexible object for specific filters (optional)
 *
 * Example usage:
 * - Basic: ?page=2&limit=50
 * - With search: ?search=john&sortBy=createdAt&sortOrder=asc
 * - With filters: ?filters[status]=ACTIVE&filters[licenseType]=PRO
 */
export const paginationSchema = basePaginationSchema.extend({
	search: z.string().optional(),
	sortBy: z.string().optional(),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
	filters: z.record(z.string(), z.any()).optional(), // Flexible filters object
});

// ============================================================================
// API REQUEST SCHEMAS
// ============================================================================

// ============================================================================
// LICENSE VALIDATION SCHEMAS (matching Prisma License and Device models)
// ============================================================================

export const validateLicenseSchema = z.object({
	licenseKey: licenseKeySchema,
	deviceId: deviceIdSchema,
	appVersion: z.string().optional(),
	deviceMetadata: deviceMetadataSchema.optional(),
});

export const licenseLookupSchema = z.object({
	email: emailSchema,
});

export const licenseStatusSchema = z.object({
	licenseKey: licenseKeySchema,
});

export const licensePingSchema = z.object({
	licenseKey: licenseKeySchema,
	deviceId: deviceIdSchema,
});

// ============================================================================
// DEVICE SCHEMAS (matching Prisma Device model)
// ============================================================================

export const registerDeviceSchema = validateLicenseSchema; // Same schema, different purpose

export const updateDeviceSchema = deviceMetadataSchema;

export const deviceHeartbeatSchema = z.object({
	licenseKey: licenseKeySchema,
	deviceId: deviceIdSchema,
	appVersion: z.string().optional(),
});

export const deviceListSchema = paginationSchema; // Now uses standardized pagination with filters

// ============================================================================
// ADMIN SCHEMAS (matching Prisma User, License, and UserInvitation models)
// ============================================================================

export const createLicenseSchema = z.object({
	email: emailSchema,
	licenseType: z.enum(LicenseType),
	deviceId: deviceIdSchema.optional(),
	trialDurationDays: z.number().int().min(1).max(365).optional(),
});

export const updateLicenseSchema = z.object({
	status: z.enum(LicenseStatus).optional(),
	maxDevices: z.number().int().min(1).max(100).optional(),
	expiresAt: isoDateSchema.optional(),
	customerName: z.string().optional(),
});

export const createUserInvitationSchema = z.object({
	email: emailSchema,
	role: z.enum(UserRole),
	expiresInDays: z.number().int().min(1).max(30).optional(),
});

export const createUserSchema = z.object({
	name: z.string().min(1).max(100),
	email: emailSchema,
	role: z.enum(UserRole),
});

export const updateUserSchema = z.object({
	name: z.string().min(1).max(100).optional(),
	role: z.enum(UserRole).optional(),
	isActive: z.boolean().optional(),
});

// Enhanced License Management Schemas
export const extendLicenseSchema = z.object({
	extensionDays: z.number().int().min(1).max(365),
	reason: z.string().min(1).max(500).optional(),
});

export const suspendLicenseSchema = z.object({
	reason: z.string().min(1).max(500),
	suspendUntil: z.date().optional(),
});

export const reactivateLicenseSchema = z.object({
	reason: z.string().min(1).max(500).optional(),
});

export const resendLicenseEmailSchema = z.object({
	customMessage: z.string().max(1000).optional(),
});

// Device Management Schemas (Admin)
export const adminUpdateDeviceSchema = z.object({
	deviceName: z.string().min(1).max(100).optional(),
	userNickname: z.string().min(1).max(100).optional(),
	location: z.string().max(200).optional(),
	status: z.enum(["ACTIVE", "INACTIVE"]).optional(),
});

export const deviceExpansionSchema = z.object({
	licenseId: z.uuid(),
	additionalDevices: z.number().int().min(1).max(10),
	reason: z.string().min(1).max(500).optional(),
});

// Refund Management Schemas (Admin)
export const approveRefundSchema = z.object({
	reason: z.string().min(1).max(500).optional(),
	adminNotes: z.string().max(1000).optional(),
});

export const rejectRefundSchema = z.object({
	reason: z.string().min(1).max(500),
	adminNotes: z.string().max(1000).optional(),
});

export const processRefundSchema = z.object({
	amount: z.number().int().min(1).optional(), // Amount in cents, optional for full refund
	reason: z.string().min(1).max(500).optional(),
});

export const adminUpdateRefundSchema = z.object({
	adminNotes: z.string().max(1000).optional(),
	internalStatus: z.string().max(100).optional(),
});

export const adminPaginationSchema = paginationSchema; // Now uses standardized pagination with filters

// ============================================================================
// PUBLIC API SCHEMAS (for public-facing endpoints)
// ============================================================================

export const pricingSchema = z.object({
	currency: z.enum(["usd", "eur", "gbp"]).default("usd"),
});

export const validatePurchaseSchema = z.object({
	licenseType: z.enum(LicenseType),
	customerEmail: emailSchema,
	customerName: z.string().optional(),
});

export const contactSchema = z.object({
	name: z.string().min(1, "Name is required"),
	email: emailSchema,
	subject: z.string().min(1, "Subject is required"),
	message: z.string().min(10, "Message must be at least 10 characters"),
	licenseKey: licenseKeySchema.optional(),
});

// ============================================================================
// REFUND SCHEMAS (matching Prisma RefundRequest model)
// ============================================================================

export const requestRefundSchema = z.object({
	licenseKey: licenseKeySchema,
	reason: z.string().min(10, "Reason must be at least 10 characters"),
	requestedAmount: z.number().int().min(0).optional(), // Amount in cents
});

export const updateRefundSchema = z.object({
	status: z.enum(RefundStatus).optional(),
	adminNotes: z.string().optional(),
	approvedAmount: z.number().int().min(0).optional(), // Amount in cents
});

export const refundListSchema = paginationSchema; // Now uses standardized pagination with filters

// ============================================================================
// SYSTEM/UTILITY SCHEMAS (for system endpoints and utilities)
// ============================================================================

export const validateEmailSchema = z.object({
	email: emailSchema,
});

export const generateLicenseKeySchema = z.object({
	count: z.number().int().min(1).max(10).default(1),
});

// ============================================================================
// PAYMENT SCHEMAS (matching Prisma PaymentIntent model exactly)
// ============================================================================

export const createCheckoutSessionSchema = z.object({
	licenseType: z.enum(LicenseType), // TRIAL licenses are created differently
	customerEmail: emailSchema,
	customerName: z.string().optional(),
	successUrl: urlSchema,
	cancelUrl: urlSchema,
});

export const createExpansionCheckoutSchema = z.object({
	licenseId: z.string().min(1, "License ID is required"),
	additionalDevices: z.number().int().min(1).max(50),
	successUrl: urlSchema,
	cancelUrl: urlSchema,
});

export const createTierUpgradeSchema = z.object({
	licenseId: z.string().min(1, "License ID is required"),
	targetTier: z.enum(["PRO", "ENTERPRISE"]),
	successUrl: urlSchema,
	cancelUrl: urlSchema,
});

// ============================================================================
// ANALYTICS & REPORTING SCHEMAS
// ============================================================================

// Dashboard stats schema
export const dashboardStatsSchema = z.object({
	period: z.enum(["7d", "30d", "90d", "1y"]).default("30d"),
});

// Revenue analytics schema
export const revenueAnalyticsSchema = z.object({
	period: z.enum(["monthly", "yearly"]).default("monthly"),
	year: z.number().int().min(2020).max(2030).optional(),
});

// License analytics schema
export const licenseAnalyticsSchema = z.object({
	period: z.enum(["7d", "30d", "90d", "1y"]).default("30d"),
	groupBy: z.enum(["type", "status", "date"]).default("type"),
});

// Device analytics schema
export const deviceAnalyticsSchema = z.object({
	period: z.enum(["7d", "30d", "90d", "1y"]).default("30d"),
	groupBy: z.enum(["registrations", "active", "date"]).default("registrations"),
});

// Report export schema
export const reportExportSchema = z.object({
	format: z.enum(["csv", "json"]).default("csv"),
	startDate: z.string().optional(),
	endDate: z.string().optional(),
	filters: z.record(z.string(), z.any()).optional(),
});

// Device expansion purchase schema
export const purchaseDeviceExpansionSchema = z.object({
	licenseKey: licenseKeySchema,
	additionalDevices: z.number().int().min(1).max(10),
});

// Process device expansion schema (admin)
export const processDeviceExpansionSchema = z.object({
	approve: z.boolean(),
	adminNotes: z.string().max(1000).optional(),
});

// User reactivation schema
export const reactivateUserSchema = z.object({
	reason: z.string().min(1).max(500).optional(),
});

// Accept invitation schema
export const acceptInvitationSchema = z.object({
	token: z.string().min(1),
	name: z.string().min(1).max(100),
});

// Resend invitation schema
export const resendInvitationSchema = z.object({
	customMessage: z.string().max(1000).optional(),
});

// Update invitation schema
export const updateInvitationSchema = z.object({
	role: z.enum(UserRole).optional(),
	expiresAt: isoDateSchema.optional(),
	notes: z.string().max(500).optional(),
});

// ============================================================================
// EMAIL & NOTIFICATIONS SCHEMAS
// ============================================================================

// Email template schema
export const emailTemplateSchema = z.object({
	name: z.string().min(1),
	subject: z.string().min(1),
	htmlContent: z.string().min(1),
	textContent: z.string().optional(),
	variables: z.array(z.string()).optional(),
});

// Bulk email schema
export const bulkEmailSchema = z.object({
	templateId: z.string().optional(),
	subject: z.string().min(1),
	htmlContent: z.string().min(1),
	textContent: z.string().optional(),
	recipients: z.array(emailSchema).min(1).max(1000),
	sendAt: isoDateSchema.optional(),
	variables: z.record(z.string(), z.string()).optional(),
});

// Email delivery stats schema
export const emailDeliveryStatsSchema = z.object({
	startDate: isoDateSchema.optional(),
	endDate: isoDateSchema.optional(),
	templateId: z.string().optional(),
});

// Notification schema
export const notificationSchema = z.object({
	type: z.enum(["INFO", "WARNING", "ERROR", "SUCCESS"]),
	title: z.string().min(1),
	message: z.string().min(1),
	userId: z.string().optional(),
	metadata: z.record(z.string(), z.any()).optional(),
});

// Mark notifications as read schema
export const markNotificationsReadSchema = z.object({
	notificationIds: z.array(z.string()).min(1),
});

// Notification settings schema
export const notificationSettingsSchema = z.object({
	emailNotifications: z.boolean().default(true),
	pushNotifications: z.boolean().default(true),
	smsNotifications: z.boolean().default(false),
	licenseExpiry: z.boolean().default(true),
	paymentUpdates: z.boolean().default(true),
	systemAlerts: z.boolean().default(true),
	marketingEmails: z.boolean().default(false),
});

// ============================================================================
// WEBHOOK MANAGEMENT SCHEMAS
// ============================================================================

// Webhook events list schema
export const webhookEventsSchema = z.object({
	eventType: z.string().optional(),
	processed: z.boolean().optional(),
	startDate: isoDateSchema.optional(),
	endDate: isoDateSchema.optional(),
});

// Retry webhook schema
export const retryWebhookSchema = z.object({
	reason: z.string().min(1).max(500).optional(),
});

// Webhook stats schema
export const webhookStatsSchema = z.object({
	period: z.enum(["24h", "7d", "30d", "90d"]).default("7d"),
	eventType: z.string().optional(),
});

// ============================================================================
// SUPPORT SYSTEM SCHEMAS
// ============================================================================

export const createSupportTicketSchema = z.object({
	subject: z
		.string()
		.min(1, "Subject is required")
		.max(200, "Subject too long"),
	description: z
		.string()
		.min(10, "Description must be at least 10 characters")
		.max(5000, "Description too long"),
	category: z.enum(TicketCategory).optional(),
	customerEmail: emailSchema,
	customerName: z.string().min(1, "Name is required").max(100, "Name too long"),
	licenseKey: licenseKeySchema.optional(), // Optional - if ticket relates to specific license
});

export const supportTicketResponseSchema = z.object({
	id: z.string(),
	ticketId: z.string(),
	subject: z.string(),
	description: z.string(),
	status: z.enum(	TicketStatus),
	priority: z.enum(TicketPriority),
	category: z.string().nullable(),
	customerEmail: z.string(),
	customerName: z.string().nullable(),
	licenseKey: z.string().nullable(),
	createdAt: z.string(),
	updatedAt: z.string(),
});

export const faqDataSchema = z.object({
	categories: z.array(
		z.object({
			id: z.string(),
			name: z.string(),
			description: z.string().optional(),
			questions: z.array(
				z.object({
					id: z.string(),
					question: z.string(),
					answer: z.string(),
					helpful: z.number().optional(),
					tags: z.array(z.string()).optional(),
				}),
			),
		}),
	),
	lastUpdated: z.string(),
});
